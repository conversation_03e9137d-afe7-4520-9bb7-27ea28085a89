import 'package:flutter/material.dart';
import 'medication.dart';

class MedicationReminder {
  final String id;
  final String medicationId;
  final DateTime scheduledTime;
  final ReminderStatus status;
  final DateTime? takenAt;
  final String? notes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MedicationReminder({
    required this.id,
    required this.medicationId,
    required this.scheduledTime,
    required this.status,
    this.takenAt,
    this.notes,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'medicationId': medicationId,
      'scheduledTime': scheduledTime.toIso8601String(),
      'status': status.name,
      'takenAt': takenAt?.toIso8601String(),
      'notes': notes,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory MedicationReminder.fromJson(Map<String, dynamic> json) {
    return MedicationReminder(
      id: json['id'] as String,
      medicationId: json['medicationId'] as String,
      scheduledTime: DateTime.parse(json['scheduledTime'] as String),
      status: ReminderStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ReminderStatus.pending,
      ),
      takenAt: json['takenAt'] != null
          ? DateTime.parse(json['takenAt'] as String)
          : null,
      notes: json['notes'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  MedicationReminder copyWith({
    String? id,
    String? medicationId,
    DateTime? scheduledTime,
    ReminderStatus? status,
    DateTime? takenAt,
    String? notes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MedicationReminder(
      id: id ?? this.id,
      medicationId: medicationId ?? this.medicationId,
      scheduledTime: scheduledTime ?? this.scheduledTime,
      status: status ?? this.status,
      takenAt: takenAt ?? this.takenAt,
      notes: notes ?? this.notes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isOverdue {
    if (status == ReminderStatus.taken || status == ReminderStatus.skipped) {
      return false;
    }
    return DateTime.now().isAfter(scheduledTime);
  }

  bool get isDueToday {
    final now = DateTime.now();
    final scheduled = scheduledTime;
    return now.year == scheduled.year &&
        now.month == scheduled.month &&
        now.day == scheduled.day;
  }

  bool get isDueSoon {
    final now = DateTime.now();
    final timeDifference = scheduledTime.difference(now);
    return timeDifference.inMinutes <= 30 && timeDifference.inMinutes > 0;
  }

  String get statusDisplay {
    switch (status) {
      case ReminderStatus.pending:
        return 'Pending';
      case ReminderStatus.taken:
        return 'Taken';
      case ReminderStatus.missed:
        return 'Missed';
      case ReminderStatus.skipped:
        return 'Skipped';
    }
  }

  Color get statusColor {
    switch (status) {
      case ReminderStatus.pending:
        return isOverdue ? Colors.red : Colors.orange;
      case ReminderStatus.taken:
        return Colors.green;
      case ReminderStatus.missed:
        return Colors.red;
      case ReminderStatus.skipped:
        return Colors.grey;
    }
  }

  IconData get statusIcon {
    switch (status) {
      case ReminderStatus.pending:
        return isOverdue ? Icons.warning : Icons.schedule;
      case ReminderStatus.taken:
        return Icons.check_circle;
      case ReminderStatus.missed:
        return Icons.error;
      case ReminderStatus.skipped:
        return Icons.cancel;
    }
  }
}

class MedicationAdherence {
  final String medicationId;
  final DateTime date;
  final int totalDoses;
  final int takenDoses;
  final int missedDoses;
  final int skippedDoses;

  const MedicationAdherence({
    required this.medicationId,
    required this.date,
    required this.totalDoses,
    required this.takenDoses,
    required this.missedDoses,
    required this.skippedDoses,
  });

  double get adherencePercentage {
    if (totalDoses == 0) return 0.0;
    return (takenDoses / totalDoses) * 100;
  }

  Map<String, dynamic> toJson() {
    return {
      'medicationId': medicationId,
      'date': date.toIso8601String(),
      'totalDoses': totalDoses,
      'takenDoses': takenDoses,
      'missedDoses': missedDoses,
      'skippedDoses': skippedDoses,
    };
  }

  factory MedicationAdherence.fromJson(Map<String, dynamic> json) {
    return MedicationAdherence(
      medicationId: json['medicationId'] as String,
      date: DateTime.parse(json['date'] as String),
      totalDoses: json['totalDoses'] as int,
      takenDoses: json['takenDoses'] as int,
      missedDoses: json['missedDoses'] as int,
      skippedDoses: json['skippedDoses'] as int,
    );
  }
}

class MedicationStats {
  final int totalMedications;
  final int activeMedications;
  final int todayReminders;
  final int completedToday;
  final int missedToday;
  final double overallAdherence;

  const MedicationStats({
    required this.totalMedications,
    required this.activeMedications,
    required this.todayReminders,
    required this.completedToday,
    required this.missedToday,
    required this.overallAdherence,
  });

  int get pendingToday => todayReminders - completedToday - missedToday;

  double get todayAdherence {
    if (todayReminders == 0) return 100.0;
    return (completedToday / todayReminders) * 100;
  }
}
