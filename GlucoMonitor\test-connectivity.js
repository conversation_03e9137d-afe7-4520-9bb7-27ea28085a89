const http = require('http');

// Test connectivity to the backend
function testConnectivity() {
    const options = {
        hostname: '127.0.0.1',
        port: 5000,
        path: '/api/health',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json'
        },
        timeout: 5000
    };

    console.log('Testing connectivity to backend...');
    console.log(`URL: http://${options.hostname}:${options.port}${options.path}`);

    const req = http.request(options, (res) => {
        console.log(`Status Code: ${res.statusCode}`);
        console.log(`Headers:`, res.headers);

        let data = '';
        res.on('data', (chunk) => {
            data += chunk;
        });

        res.on('end', () => {
            console.log('Response Body:', data);
            if (res.statusCode === 200) {
                console.log('✅ Backend is accessible!');
            } else {
                console.log('❌ Backend returned error status');
            }
        });
    });

    req.on('error', (error) => {
        console.log('❌ Connection failed:', error.message);
    });

    req.on('timeout', () => {
        console.log('❌ Connection timed out');
        req.destroy();
    });

    req.end();
}

testConnectivity();
