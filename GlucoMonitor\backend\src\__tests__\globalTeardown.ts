/**
 * Global teardown for Jest tests
 * Runs once after all test suites
 */

export default async function globalTeardown() {
  // Get the mongod instance from global setup
  const mongod = (global as any).__MONGOD__;
  
  if (mongod) {
    // Stop the in-memory MongoDB instance
    await mongod.stop();
    console.log('Test MongoDB instance stopped');
  }
  
  console.log('Global test teardown completed');
};
