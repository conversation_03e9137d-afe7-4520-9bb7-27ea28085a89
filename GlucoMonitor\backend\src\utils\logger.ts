import { createLogger, format, transports, Logger } from 'winston';
import { env } from '../config/env';
import path from 'path';
import fs from 'fs';

// Ensure logs directory exists
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

// Custom log levels
const logLevels = {
    error: 0,
    warn: 1,
    info: 2,
    http: 3,
    debug: 4,
    trace: 5
};

// Custom colors for log levels
const logColors = {
    error: 'red',
    warn: 'yellow',
    info: 'green',
    http: 'magenta',
    debug: 'blue',
    trace: 'cyan'
};

// Custom format for console output
const consoleFormat = format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.errors({ stack: true }),
    format.colorize({ all: true }),
    format.printf(({ timestamp, level, message, stack, ...meta }) => {
        let log = `${timestamp} [${level}]: ${message}`;
        
        // Add metadata if present
        if (Object.keys(meta).length > 0) {
            log += ` ${JSON.stringify(meta, null, 2)}`;
        }
        
        // Add stack trace for errors
        if (stack) {
            log += `\n${stack}`;
        }
        
        return log;
    })
);

// Custom format for file output
const fileFormat = format.combine(
    format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    format.errors({ stack: true }),
    format.json()
);

// Create the logger instance
const logger: Logger = createLogger({
    levels: logLevels,
    level: env.NODE_ENV === 'production' ? 'info' : 'debug',
    format: fileFormat,
    defaultMeta: {
        service: 'glucomonitor-backend',
        environment: env.NODE_ENV,
        version: process.env.npm_package_version || '1.0.0'
    },
    transports: [
        // Console transport for development
        new transports.Console({
            format: consoleFormat,
            level: env.NODE_ENV === 'production' ? 'warn' : 'debug'
        }),
        
        // File transport for all logs
        new transports.File({
            filename: path.join(logsDir, 'app.log'),
            level: 'info',
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true
        }),
        
        // Separate file for errors
        new transports.File({
            filename: path.join(logsDir, 'error.log'),
            level: 'error',
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 5,
            tailable: true
        }),
        
        // HTTP requests log
        new transports.File({
            filename: path.join(logsDir, 'http.log'),
            level: 'http',
            maxsize: 10 * 1024 * 1024, // 10MB
            maxFiles: 3,
            tailable: true
        })
    ],
    
    // Handle uncaught exceptions and rejections
    exceptionHandlers: [
        new transports.File({
            filename: path.join(logsDir, 'exceptions.log'),
            maxsize: 10 * 1024 * 1024,
            maxFiles: 3
        })
    ],
    
    rejectionHandlers: [
        new transports.File({
            filename: path.join(logsDir, 'rejections.log'),
            maxsize: 10 * 1024 * 1024,
            maxFiles: 3
        })
    ],
    
    exitOnError: false
});

// Add colors to winston
require('winston').addColors(logColors);

// Enhanced logging interface
interface LogContext {
    userId?: string;
    requestId?: string;
    action?: string;
    resource?: string;
    ip?: string;
    userAgent?: string;
    duration?: number;
    statusCode?: number;
    method?: string;
    url?: string;
    [key: string]: any;
}

class EnhancedLogger {
    private logger: Logger;

    constructor(logger: Logger) {
        this.logger = logger;
    }

    // Standard log methods
    error(message: string, context?: LogContext): void {
        this.logger.error(message, context);
    }

    warn(message: string, context?: LogContext): void {
        this.logger.warn(message, context);
    }

    info(message: string, context?: LogContext): void {
        this.logger.info(message, context);
    }

    http(message: string, context?: LogContext): void {
        this.logger.http(message, context);
    }

    debug(message: string, context?: LogContext): void {
        this.logger.debug(message, context);
    }

    trace(message: string, context?: LogContext): void {
        this.logger.log('trace', message, context);
    }

    // Specialized logging methods
    database(action: string, context?: LogContext): void {
        this.logger.info(`Database: ${action}`, { ...context, category: 'database' });
    }

    auth(action: string, context?: LogContext): void {
        this.logger.info(`Auth: ${action}`, { ...context, category: 'authentication' });
    }

    api(method: string, url: string, statusCode: number, duration: number, context?: LogContext): void {
        const level = statusCode >= 400 ? 'warn' : 'http';
        this.logger.log(level, `API: ${method} ${url} ${statusCode}`, {
            ...context,
            method,
            url,
            statusCode,
            duration,
            category: 'api'
        });
    }

    security(event: string, context?: LogContext): void {
        this.logger.warn(`Security: ${event}`, { ...context, category: 'security' });
    }

    performance(metric: string, value: number, context?: LogContext): void {
        this.logger.info(`Performance: ${metric} = ${value}ms`, {
            ...context,
            metric,
            value,
            category: 'performance'
        });
    }

    // Error logging with stack trace
    exception(error: Error, context?: LogContext): void {
        this.logger.error(`Exception: ${error.message}`, {
            ...context,
            stack: error.stack,
            name: error.name,
            category: 'exception'
        });
    }

    // Business logic logging
    business(event: string, context?: LogContext): void {
        this.logger.info(`Business: ${event}`, { ...context, category: 'business' });
    }

    // System health logging
    health(component: string, status: 'healthy' | 'unhealthy', context?: LogContext): void {
        const level = status === 'healthy' ? 'info' : 'error';
        this.logger.log(level, `Health: ${component} is ${status}`, {
            ...context,
            component,
            status,
            category: 'health'
        });
    }

    // Create child logger with default context
    child(defaultContext: LogContext): EnhancedLogger {
        const childLogger = this.logger.child(defaultContext);
        return new EnhancedLogger(childLogger);
    }
}

// Create enhanced logger instance
const enhancedLogger = new EnhancedLogger(logger);

// Request ID generator
export const generateRequestId = (): string => {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};

// Log rotation cleanup (run daily)
const cleanupOldLogs = (): void => {
    const maxAge = 30 * 24 * 60 * 60 * 1000; // 30 days
    const now = Date.now();

    try {
        const files = fs.readdirSync(logsDir);
        files.forEach(file => {
            const filePath = path.join(logsDir, file);
            const stats = fs.statSync(filePath);
            
            if (now - stats.mtime.getTime() > maxAge) {
                fs.unlinkSync(filePath);
                enhancedLogger.info(`Cleaned up old log file: ${file}`);
            }
        });
    } catch (error) {
        enhancedLogger.error('Failed to cleanup old logs', { error: (error as Error).message });
    }
};

// Schedule daily cleanup
setInterval(cleanupOldLogs, 24 * 60 * 60 * 1000); // Run daily

export default enhancedLogger;
export type { LogContext, EnhancedLogger };
