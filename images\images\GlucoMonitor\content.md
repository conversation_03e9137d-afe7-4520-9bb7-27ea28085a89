﻿GlucoMonitor App Structure (MVP)
1. Onboarding Flow
Screens:
Welcome Screen
Language Selection
POPIA Consent Screen (with checkbox)
Features Overview (brief descriptions of glucose tracking, reminders, emergency alerts, etc.)
Account Creation/Login
2. Dashboard
Overview of:
Latest Glucose Reading
Medication Reminders
Quick Add Buttons (Log Glucose, Log Food, Log Activity)
Emergency Access
3. Glucose Tracking
Manual input of glucose levels
Optional note/tag per entry
History chart with basic trends (weekly view)
4. Medication Management
Add medication name, dosage, and times
Set reminders
Track adherence history
5. Food Diary
Add meals/snacks with timestamps
Select from local food database (SA staples like pap, chakalaka, etc.)
Carb count display per meal
6. Exercise Tracking
Manual input of activity type, duration, and notes
7. Emergency Screen
One-tap SMS alerts to preloaded emergency contacts or clinics
Share GPS location
Show load-shedding insulin storage tips
8. Reports & Insights
Simple graphs:
Glucose trends
Medication adherence
Weekly summary notifications
9. Settings
Language Selection
Manage POPIA Consent
Sync/Backup Options
Help & Contact Info
10. Offline-First Features
Log glucose, food, meds offline
Use local storage (Firestore offline)
SMS fallbacks for alerts


Context.md (Detailed MVP Context for GlucoMonitor)
🎯 Goal
GlucoMonitor is a mobile app built to help South African users—especially those in urban and semi-rural areas—manage diabetes effectively. It focuses on accessibility, cultural relevance, legal compliance, and offline usability.
👥 Target Audience
Diabetic individuals in urban and semi-rural areas
Age group: 18–65
Device: Low- to mid-range Android phones (e.g., Redmi Go, Galaxy A04)
Connectivity: Limited or unreliable internet
🏥 Health Context
Based on South African Diabetes Guidelines:
Promote lifestyle changes, self-monitoring, and teamwork
Supports emergency understanding:
HHS, insulin reactions, etc.
Cultural sensitivity:
Includes SA food database
Recognizes use of traditional medicine (e.g., muthi)
⚖️ Legal & Ethical Context
POPIA Compliance:
User consent on onboarding
Data encryption
Revocable consent stored with timestamp
Not a certified medical device (no diagnosis, just tracking)
Local ethical alignment (e.g., traditional medicine logging with insulin warnings)
🛠 Technical Context
Prioritize Android platform
Flutter frontend for cross-platform support
Supabase for auth
Node.js + Docker backend
Twilio for SMS alerts
📶 Offline and Accessibility Considerations
Firestore offline persistence
Voice navigation (for low literacy)
Multilingual UI: isiZulu, Afrikaans, Sesotho, English
SMS-based glucose logging
🔐 Privacy & Security
Encrypted health data
Two-factor authentication (SMS-based)
No cloud sync without consent
🧠 Key MVP Questions
How do SA users currently track meds?
Which offline features are most critical?
What features help low-literacy users?
How to keep users engaged without replacing clinics?

Let me know if you want this structure exported to Notion, Markdown file, or a Figma flow!

