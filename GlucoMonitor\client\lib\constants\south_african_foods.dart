import '../models/food_entry.dart';

/// South African traditional food data
class SAFood {
  final String name;
  final String nameZulu;
  final String nameAfrikaans;
  final String nameSesotho;
  final double caloriesPer100g;
  final double carbsPer100g;
  final double proteinPer100g;
  final double fatPer100g;
  final double fiberPer100g;
  final GlycemicIndex glycemicIndex;
  final FoodCategory category;
  final String description;
  final List<String> commonNames;

  const SAFood({
    required this.name,
    required this.nameZulu,
    required this.nameAfrikaans,
    required this.nameSesotho,
    required this.caloriesPer100g,
    required this.carbsPer100g,
    required this.proteinPer100g,
    required this.fatPer100g,
    required this.fiberPer100g,
    required this.glycemicIndex,
    required this.category,
    required this.description,
    required this.commonNames,
  });
}

class SouthAfricanFoods {
  // Traditional staples
  static const SAFood pap = SAFood(
    name: '<PERSON><PERSON> (<PERSON>ze Porridge)',
    nameZulu: 'Iphutu',
    nameAfrikaans: '<PERSON><PERSON>',
    nameSesotho: '<PERSON>',
    caloriesPer100g: 112,
    carbsPer100g: 23.0,
    proteinPer100g: 2.4,
    fatPer100g: 0.4,
    fiberPer100g: 1.2,
    glycemicIndex: GlycemicIndex.high,
    category: FoodCategory.grains,
    description: 'Traditional maize porridge, staple food',
    commonNames: ['pap', 'mealie pap', 'maize porridge', 'iphutu', 'papa'],
  );

  static const SAFood morogo = SAFood(
    name: 'Morogo (Wild Spinach)',
    nameZulu: 'Imifino',
    nameAfrikaans: 'Wilde spinasie',
    nameSesotho: 'Morogo',
    caloriesPer100g: 23,
    carbsPer100g: 3.6,
    proteinPer100g: 2.9,
    fatPer100g: 0.4,
    fiberPer100g: 2.2,
    glycemicIndex: GlycemicIndex.low,
    category: FoodCategory.vegetables,
    description: 'Traditional leafy green vegetables',
    commonNames: ['morogo', 'imifino', 'wilde spinasie', 'wild spinach'],
  );

  static const SAFood boerewors = SAFood(
    name: 'Boerewors',
    nameZulu: 'Iboerewors',
    nameAfrikaans: 'Boerewors',
    nameSesotho: 'Boerewors',
    caloriesPer100g: 285,
    carbsPer100g: 2.0,
    proteinPer100g: 16.5,
    fatPer100g: 23.5,
    fiberPer100g: 0.0,
    glycemicIndex: GlycemicIndex.low,
    category: FoodCategory.proteins,
    description: 'Traditional South African sausage',
    commonNames: ['boerewors', 'boerie', 'farmer\'s sausage'],
  );

  static const SAFood biltong = SAFood(
    name: 'Biltong',
    nameZulu: 'Ibiltong',
    nameAfrikaans: 'Biltong',
    nameSesotho: 'Biltong',
    caloriesPer100g: 550,
    carbsPer100g: 4.0,
    proteinPer100g: 54.0,
    fatPer100g: 34.0,
    fiberPer100g: 0.0,
    glycemicIndex: GlycemicIndex.low,
    category: FoodCategory.proteins,
    description: 'Traditional dried meat snack',
    commonNames: ['biltong', 'dried meat', 'jerky'],
  );

  static const SAFood sampAndBeans = SAFood(
    name: 'Samp and Beans',
    nameZulu: 'Isamp nezimbotshwa',
    nameAfrikaans: 'Stamp en bone',
    nameSesotho: 'Samp le dinawa',
    caloriesPer100g: 125,
    carbsPer100g: 22.0,
    proteinPer100g: 5.5,
    fatPer100g: 1.2,
    fiberPer100g: 4.8,
    glycemicIndex: GlycemicIndex.medium,
    category: FoodCategory.grains,
    description: 'Traditional crushed corn and beans',
    commonNames: ['samp and beans', 'stamp en bone', 'isamp', 'umngqusho'],
  );

  static const SAFood potjiekos = SAFood(
    name: 'Potjiekos (Stew)',
    nameZulu: 'Isitshulu',
    nameAfrikaans: 'Potjiekos',
    nameSesotho: 'Potjiekos',
    caloriesPer100g: 180,
    carbsPer100g: 8.0,
    proteinPer100g: 12.0,
    fatPer100g: 11.0,
    fiberPer100g: 2.5,
    glycemicIndex: GlycemicIndex.medium,
    category: FoodCategory.proteins,
    description: 'Traditional slow-cooked stew',
    commonNames: ['potjiekos', 'potjie', 'stew', 'isitshulu'],
  );

  static const SAFood mealieRice = SAFood(
    name: 'Mealie Rice',
    nameZulu: 'Irayisi yombila',
    nameAfrikaans: 'Mielierys',
    nameSesotho: 'Raese ea poone',
    caloriesPer100g: 130,
    carbsPer100g: 28.0,
    proteinPer100g: 2.6,
    fatPer100g: 0.3,
    fiberPer100g: 1.4,
    glycemicIndex: GlycemicIndex.high,
    category: FoodCategory.grains,
    description: 'Corn kernels prepared like rice',
    commonNames: ['mealie rice', 'mielierys', 'corn rice'],
  );

  static const SAFood amadumbe = SAFood(
    name: 'Amadumbe (Taro)',
    nameZulu: 'Amadumbe',
    nameAfrikaans: 'Amadumbe',
    nameSesotho: 'Amadumbe',
    caloriesPer100g: 112,
    carbsPer100g: 26.0,
    proteinPer100g: 1.5,
    fatPer100g: 0.2,
    fiberPer100g: 4.1,
    glycemicIndex: GlycemicIndex.medium,
    category: FoodCategory.vegetables,
    description: 'Traditional root vegetable',
    commonNames: ['amadumbe', 'taro', 'madumbe'],
  );

  static const SAFood mopanWorms = SAFood(
    name: 'Mopane Worms',
    nameZulu: 'Amacimbi',
    nameAfrikaans: 'Mopaniewurms',
    nameSesotho: 'Dikokwane',
    caloriesPer100g: 430,
    carbsPer100g: 5.0,
    proteinPer100g: 60.0,
    fatPer100g: 17.0,
    fiberPer100g: 8.0,
    glycemicIndex: GlycemicIndex.low,
    category: FoodCategory.proteins,
    description: 'Traditional protein-rich caterpillars',
    commonNames: ['mopane worms', 'amacimbi', 'dikokwane', 'mopaniewurms'],
  );

  static const SAFood mageu = SAFood(
    name: 'Mageu',
    nameZulu: 'Amageu',
    nameAfrikaans: 'Mageu',
    nameSesotho: 'Mageu',
    caloriesPer100g: 45,
    carbsPer100g: 9.0,
    proteinPer100g: 1.2,
    fatPer100g: 0.8,
    fiberPer100g: 0.5,
    glycemicIndex: GlycemicIndex.medium,
    category: FoodCategory.beverages,
    description: 'Traditional fermented maize drink',
    commonNames: ['mageu', 'amageu', 'fermented porridge drink'],
  );

  // Fruits common in SA
  static const SAFood marula = SAFood(
    name: 'Marula Fruit',
    nameZulu: 'Umganu',
    nameAfrikaans: 'Maroela',
    nameSesotho: 'Morula',
    caloriesPer100g: 48,
    carbsPer100g: 11.0,
    proteinPer100g: 0.5,
    fatPer100g: 0.2,
    fiberPer100g: 2.8,
    glycemicIndex: GlycemicIndex.low,
    category: FoodCategory.fruits,
    description: 'Indigenous African fruit',
    commonNames: ['marula', 'umganu', 'maroela', 'morula'],
  );

  static const SAFood baobab = SAFood(
    name: 'Baobab Fruit',
    nameZulu: 'Isimuhu',
    nameAfrikaans: 'Kremetart',
    nameSesotho: 'Setaola',
    caloriesPer100g: 162,
    carbsPer100g: 38.0,
    proteinPer100g: 2.3,
    fatPer100g: 0.3,
    fiberPer100g: 44.0,
    glycemicIndex: GlycemicIndex.low,
    category: FoodCategory.fruits,
    description: 'Superfruit from baobab tree',
    commonNames: ['baobab', 'isimuhu', 'kremetart', 'setaola', 'monkey bread'],
  );

  // All foods list
  static const List<SAFood> allFoods = [
    pap,
    morogo,
    boerewors,
    biltong,
    sampAndBeans,
    potjiekos,
    mealieRice,
    amadumbe,
    mopanWorms,
    mageu,
    marula,
    baobab,
  ];

  /// Find SA food by name (supports multiple languages)
  static SAFood? findByName(String name) {
    final searchName = name.toLowerCase().trim();

    for (final food in allFoods) {
      // Check all name variations
      final names = [
        food.name.toLowerCase(),
        food.nameZulu.toLowerCase(),
        food.nameAfrikaans.toLowerCase(),
        food.nameSesotho.toLowerCase(),
        ...food.commonNames.map((n) => n.toLowerCase()),
      ];

      // More specific matching logic:
      // 1. Exact match (highest priority)
      if (names.any((n) => n == searchName)) {
        return food;
      }

      // 2. Search name is a specific food name (not generic terms)
      if (_isSpecificFoodName(searchName)) {
        // Check if search name matches the main food name
        if (names.any((n) => n.contains(searchName) && searchName.length > 3)) {
          return food;
        }
      }
    }

    return null;
  }

  /// Check if the search term is a specific food name (not generic)
  static bool _isSpecificFoodName(String name) {
    // Generic terms that shouldn't match SA foods
    final genericTerms = [
      'fruit',
      'vegetable',
      'meat',
      'grain',
      'food',
      'dish',
      'snack',
      'drink',
      'beverage',
      'meal',
      'bread',
      'rice',
      'pasta',
      'soup',
      'salad',
      'sauce',
      'spice',
      'herb',
    ];

    // If the search term is a generic term, don't match SA foods
    if (genericTerms.contains(name.toLowerCase())) {
      return false;
    }

    // If it's a specific term (length > 3 and not generic), allow matching
    return name.length > 3;
  }

  /// Search foods by category
  static List<SAFood> findByCategory(FoodCategory category) {
    return allFoods.where((food) => food.category == category).toList();
  }

  /// Get foods suitable for diabetics (low GI)
  static List<SAFood> getDiabeticFriendlyFoods() {
    return allFoods
        .where(
          (food) =>
              food.glycemicIndex == GlycemicIndex.low ||
              (food.glycemicIndex == GlycemicIndex.medium &&
                  food.fiberPer100g > 3.0),
        )
        .toList();
  }

  /// Get high-protein traditional foods
  static List<SAFood> getHighProteinFoods() {
    return allFoods.where((food) => food.proteinPer100g > 10.0).toList();
  }

  /// Get traditional breakfast foods
  static List<SAFood> getBreakfastFoods() {
    return [pap, mealieRice, mageu, marula];
  }

  /// Get traditional lunch/dinner foods
  static List<SAFood> getMealFoods() {
    return [sampAndBeans, potjiekos, amadumbe, morogo, boerewors];
  }

  /// Get traditional snacks
  static List<SAFood> getSnackFoods() {
    return [biltong, mopanWorms, baobab, marula];
  }
}
