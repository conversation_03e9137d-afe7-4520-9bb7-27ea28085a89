#!/bin/bash

# GlucoMonitor Docker Management Script
# This script provides convenient commands for managing the GlucoMonitor Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if environment files exist
check_env_files() {
    if [ ! -f ".env.docker" ]; then
        print_warning ".env.docker not found. Creating from template..."
        if [ -f ".env.docker.example" ]; then
            cp .env.docker.example .env.docker
            print_warning "Please edit .env.docker with your configuration before proceeding."
            exit 1
        else
            print_error ".env.docker.example not found. Please create environment configuration."
            exit 1
        fi
    fi
}

# Function to show usage
show_usage() {
    echo "GlucoMonitor Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development environment"
    echo "  prod        Start production environment"
    echo "  test        Run tests"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  logs        Show logs for all services"
    echo "  logs-dev    Show logs for development services"
    echo "  logs-prod   Show logs for production services"
    echo "  health      Check health of all services"
    echo "  clean       Clean up Docker resources"
    echo "  rebuild     Rebuild all images without cache"
    echo "  shell       Access backend container shell"
    echo "  redis       Access Redis CLI"
    echo "  status      Show status of all services"
    echo "  setup       Initial setup (copy env files)"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 dev              # Start development environment"
    echo "  $0 prod             # Start production environment"
    echo "  $0 logs-dev         # View development logs"
    echo "  $0 health           # Check service health"
    echo "  $0 clean            # Clean up Docker resources"
}

# Function to start development environment
start_dev() {
    print_status "Starting development environment..."
    check_docker
    check_env_files
    docker-compose --profile dev up --build -d
    print_success "Development environment started!"
    print_status "Backend available at: http://localhost:5000"
    print_status "Debug port available at: localhost:9229"
    print_status "Use '$0 logs-dev' to view logs"
}

# Function to start production environment
start_prod() {
    print_status "Starting production environment..."
    check_docker
    check_env_files
    docker-compose --profile prod up --build -d
    print_success "Production environment started!"
    print_status "Backend available at: http://localhost:5000"
    print_status "Use '$0 logs-prod' to view logs"
}

# Function to run tests
run_tests() {
    print_status "Running tests..."
    check_docker
    if [ ! -f ".env.test" ]; then
        print_warning ".env.test not found. Creating from template..."
        if [ -f ".env.test.example" ]; then
            cp .env.test.example .env.test
        fi
    fi
    docker-compose --profile test up --build --abort-on-container-exit
    print_success "Tests completed!"
}

# Function to stop all services
stop_services() {
    print_status "Stopping all services..."
    docker-compose down
    print_success "All services stopped!"
}

# Function to restart services
restart_services() {
    print_status "Restarting services..."
    docker-compose restart
    print_success "Services restarted!"
}

# Function to show logs
show_logs() {
    print_status "Showing logs for all services..."
    docker-compose logs -f
}

# Function to show development logs
show_logs_dev() {
    print_status "Showing logs for development services..."
    docker-compose logs -f backend-dev redis
}

# Function to show production logs
show_logs_prod() {
    print_status "Showing logs for production services..."
    docker-compose logs -f backend-prod redis
}

# Function to check health
check_health() {
    print_status "Checking service health..."
    
    # Check if services are running
    if ! docker-compose ps | grep -q "Up"; then
        print_error "No services are currently running. Start services first."
        return 1
    fi
    
    # Check backend health
    print_status "Checking backend health..."
    if curl -f -s http://localhost:5000/api/health/live > /dev/null; then
        print_success "Backend is healthy"
    else
        print_error "Backend health check failed"
    fi
    
    # Check Redis health
    print_status "Checking Redis health..."
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        print_success "Redis is healthy"
    else
        print_error "Redis health check failed"
    fi
    
    # Show detailed health
    print_status "Detailed health information:"
    curl -s http://localhost:5000/api/health | jq '.' 2>/dev/null || curl -s http://localhost:5000/api/health
}

# Function to clean up Docker resources
clean_docker() {
    print_warning "This will remove all stopped containers, unused networks, and dangling images."
    read -p "Are you sure? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_status "Cleaning up Docker resources..."
        docker-compose down -v
        docker system prune -f
        docker volume prune -f
        print_success "Docker cleanup completed!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to rebuild images
rebuild_images() {
    print_status "Rebuilding all images without cache..."
    docker-compose build --no-cache --pull
    print_success "Images rebuilt successfully!"
}

# Function to access backend shell
access_shell() {
    print_status "Accessing backend container shell..."
    if docker-compose ps | grep -q "backend-dev.*Up"; then
        docker-compose exec backend-dev sh
    elif docker-compose ps | grep -q "backend-prod.*Up"; then
        docker-compose exec backend-prod sh
    else
        print_error "No backend container is running. Start services first."
    fi
}

# Function to access Redis CLI
access_redis() {
    print_status "Accessing Redis CLI..."
    if docker-compose ps | grep -q "redis.*Up"; then
        docker-compose exec redis redis-cli
    else
        print_error "Redis container is not running. Start services first."
    fi
}

# Function to show service status
show_status() {
    print_status "Service status:"
    docker-compose ps
    echo ""
    print_status "Resource usage:"
    docker stats --no-stream
}

# Function to setup environment
setup_env() {
    print_status "Setting up environment files..."
    
    if [ ! -f ".env.docker" ]; then
        if [ -f ".env.docker.example" ]; then
            cp .env.docker.example .env.docker
            print_success "Created .env.docker from template"
        else
            print_error ".env.docker.example not found"
        fi
    else
        print_warning ".env.docker already exists"
    fi
    
    if [ ! -f ".env.test" ]; then
        if [ -f ".env.test.example" ]; then
            cp .env.test.example .env.test
            print_success "Created .env.test from template"
        else
            print_error ".env.test.example not found"
        fi
    else
        print_warning ".env.test already exists"
    fi
    
    print_warning "Please edit the environment files with your configuration:"
    print_status "  - .env.docker (for production/development)"
    print_status "  - .env.test (for testing)"
}

# Main script logic
case "${1:-help}" in
    "dev")
        start_dev
        ;;
    "prod")
        start_prod
        ;;
    "test")
        run_tests
        ;;
    "stop")
        stop_services
        ;;
    "restart")
        restart_services
        ;;
    "logs")
        show_logs
        ;;
    "logs-dev")
        show_logs_dev
        ;;
    "logs-prod")
        show_logs_prod
        ;;
    "health")
        check_health
        ;;
    "clean")
        clean_docker
        ;;
    "rebuild")
        rebuild_images
        ;;
    "shell")
        access_shell
        ;;
    "redis")
        access_redis
        ;;
    "status")
        show_status
        ;;
    "setup")
        setup_env
        ;;
    "help"|*)
        show_usage
        ;;
esac
