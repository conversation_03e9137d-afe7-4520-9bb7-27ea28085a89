const axios = require('axios');

// Configuration
const BASE_URL = 'http://localhost:5000';

// Test user credentials (replace with your actual test user)
const TEST_USER = {
    email: '<EMAIL>',
    password: 'TestPassword123!' // Test password
};

const getAuthToken = async () => {
    try {
        console.log('🔐 Attempting to get authentication token...\n');

        // Try to login
        const response = await axios.post(`${BASE_URL}/api/auth/login`, {
            email: TEST_USER.email,
            password: TEST_USER.password
        });

        if (response.data.success && response.data.token) {
            console.log('✅ Login successful!');
            console.log('📋 Your JWT Token:');
            console.log('==========================================');
            console.log(response.data.token);
            console.log('==========================================\n');
            
            console.log('📝 Copy this token and paste it into test-export.js');
            console.log('   Replace: authToken = "YOUR_JWT_TOKEN_HERE";');
            console.log('   With:    authToken = "' + response.data.token + '";\n');
            
            console.log('🚀 Then run: node test-export.js');
            
            return response.data.token;
        } else {
            console.log('❌ Login failed: Invalid response format');
            return null;
        }

    } catch (error) {
        console.log('❌ Login failed!');
        
        if (error.response) {
            console.log(`   Status: ${error.response.status}`);
            console.log(`   Error: ${error.response.data?.message || error.response.data}`);
            
            if (error.response.status === 401) {
                console.log('\n💡 Possible solutions:');
                console.log('   1. Check your email and password in this script');
                console.log('   2. Make sure the user exists in the database');
                console.log('   3. Try registering a new user first');
            }
        } else {
            console.log(`   Error: ${error.message}`);
            console.log('\n💡 Make sure the backend server is running on port 5000');
        }
        
        return null;
    }
};

const registerTestUser = async () => {
    try {
        console.log('👤 Attempting to register test user...\n');

        const response = await axios.post(`${BASE_URL}/api/auth/register`, {
            name: 'Test User',
            email: TEST_USER.email,
            password: TEST_USER.password,
            phoneNumber: '+27123456789'
        });

        if (response.data.success) {
            console.log('✅ User registration successful!');
            console.log('📧 Verifying with development OTP: 123456');

            // Verify OTP automatically in development mode
            const verifyResponse = await axios.post(`${BASE_URL}/api/auth/verify-otp`, {
                phoneNumber: '+27123456789',
                otp: '123456'
            });

            if (verifyResponse.data.success) {
                console.log('✅ Phone verification successful!');
                return true;
            } else {
                console.log('❌ Phone verification failed');
                return false;
            }
        } else {
            console.log('❌ Registration failed');
            return false;
        }

    } catch (error) {
        if (error.response?.status === 400 && error.response.data?.message?.includes('already exists')) {
            console.log('ℹ️  User already exists, trying to login...');
            return true;
        } else {
            console.log('❌ Registration failed:', error.response?.data?.message || error.message);
            return false;
        }
    }
};

const main = async () => {
    console.log('🎯 GlucoMonitor Test Token Generator');
    console.log('====================================\n');

    // Check if credentials are set
    if (TEST_USER.password === 'your_password_here') {
        console.log('⚠️  Please update the TEST_USER credentials in this script:');
        console.log('   - Set the correct email');
        console.log('   - Set the correct password');
        console.log('   - Run the script again\n');
        return;
    }

    // Check if server is running
    try {
        await axios.get(`${BASE_URL}/health`);
        console.log('✅ Backend server is running\n');
    } catch (error) {
        console.log('❌ Backend server is not running!');
        console.log('   Please start the server first: npm run dev\n');
        return;
    }

    // Try to get token
    let token = await getAuthToken();

    // If login failed, try to register first
    if (!token) {
        console.log('\n🔄 Trying to register user first...');
        const registered = await registerTestUser();
        
        if (registered) {
            console.log('\n🔄 Now trying to login again...');
            token = await getAuthToken();
        }
    }

    if (!token) {
        console.log('\n💥 Could not obtain authentication token');
        console.log('   Please check your credentials and try again');
    }
};

// Instructions
console.log('📋 INSTRUCTIONS:');
console.log('1. Update TEST_USER credentials in this script');
console.log('2. Make sure backend server is running');
console.log('3. Run: node get-test-token.js\n');

main();
