# Medication Reminder API Documentation

## Overview
The Medication Reminder API provides comprehensive functionality for managing medications and their associated reminders in the GlucoMonitor application. This system supports CRUD operations, automatic reminder generation, notification scheduling, and adherence statistics.

## Base URL
```
/api/medications
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## Data Models

### Medication
```typescript
interface IMedication {
  _id: string;
  userId: string;
  name: string;
  brandName?: string;
  type: MedicationType;
  dosage: number;
  dosageUnit: string;
  frequency: MedicationFrequency;
  customTimes?: string[];
  startDate: Date;
  endDate?: Date;
  instructions?: string;
  color: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

enum MedicationType {
  INSULIN = 'insulin',
  METFORMIN = 'metformin',
  SUPPLEMENT = 'supplement',
  OTHER = 'other'
}

enum MedicationFrequency {
  ONCE_DAILY = 'once_daily',
  TWICE_DAILY = 'twice_daily',
  THREE_TIMES_DAILY = 'three_times_daily',
  FOUR_TIMES_DAILY = 'four_times_daily',
  WEEKLY = 'weekly',
  CUSTOM = 'custom'
}
```

### MedicationReminder
```typescript
interface IMedicationReminder {
  _id: string;
  medicationId: string;
  userId: string;
  scheduledTime: Date;
  status: ReminderStatus;
  takenAt?: Date;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

enum ReminderStatus {
  PENDING = 'pending',
  TAKEN = 'taken',
  MISSED = 'missed',
  SKIPPED = 'skipped'
}
```

## API Endpoints

### Medications

#### GET /api/medications
Get all medications for the authenticated user.

**Query Parameters:**
- `active` (boolean): Filter by active status
- `type` (string): Filter by medication type
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Items per page (default: 10)

**Response:**
```json
{
  "success": true,
  "count": 5,
  "total": 15,
  "page": 1,
  "pages": 2,
  "data": [
    {
      "_id": "medication_id",
      "name": "Metformin",
      "brandName": "Glucophage",
      "type": "metformin",
      "dosage": 500,
      "dosageUnit": "mg",
      "frequency": "twice_daily",
      "startDate": "2024-01-01T00:00:00.000Z",
      "color": "#4CAF50",
      "isActive": true,
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### POST /api/medications
Create a new medication.

**Request Body:**
```json
{
  "name": "Metformin",
  "brandName": "Glucophage",
  "type": "metformin",
  "dosage": 500,
  "dosageUnit": "mg",
  "frequency": "twice_daily",
  "startDate": "2024-01-01T00:00:00.000Z",
  "endDate": "2024-12-31T23:59:59.999Z",
  "instructions": "Take with meals",
  "customTimes": ["08:00", "20:00"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "medication_id",
    "name": "Metformin",
    "brandName": "Glucophage",
    "type": "metformin",
    "dosage": 500,
    "dosageUnit": "mg",
    "frequency": "twice_daily",
    "startDate": "2024-01-01T00:00:00.000Z",
    "endDate": "2024-12-31T23:59:59.999Z",
    "instructions": "Take with meals",
    "color": "#4CAF50",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### GET /api/medications/:id
Get a specific medication by ID.

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "medication_id",
    "name": "Metformin",
    "brandName": "Glucophage",
    "type": "metformin",
    "dosage": 500,
    "dosageUnit": "mg",
    "frequency": "twice_daily",
    "startDate": "2024-01-01T00:00:00.000Z",
    "color": "#4CAF50",
    "isActive": true,
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T00:00:00.000Z"
  }
}
```

#### PUT /api/medications/:id
Update a specific medication.

**Request Body:** Same as POST request
**Response:** Same as GET response

#### DELETE /api/medications/:id
Delete a medication and all associated reminders.

**Response:**
```json
{
  "success": true,
  "message": "Medication and associated reminders deleted successfully"
}
```

### Reminders

#### GET /api/medications/reminders
Get all reminders for the authenticated user.

**Query Parameters:**
- `date` (string): Filter by specific date (YYYY-MM-DD)
- `status` (string): Filter by reminder status
- `page` (number): Page number for pagination (default: 1)
- `limit` (number): Items per page (default: 50)

**Response:**
```json
{
  "success": true,
  "count": 10,
  "total": 25,
  "page": 1,
  "pages": 3,
  "data": [
    {
      "_id": "reminder_id",
      "medicationId": {
        "_id": "medication_id",
        "name": "Metformin",
        "brandName": "Glucophage",
        "dosage": 500,
        "dosageUnit": "mg",
        "color": "#4CAF50",
        "type": "metformin"
      },
      "scheduledTime": "2024-01-01T08:00:00.000Z",
      "status": "pending",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

#### GET /api/medications/:id/reminders
Get reminders for a specific medication.

**Query Parameters:**
- `startDate` (string): Start date for filtering
- `endDate` (string): End date for filtering
- `status` (string): Filter by reminder status

**Response:** Same format as GET /api/medications/reminders

#### PUT /api/medications/reminders/:id
Update a specific reminder status.

**Request Body:**
```json
{
  "status": "taken",
  "takenAt": "2024-01-01T08:05:00.000Z",
  "notes": "Taken with breakfast"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "reminder_id",
    "medicationId": "medication_id",
    "scheduledTime": "2024-01-01T08:00:00.000Z",
    "status": "taken",
    "takenAt": "2024-01-01T08:05:00.000Z",
    "notes": "Taken with breakfast",
    "createdAt": "2024-01-01T00:00:00.000Z",
    "updatedAt": "2024-01-01T08:05:00.000Z"
  }
}
```

#### PUT /api/medications/reminders/bulk
Bulk update multiple reminder statuses.

**Request Body:**
```json
{
  "updates": [
    {
      "reminderId": "reminder_id_1",
      "status": "taken",
      "takenAt": "2024-01-01T08:05:00.000Z"
    },
    {
      "reminderId": "reminder_id_2",
      "status": "missed"
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk update completed",
  "results": {
    "successful": 2,
    "failed": 0,
    "details": [
      {
        "reminderId": "reminder_id_1",
        "success": true
      },
      {
        "reminderId": "reminder_id_2",
        "success": true
      }
    ]
  }
}
```

### Statistics

#### GET /api/medications/stats
Get medication adherence statistics.

**Query Parameters:**
- `startDate` (string): Start date for statistics calculation
- `endDate` (string): End date for statistics calculation

**Response:**
```json
{
  "success": true,
  "data": {
    "totalMedications": 5,
    "activeMedications": 4,
    "totalReminders": 150,
    "takenReminders": 135,
    "missedReminders": 10,
    "skippedReminders": 5,
    "adherenceRate": 90.0,
    "medicationBreakdown": [
      {
        "medicationId": "medication_id",
        "medicationName": "Metformin",
        "totalReminders": 30,
        "takenReminders": 28,
        "missedReminders": 2,
        "adherenceRate": 93.33
      }
    ]
  }
}
```

### Utility Endpoints

#### POST /api/medications/:id/generate-reminders
Generate reminders for a specific medication.

**Request Body:**
```json
{
  "startDate": "2024-01-01T00:00:00.000Z",
  "endDate": "2024-01-31T23:59:59.999Z"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Reminders generated successfully",
  "data": {
    "generatedCount": 62,
    "replacedCount": 0
  }
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "errors": ["Detailed error messages"]
}
```

### Common HTTP Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (authentication required)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

## Rate Limiting
API endpoints are rate-limited to prevent abuse. Current limits:
- 100 requests per minute per user
- 1000 requests per hour per user

## Notification System
The medication reminder system includes automatic SMS notifications:
- Reminders are sent 5 minutes before scheduled time
- Follow-up notifications for missed medications
- Daily adherence summaries
- Weekly progress reports

## Development Mode
In development mode:
- SMS notifications are logged to console instead of being sent
- Fixed OTP "123456" is used for testing
- Database operations use test database
