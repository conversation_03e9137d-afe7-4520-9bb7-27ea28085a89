import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:google_fonts/google_fonts.dart';
import 'screens/onboarding/welcome_screen.dart';
import 'screens/food/enhanced_ai_recognition_demo_screen.dart';
import 'screens/food/barcode_scanner_screen.dart';
import 'screens/food/voice_input_screen.dart';
import 'screens/food/ai_recognition_screen.dart';
import 'screens/settings/ai_recognition_settings_screen.dart';
import 'providers/language_provider.dart';
import 'providers/auth_provider.dart';
import 'providers/medication_provider.dart';
import 'providers/food_diary_provider.dart';
import 'providers/ai_recognition_settings_provider.dart';
import 'services/connectivity_service.dart';
import 'services/backend_service.dart';
import 'services/notification_service.dart';
import 'constants/app_colors.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Preserve the splash screen until initialization is complete
  FlutterNativeSplash.preserve(
    widgetsBinding: WidgetsFlutterBinding.ensureInitialized(),
  );

  try {
    // Load environment variables with timeout
    await dotenv
        .load(fileName: ".env")
        .timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            debugPrint('Environment loading timed out');
            throw TimeoutException(
              'Environment loading timeout',
              const Duration(seconds: 5),
            );
          },
        );

    // Initialize backend service with timeout and error handling
    await BackendService.init()
        .timeout(
          const Duration(seconds: 10),
          onTimeout: () {
            debugPrint('Backend service initialization timed out');
            // Don't throw error, just log it - app can work in offline mode
          },
        )
        .catchError((e) {
          debugPrint('Backend service initialization failed: $e');
          // Don't throw error, just log it - app can work in offline mode
        });

    // Initialize notification service
    await NotificationService.initialize();
    await NotificationService.requestPermissions();

    // Initialize connectivity service with timeout
    final connectivityService = ConnectivityService();
    await connectivityService
        .init()
        .timeout(
          const Duration(seconds: 5),
          onTimeout: () {
            debugPrint('Connectivity service initialization timed out');
            // Don't throw error, just log it
          },
        )
        .catchError((e) {
          debugPrint('Connectivity service initialization failed: $e');
          // Don't throw error, just log it
        });

    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ChangeNotifierProvider(create: (_) => AuthProvider()),
          ChangeNotifierProvider(create: (_) => MedicationProvider()),
          ChangeNotifierProvider(create: (_) => FoodDiaryProvider()),
          ChangeNotifierProvider(
            create: (_) => AIRecognitionSettingsProvider(),
          ),
          Provider.value(value: connectivityService),
        ],
        child: const MyApp(),
      ),
    );
  } catch (e) {
    // If initialization fails, still run the app but with error handling
    debugPrint('Initialization error: $e');
    runApp(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => LanguageProvider()),
          ChangeNotifierProvider(create: (_) => AuthProvider()),
          ChangeNotifierProvider(create: (_) => MedicationProvider()),
          ChangeNotifierProvider(create: (_) => FoodDiaryProvider()),
          ChangeNotifierProvider(
            create: (_) => AIRecognitionSettingsProvider(),
          ),
          Provider.value(value: ConnectivityService()),
        ],
        child: const MyApp(),
      ),
    );
  }
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    // Remove splash screen after the first frame is rendered
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FlutterNativeSplash.remove();
      // Initialize AI recognition settings
      _initializeAISettings();
    });
  }

  void _initializeAISettings() {
    // Initialize AI recognition settings provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<AIRecognitionSettingsProvider>().initialize();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final connectivityService = Provider.of<ConnectivityService>(context);
    return MaterialApp(
      title: 'GlucoMonitor',
      debugShowCheckedModeBanner: false,
      routes: {
        '/enhanced-ai-demo':
            (context) => const EnhancedAIRecognitionDemoScreen(),
        '/barcode-scanner': (context) => const BarcodeScannerScreen(),
        '/voice-input': (context) => const VoiceInputScreen(),
        '/ai-recognition': (context) => const AIRecognitionScreen(),
        '/ai-settings': (context) => const AIRecognitionSettingsScreen(),
      },
      theme: ThemeData(
        colorScheme: AppColors.lightColorScheme,
        useMaterial3: true,
        scaffoldBackgroundColor: AppColors.background,
        textTheme: GoogleFonts.robotoTextTheme(),
        appBarTheme: AppBarTheme(
          centerTitle: true,
          backgroundColor: AppColors.background,
          elevation: 0,
          iconTheme: const IconThemeData(color: AppColors.onBackground),
          titleTextStyle: GoogleFonts.roboto(
            color: AppColors.onBackground,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.secondary, // Use green instead of teal
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            foregroundColor: Colors.white, // White text for visibility
            side: const BorderSide(color: Colors.white), // White border
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        textButtonTheme: TextButtonThemeData(
          style: TextButton.styleFrom(
            foregroundColor: Colors.white,
          ), // White text
        ),
      ),
      darkTheme: ThemeData(
        colorScheme: AppColors.darkColorScheme,
        useMaterial3: true,
        textTheme: GoogleFonts.robotoTextTheme(ThemeData.dark().textTheme),
        appBarTheme: AppBarTheme(
          centerTitle: true,
          backgroundColor: AppColors.backgroundDark,
          elevation: 0,
          iconTheme: const IconThemeData(color: AppColors.surfCrest),
          titleTextStyle: GoogleFonts.roboto(
            color: AppColors.surfCrest,
            fontSize: 20,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      builder: (context, child) {
        return Stack(
          children: [
            child!,
            // Show offline/retry status
            ValueListenableBuilder<bool>(
              valueListenable: connectivityService.isOnline,
              builder: (context, isOnline, _) {
                return ValueListenableBuilder<String?>(
                  valueListenable: BackendService.retryStatus,
                  builder: (context, retryStatus, _) {
                    if (!isOnline || retryStatus != null) {
                      return Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Material(
                          color: isOnline ? Colors.orange : Colors.grey[800],
                          child: SafeArea(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Text(
                                retryStatus ?? 'You are offline',
                                style: const TextStyle(color: Colors.white),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ),
                        ),
                      );
                    }
                    return const SizedBox.shrink();
                  },
                );
              },
            ),
          ],
        );
      },
      home: const WelcomeScreen(),
    );
  }
}
