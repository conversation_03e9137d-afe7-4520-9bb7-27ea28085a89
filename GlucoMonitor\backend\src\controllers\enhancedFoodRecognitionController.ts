import { Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { catchAsync } from '../utils/errorHandler';
import logger from '../utils/logger';
import FoodRecognitionHistory from '../models/FoodRecognitionHistory';
import CustomFoodModel from '../models/CustomFoodModel';
import { processImageForRecognition, extractImageFeatures } from '../services/imageProcessingService';
import { analyzeRecognitionAccuracy, generateRecommendations } from '../services/aiAnalyticsService';
import mongoose from 'mongoose';

// Extend AuthRequest to include startTime for performance tracking
interface EnhancedAuthRequest extends AuthRequest {
    startTime?: number;
}

/**
 * Enhanced food recognition with multi-detection and confidence scoring
 */
export const enhancedFoodRecognition = catchAsync(async (req: EnhancedAuthRequest, res: Response): Promise<void> => {
    // Add startTime for performance tracking
    req.startTime = Date.now();
    const userId = req.user!._id;
    const { 
        imageData, 
        mealType, 
        enableMultiDetection = false,
        enablePortionEstimation = false,
        useCustomModels = true 
    } = req.body;

    if (!imageData) {
        res.status(400).json({
            success: false,
            message: 'Image data is required'
        });
        return;
    }

    try {
        logger.info('Enhanced food recognition started', {
            userId: userId.toString(),
            mealType,
            enableMultiDetection,
            enablePortionEstimation,
            useCustomModels
        });

        // Process image and extract features
        const imageFeatures = await extractImageFeatures(imageData);
        
        // Check custom trained models first
        let recognitionResults = [];
        if (useCustomModels) {
            const customResults = await recognizeWithCustomModels(userId, imageFeatures, mealType);
            recognitionResults.push(...customResults);
        }

        // If no custom results or multi-detection enabled, use enhanced AI
        if (recognitionResults.length === 0 || enableMultiDetection) {
            const aiResults = await processImageForRecognition(
                imageData, 
                {
                    enableMultiDetection,
                    enablePortionEstimation,
                    userId: userId.toString(),
                    mealType
                }
            );
            recognitionResults.push(...aiResults);
        }

        // Calculate overall confidence
        const overallConfidence = calculateOverallConfidence(recognitionResults);

        // Record recognition attempt for learning
        await recordRecognitionAttempt(userId, {
            imageFeatures,
            recognitionResults,
            mealType,
            confidence: overallConfidence,
            method: useCustomModels ? 'ENHANCED_WITH_CUSTOM' : 'ENHANCED_AI'
        });

        res.json({
            success: true,
            data: {
                recognitionResults,
                overallConfidence,
                multiDetectionEnabled: enableMultiDetection,
                portionEstimationEnabled: enablePortionEstimation,
                customModelsUsed: useCustomModels,
                processingTime: Date.now() - req.startTime
            }
        });

    } catch (error) {
        logger.error('Enhanced food recognition failed', {
            userId: userId.toString(),
            error: error.message
        });

        res.status(500).json({
            success: false,
            message: 'Food recognition failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

/**
 * Train custom food model
 */
export const trainCustomFood = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const {
        foodName,
        description,
        nutritionData,
        trainingImages,
        tags = []
    } = req.body;

    if (!foodName || !trainingImages || trainingImages.length === 0) {
        res.status(400).json({
            success: false,
            message: 'Food name and training images are required'
        });
        return;
    }

    try {
        logger.info('Custom food training started', {
            userId: userId.toString(),
            foodName,
            imageCount: trainingImages.length
        });

        // Extract features from training images
        const trainingFeatures = [];
        for (const imageData of trainingImages) {
            const features = await extractImageFeatures(imageData);
            trainingFeatures.push(features);
        }

        // Create custom food model
        const customFood = new CustomFoodModel({
            userId,
            foodName,
            description,
            nutritionData,
            trainingFeatures,
            tags,
            confidence: calculateTrainingConfidence(trainingFeatures),
            createdAt: new Date(),
            lastTrained: new Date()
        });

        await customFood.save();

        logger.info('Custom food training completed', {
            userId: userId.toString(),
            foodName,
            customFoodId: customFood._id
        });

        res.status(201).json({
            success: true,
            message: 'Custom food trained successfully',
            data: {
                customFoodId: customFood._id,
                foodName,
                confidence: customFood.confidence,
                trainingImageCount: trainingImages.length
            }
        });

    } catch (error) {
        logger.error('Custom food training failed', {
            userId: userId.toString(),
            foodName,
            error: error.message
        });

        res.status(500).json({
            success: false,
            message: 'Custom food training failed',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
});

/**
 * Get user's custom trained foods
 */
export const getCustomTrainedFoods = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;

    try {
        const customFoods = await CustomFoodModel.find({ userId })
            .select('-trainingFeatures') // Exclude large feature data
            .sort({ lastTrained: -1 });

        res.json({
            success: true,
            data: {
                customFoods: customFoods.map(food => ({
                    id: food._id,
                    foodName: food.foodName,
                    description: food.description,
                    tags: food.tags,
                    confidence: food.confidence,
                    createdAt: food.createdAt,
                    lastTrained: food.lastTrained
                })),
                totalCount: customFoods.length
            }
        });

    } catch (error) {
        logger.error('Failed to get custom trained foods', {
            userId: userId.toString(),
            error: error.message
        });

        res.status(500).json({
            success: false,
            message: 'Failed to retrieve custom foods'
        });
    }
});

/**
 * Update custom food training
 */
export const updateCustomFood = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const { customFoodId } = req.params;
    const {
        additionalImages = [],
        updatedNutrition,
        updatedDescription,
        updatedTags
    } = req.body;

    try {
        const customFood = await CustomFoodModel.findOne({
            _id: customFoodId,
            userId
        });

        if (!customFood) {
            res.status(404).json({
                success: false,
                message: 'Custom food not found'
            });
            return;
        }

        // Add new training features
        if (additionalImages.length > 0) {
            const newFeatures = [];
            for (const imageData of additionalImages) {
                const features = await extractImageFeatures(imageData);
                newFeatures.push(features);
            }
            customFood.trainingFeatures.push(...newFeatures);
        }

        // Update other fields
        if (updatedNutrition) customFood.nutritionData = updatedNutrition;
        if (updatedDescription) customFood.description = updatedDescription;
        if (updatedTags) customFood.tags = updatedTags;

        customFood.lastTrained = new Date();
        customFood.confidence = calculateTrainingConfidence(customFood.trainingFeatures);

        await customFood.save();

        logger.info('Custom food updated', {
            userId: userId.toString(),
            customFoodId,
            newImageCount: additionalImages.length
        });

        res.json({
            success: true,
            message: 'Custom food updated successfully',
            data: {
                customFoodId,
                confidence: customFood.confidence,
                totalTrainingImages: customFood.trainingFeatures.length
            }
        });

    } catch (error) {
        logger.error('Failed to update custom food', {
            userId: userId.toString(),
            customFoodId,
            error: error.message
        });

        res.status(500).json({
            success: false,
            message: 'Failed to update custom food'
        });
    }
});

/**
 * Get recognition analytics and accuracy metrics
 */
export const getRecognitionAnalytics = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id as mongoose.Types.ObjectId;
    const { timeRange = '30d' } = req.query;

    try {
        const analytics = await analyzeRecognitionAccuracy(userId, timeRange as string);
        const recommendations = await generateRecommendations(userId, analytics);

        res.json({
            success: true,
            data: {
                analytics,
                recommendations,
                timeRange
            }
        });

    } catch (error) {
        logger.error('Failed to get recognition analytics', {
            userId: userId.toString(),
            error: error.message
        });

        res.status(500).json({
            success: false,
            message: 'Failed to retrieve analytics'
        });
    }
});

/**
 * Submit user feedback for recognition improvement
 */
export const submitRecognitionFeedback = catchAsync(async (req: AuthRequest, res: Response): Promise<void> => {
    const userId = req.user!._id;
    const {
        recognitionId,
        isCorrect,
        correctedFoodName,
        confidenceRating,
        additionalNotes
    } = req.body;

    if (!recognitionId || isCorrect === undefined) {
        res.status(400).json({
            success: false,
            message: 'Recognition ID and correctness feedback are required'
        });
        return;
    }

    try {
        // Find the recognition record
        const recognition = await FoodRecognitionHistory.findOne({
            _id: recognitionId,
            userId
        });

        if (!recognition) {
            res.status(404).json({
                success: false,
                message: 'Recognition record not found'
            });
            return;
        }

        // Update with user feedback
        recognition.userCorrection = {
            originalResult: recognition.recognizedFood?.name || 'Unknown',
            correctedResult: correctedFoodName || recognition.recognizedFood?.name || 'Unknown',
            correctionType: isCorrect ? 'food_name' : 'complete_replacement',
            userFeedback: additionalNotes,
            timestamp: new Date()
        };

        await recognition.save();

        // Process feedback for learning algorithm
        await processFeedbackForLearning(userId, recognition);

        logger.info('Recognition feedback submitted', {
            userId: userId.toString(),
            recognitionId,
            isCorrect,
            correctedFoodName
        });

        res.json({
            success: true,
            message: 'Feedback submitted successfully'
        });

    } catch (error) {
        logger.error('Failed to submit recognition feedback', {
            userId: userId.toString(),
            recognitionId,
            error: error.message
        });

        res.status(500).json({
            success: false,
            message: 'Failed to submit feedback'
        });
    }
});

// Helper functions
async function recognizeWithCustomModels(userId: any, imageFeatures: any, _mealType: string) {
    const customFoods = await CustomFoodModel.find({ userId });
    const results = [];

    for (const customFood of customFoods) {
        const similarity = calculateFeatureSimilarity(imageFeatures, customFood.trainingFeatures);
        if (similarity > 0.75) { // High threshold for custom foods
            results.push({
                foodName: customFood.foodName,
                confidence: similarity * customFood.confidence,
                source: 'CUSTOM_MODEL',
                nutritionData: customFood.nutritionData
            });
        }
    }

    return results.sort((a, b) => b.confidence - a.confidence);
}

function calculateOverallConfidence(results: any[]) {
    if (results.length === 0) return 0;
    return results.reduce((sum, result) => sum + result.confidence, 0) / results.length;
}

function calculateTrainingConfidence(trainingFeatures: any[]) {
    // Calculate confidence based on number and quality of training examples
    const baseConfidence = Math.min(trainingFeatures.length / 5, 1.0); // Max confidence with 5+ examples
    return baseConfidence * 0.9; // 90% max confidence for custom models
}

function calculateFeatureSimilarity(_imageFeatures: any, trainingFeatures: any[]) {
    // Simplified similarity calculation
    // In production, this would use proper ML similarity metrics
    // Base similarity on training data quality for now
    const baseScore = Math.min(trainingFeatures.length / 10, 1.0);
    return baseScore * 0.3 + 0.7; // 70-100% similarity based on training data
}

async function recordRecognitionAttempt(userId: any, data: any) {
    const recognition = new FoodRecognitionHistory({
        userId,
        recognitionAttempt: {
            method: data.method,
            confidence: data.confidence,
            timestamp: new Date()
        },
        recognizedFood: data.recognitionResults[0] || null,
        mealContext: {
            mealType: data.mealType,
            timestamp: new Date()
        },
        imageMetadata: data.imageFeatures
    });

    await recognition.save();
    return recognition;
}

async function processFeedbackForLearning(userId: any, recognition: any) {
    // Process user feedback to improve future recognition
    // This would integrate with ML pipeline in production
    logger.info('Processing feedback for learning algorithm', {
        userId: userId.toString(),
        recognitionId: recognition._id
    });
}
