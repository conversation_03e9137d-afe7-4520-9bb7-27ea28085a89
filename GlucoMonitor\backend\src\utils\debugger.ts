import { Request, Response, NextFunction } from 'express';
import logger from './logger';
import { env } from '../config/env';

/**
 * Development debugging utilities
 * Only active in development environment
 */

interface DebugInfo {
    timestamp: string;
    requestId: string;
    method: string;
    url: string;
    headers: any;
    body: any;
    params: any;
    query: any;
    user?: any;
    ip: string;
    userAgent?: string;
}

class Debugger {
    private static instance: Debugger;
    private debugMode: boolean;
    private debugRequests: DebugInfo[] = [];
    private maxStoredRequests = 100;

    private constructor() {
        this.debugMode = env.NODE_ENV === 'development';
    }

    public static getInstance(): Debugger {
        if (!Debugger.instance) {
            Debugger.instance = new Debugger();
        }
        return Debugger.instance;
    }

    public isDebugMode(): boolean {
        return this.debugMode;
    }

    public setDebugMode(enabled: boolean): void {
        this.debugMode = enabled;
        logger.info(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }

    public logRequest(req: Request): void {
        if (!this.debugMode) return;

        const debugInfo: DebugInfo = {
            timestamp: new Date().toISOString(),
            requestId: req.headers['x-request-id'] as string,
            method: req.method,
            url: req.url,
            headers: this.sanitizeHeaders(req.headers),
            body: this.sanitizeBody(req.body),
            params: req.params,
            query: req.query,
            user: (req as any).user ? {
                id: (req as any).user.id,
                email: (req as any).user.email,
                role: (req as any).user.role
            } : undefined,
            ip: req.ip,
            userAgent: req.get('User-Agent')
        };

        this.debugRequests.unshift(debugInfo);
        
        // Keep only the last N requests
        if (this.debugRequests.length > this.maxStoredRequests) {
            this.debugRequests = this.debugRequests.slice(0, this.maxStoredRequests);
        }

        logger.debug('Request captured for debugging', debugInfo);
    }

    public getStoredRequests(limit: number = 20): DebugInfo[] {
        return this.debugRequests.slice(0, limit);
    }

    public getRequestById(requestId: string): DebugInfo | undefined {
        return this.debugRequests.find(req => req.requestId === requestId);
    }

    public clearStoredRequests(): void {
        this.debugRequests = [];
        logger.debug('Stored debug requests cleared');
    }

    public logDatabaseQuery(query: string, params?: any, duration?: number): void {
        if (!this.debugMode) return;

        logger.debug('Database query executed', {
            query: this.sanitizeQuery(query),
            params: this.sanitizeParams(params),
            duration: duration,
            category: 'database-debug'
        });
    }

    public logPerformance(operation: string, duration: number, context?: any): void {
        if (!this.debugMode) return;

        const level = duration > 1000 ? 'warn' : 'debug';
        logger[level](`Performance: ${operation} (${duration}ms)`, {
            duration: duration,
            operation,
            context,
            category: 'performance-debug'
        });
    }

    public logMemoryUsage(context?: string): void {
        if (!this.debugMode) return;

        const memoryUsage = process.memoryUsage();
        logger.debug('Memory usage snapshot', {
            context,
            heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)}MB`,
            heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)}MB`,
            rss: `${Math.round(memoryUsage.rss / 1024 / 1024)}MB`,
            external: `${Math.round(memoryUsage.external / 1024 / 1024)}MB`,
            category: 'memory-debug'
        });
    }

    public createBreakpoint(name: string, data?: any): void {
        if (!this.debugMode) return;

        logger.debug(`🔴 BREAKPOINT: ${name}`, {
            breakpoint: name,
            data,
            stack: new Error().stack?.split('\n').slice(1, 4),
            category: 'breakpoint'
        });
    }

    public trace(message: string, data?: any): void {
        if (!this.debugMode) return;

        logger.trace(message, {
            data,
            stack: new Error().stack?.split('\n').slice(1, 6),
            category: 'trace'
        });
    }

    private sanitizeHeaders(headers: any): any {
        const sanitized = { ...headers };
        
        // Remove sensitive headers
        delete sanitized.authorization;
        delete sanitized.cookie;
        delete sanitized['x-api-key'];
        
        return sanitized;
    }

    private sanitizeBody(body: any): any {
        if (!body || typeof body !== 'object') return body;

        const sanitized = { ...body };
        
        // Remove sensitive fields
        delete sanitized.password;
        delete sanitized.confirmPassword;
        delete sanitized.token;
        delete sanitized.refreshToken;
        delete sanitized.apiKey;
        
        return sanitized;
    }

    private sanitizeQuery(query: string): string {
        // Remove potential sensitive data from query strings
        return query.replace(/password\s*=\s*['"][^'"]*['"]/gi, 'password=***');
    }

    private sanitizeParams(params: any): any {
        if (!params || typeof params !== 'object') return params;

        const sanitized = { ...params };
        
        // Remove sensitive parameters
        if (sanitized.password) sanitized.password = '***';
        if (sanitized.token) sanitized.token = '***';
        
        return sanitized;
    }
}

// Create singleton instance
const debugInstance = Debugger.getInstance();

// Debug middleware
export const debugMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    if (debugInstance.isDebugMode()) {
        debugInstance.logRequest(req);

        // Add debug helpers to request object
        (req as any).debug = {
            breakpoint: (name: string, data?: any) => debugInstance.createBreakpoint(name, data),
            trace: (message: string, data?: any) => debugInstance.trace(message, data),
            logPerformance: (operation: string, duration: number, context?: any) =>
                debugInstance.logPerformance(operation, duration, context),
            logMemory: (context?: string) => debugInstance.logMemoryUsage(context)
        };
    }

    next();
};

// Performance timing decorator
export function timed(target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;
    
    descriptor.value = async function (...args: any[]) {
        const start = Date.now();
        const result = await method.apply(this, args);
        const duration = Date.now() - start;
        
        debugInstance.logPerformance(`${target.constructor.name}.${propertyName}`, duration);
        
        return result;
    };
    
    return descriptor;
}

// Memory monitoring function
export function monitorMemory(interval: number = 30000): void {
    if (!debugInstance.isDebugMode()) return;

    setInterval(() => {
        debugInstance.logMemoryUsage('periodic-check');
    }, interval);
}

// Debug route handlers
export const getDebugInfo = (req: Request, res: Response): void => {
    if (!debugInstance.isDebugMode()) {
        res.status(403).json({
            success: false,
            message: 'Debug mode is not enabled'
        });
        return;
    }

    const limit = parseInt(req.query.limit as string) || 20;
    const requests = debugInstance.getStoredRequests(limit);

    res.json({
        success: true,
        data: {
            debugMode: true,
            totalRequests: requests.length,
            requests,
            memoryUsage: process.memoryUsage(),
            uptime: process.uptime(),
            environment: env.NODE_ENV
        }
    });
};

export const getDebugRequest = (req: Request, res: Response): void => {
    if (!debugInstance.isDebugMode()) {
        res.status(403).json({
            success: false,
            message: 'Debug mode is not enabled'
        });
        return;
    }

    const requestId = req.params.requestId;
    const debugRequest = debugInstance.getRequestById(requestId);

    if (!debugRequest) {
        res.status(404).json({
            success: false,
            message: 'Debug request not found'
        });
        return;
    }

    res.json({
        success: true,
        data: debugRequest
    });
};

export const clearDebugData = (req: Request, res: Response): void => {
    if (!debugInstance.isDebugMode()) {
        res.status(403).json({
            success: false,
            message: 'Debug mode is not enabled'
        });
        return;
    }

    debugInstance.clearStoredRequests();

    res.json({
        success: true,
        message: 'Debug data cleared'
    });
};

export default debugInstance;
