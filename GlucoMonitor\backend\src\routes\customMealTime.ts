import express from 'express';
import { protect } from '../middleware/auth';
import {
    getCustomMealTimes,
    createCustomMealTime,
    updateCustomMealTime,
    deleteCustomMealTime,
    initializeDefaultMealTimes
} from '../controllers/customMealTimeController';

const router = express.Router();

// Apply authentication middleware to all routes
router.use(protect);

// @route   GET /api/custom-meal-times
// @desc    Get all custom meal times for user
// @access  Private
router.get('/', getCustomMealTimes);

// @route   POST /api/custom-meal-times
// @desc    Create a new custom meal time
// @access  Private
router.post('/', createCustomMealTime);

// @route   POST /api/custom-meal-times/initialize-defaults
// @desc    Initialize default meal times for user
// @access  Private
router.post('/initialize-defaults', initializeDefaultMealTimes);

// @route   PUT /api/custom-meal-times/:id
// @desc    Update a custom meal time
// @access  Private
router.put('/:id', updateCustomMealTime);

// @route   DELETE /api/custom-meal-times/:id
// @desc    Delete a custom meal time
// @access  Private
router.delete('/:id', deleteCustomMealTime);

export default router;
