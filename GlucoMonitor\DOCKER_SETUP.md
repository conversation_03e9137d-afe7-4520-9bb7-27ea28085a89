# 🐳 GlucoMonitor Docker Setup Guide

This guide provides comprehensive instructions for setting up and running GlucoMonitor using Docker with support for development, testing, and production environments.

## 📋 Prerequisites

- Docker Engine 20.10+ and Docker Compose 2.0+
- Git (for cloning the repository)
- 4GB+ RAM available for Docker
- 10GB+ free disk space

### Installation Links
- [Docker Desktop](https://www.docker.com/products/docker-desktop/) (Windows/macOS)
- [Docker Engine](https://docs.docker.com/engine/install/) (Linux)

## 🚀 Quick Start

### 1. Clone and Setup
```bash
# Clone the repository
git clone <repository-url>
cd GlucoMonitor

# Copy environment templates
cp .env.docker.example .env.docker
cp .env.test.example .env.test

# Edit environment files with your configuration
nano .env.docker  # or use your preferred editor
```

### 2. Development Environment
```bash
# Start development environment with hot reloading
docker-compose --profile dev up --build

# Run in background
docker-compose --profile dev up -d --build

# View logs
docker-compose logs -f backend-dev
```

### 3. Production Environment
```bash
# Start production environment
docker-compose --profile prod up --build

# Run in background
docker-compose --profile prod up -d --build

# View logs
docker-compose logs -f backend-prod
```

### 4. Testing Environment
```bash
# Run tests
docker-compose --profile test up --build

# Run tests and exit
docker-compose --profile test up --build --abort-on-container-exit
```

## 🔧 Environment Configuration

### Required Environment Variables

Create `.env.docker` with the following variables:

```env
# Database
MONGODB_URI=mongodb://your-mongodb-connection-string

# Authentication
JWT_SECRET=your-super-secret-jwt-key-minimum-32-characters
JWT_EXPIRE=30d

# SMS Service (SMSPortal)
SMSPORTAL_CLIENT_ID=your-smsportal-client-id
SMSPORTAL_API_SECRET=your-smsportal-api-secret
SMSPORTAL_SENDER=your-sender-name

# Application
NODE_ENV=production
PORT=5000
REDIS_URL=redis://redis:6379
NOTIFICATION_ENABLED=true
DEVELOPMENT_MODE=false
```

### Optional Configuration

```env
# Logging
LOG_LEVEL=info
LOG_FILE_ENABLED=true

# Security
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Health Checks
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Export Features
GOOGLE_APPLICATION_CREDENTIALS=/app/config/google/service-account-key.json
```

## 🏗️ Architecture Overview

### Services

1. **Backend API** (`backend-dev`/`backend-prod`)
   - Node.js 20 with TypeScript
   - Express.js REST API
   - JWT authentication
   - SMS integration
   - File export capabilities

2. **Redis Cache** (`redis`)
   - Redis 7 Alpine
   - Caching and rate limiting
   - Session storage
   - 256MB memory limit with LRU eviction

### Profiles

- **`dev`**: Development with hot reloading and debugging
- **`prod`**: Production-optimized with security hardening
- **`test`**: Testing environment with minimal configuration

## 🛠️ Development Workflow

### Hot Reloading
Changes to source code automatically restart the server:
```bash
# Start development environment
docker-compose --profile dev up

# Edit files in backend/src/
# Server automatically restarts on changes
```

### Debugging
Attach your IDE debugger to port 9229:

**VS Code Configuration** (`.vscode/launch.json`):
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Docker Debug",
      "type": "node",
      "request": "attach",
      "port": 9229,
      "address": "localhost",
      "localRoot": "${workspaceFolder}/backend/src",
      "remoteRoot": "/app/src",
      "protocol": "inspector",
      "restart": true
    }
  ]
}
```

### Database Access
```bash
# Access MongoDB from your host machine
# Use the same MONGODB_URI from your .env.docker file

# Access Redis CLI
docker-compose exec redis redis-cli

# Check Redis connectivity
docker-compose exec redis redis-cli ping
```

## 📊 Health Monitoring

### Health Check Endpoints

```bash
# Basic health status
curl http://localhost:5000/api/health

# Liveness probe (container is running)
curl http://localhost:5000/api/health/live

# Readiness probe (ready to serve traffic)
curl http://localhost:5000/api/health/ready

# Database connectivity
curl http://localhost:5000/api/health/database

# Redis connectivity
curl http://localhost:5000/api/health/redis
```

### Service Status
```bash
# Check all services
docker-compose ps

# Check service health
docker-compose exec backend-dev curl -f http://localhost:5000/api/health/live

# View resource usage
docker stats
```

## 🚀 Common Commands

### Service Management
```bash
# Start specific profile
docker-compose --profile dev up
docker-compose --profile prod up
docker-compose --profile test up

# Stop all services
docker-compose down

# Stop and remove volumes (⚠️ data loss)
docker-compose down -v

# Restart specific service
docker-compose restart backend-dev
docker-compose restart redis
```

### Logs and Debugging
```bash
# View all logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend-dev
docker-compose logs -f redis

# Follow logs with timestamps
docker-compose logs -f -t

# Export logs to file
docker-compose logs > docker-logs.txt

# Access container shell
docker-compose exec backend-dev sh
docker-compose exec redis sh
```

### Build and Maintenance
```bash
# Rebuild without cache
docker-compose build --no-cache

# Pull latest base images
docker-compose pull

# Remove unused Docker resources
docker system prune -f

# Remove unused volumes
docker volume prune -f

# Remove unused images
docker image prune -f
```

## 🐛 Troubleshooting

### Common Issues

#### Port Already in Use
```bash
# Check what's using port 5000
lsof -i :5000  # macOS/Linux
netstat -ano | findstr :5000  # Windows

# Solution: Change port in docker-compose.yml
ports:
  - "5001:5000"  # Use different host port
```

#### MongoDB Connection Issues
```bash
# Verify MongoDB URI format
MONGODB_URI=********************************:port/database

# Test connection
docker-compose exec backend-dev npm run db:check

# Check MongoDB logs (if running locally)
docker logs mongodb-container
```

#### Redis Connection Issues
```bash
# Test Redis connectivity
docker-compose exec redis redis-cli ping

# Check Redis logs
docker-compose logs redis

# Verify Redis URL
echo $REDIS_URL
```

#### Build Failures
```bash
# Clear Docker build cache
docker builder prune -f

# Rebuild from scratch
docker-compose build --no-cache --pull

# Check available disk space
df -h  # Linux/macOS
```

#### Permission Issues (Linux/macOS)
```bash
# Fix file permissions
sudo chown -R $USER:$USER ./backend/logs
sudo chown -R $USER:$USER ./uploads

# Check container user
docker-compose exec backend-dev id
```

### Performance Issues

#### High Memory Usage
```bash
# Monitor resource usage
docker stats

# Adjust memory limits in docker-compose.yml
deploy:
  resources:
    limits:
      memory: 1G
```

#### Slow Build Times
```bash
# Enable BuildKit for faster builds
export DOCKER_BUILDKIT=1

# Use multi-stage build caching
docker build --target development .
```

## 🔒 Security Considerations

### Production Security
- All services run as non-root users
- Environment variables for sensitive data
- Network isolation between services
- Regular security updates in base images
- Minimal attack surface with Alpine Linux

### Development Security
- Debug ports only exposed in development
- Separate environment configurations
- Isolated Docker networks
- No production secrets in development

## 📈 Production Deployment

### Best Practices
1. Use specific image tags instead of `latest`
2. Set resource limits for all services
3. Configure proper restart policies
4. Use external secrets management
5. Enable log rotation and monitoring
6. Regular security updates
7. Backup strategies for persistent data

### Example Production Configuration
```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
  restart_policy:
    condition: on-failure
    delay: 5s
    max_attempts: 3
```

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
name: Docker Build and Test
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      
      - name: Create environment file
        run: |
          cp .env.test.example .env.test
          # Set test environment variables
          
      - name: Run tests
        run: |
          docker-compose --profile test up --build --abort-on-container-exit
          
      - name: Build production image
        run: |
          docker-compose --profile prod build
```

## 📚 Additional Resources

- [Backend Docker Documentation](./backend/DOCKER.md)
- [API Documentation](./backend/API.md)
- [TypeScript Migration Guide](./backend/TYPESCRIPT_MIGRATION.md)
- [Export Features Documentation](./backend/docs/EXPORT_FEATURES.md)
- [Medication API Documentation](./backend/docs/MEDICATION_API.md)

## 🆘 Getting Help

If you encounter issues:

1. Check the troubleshooting section above
2. Review service logs: `docker-compose logs -f`
3. Verify environment configuration
4. Check Docker and system resources
5. Consult the specific service documentation

For development questions, refer to the backend documentation in `./backend/README.md`.
