import type { Metadata } from "next";
import {DM_Sans } from "next/font/google";
import "./globals.css";


const dmSans = DM_Sans({
  subsets: ["latin"],
})

export const metadata: Metadata = {
  title: "Sentebale Angel Dev Tech",
   description: "A South African e-commerce SaaS platform empowering wig and hair vendors with mobile-friendly storefronts, vendor dashboards, and secure online payments.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${dmSans.className} antialiased`}
        suppressHydrationWarning
      >
        {children}
      </body>
    </html>
  );
}