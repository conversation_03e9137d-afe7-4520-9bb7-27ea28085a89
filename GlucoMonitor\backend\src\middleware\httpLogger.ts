import { Request, Response, NextFunction } from 'express';
import logger from '../utils/logger';
import { env } from '../config/env';

/**
 * HTTP request logging middleware
 * Logs all HTTP requests with detailed information
 */
export const httpLoggerMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    const startTime = Date.now();
    const requestId = req.headers['x-request-id'] as string;
    
    // Log incoming request
    logger.http(`${req.method} ${req.url} - Started`, {
        requestId,
        method: req.method,
        url: req.url,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        contentLength: req.get('Content-Length'),
        contentType: req.get('Content-Type'),
        userId: (req as any).user?.id,
        timestamp: new Date().toISOString()
    });

    // Override res.end to capture response details
    const originalEnd = res.end;
    res.end = function(chunk?: any, encoding?: any, cb?: any): Response {
        const duration = Date.now() - startTime;
        const statusCode = res.statusCode;

        // Determine log level based on status code
        let logLevel: 'http' | 'warn' | 'error' = 'http';
        if (statusCode >= 400 && statusCode < 500) {
            logLevel = 'warn';
        } else if (statusCode >= 500) {
            logLevel = 'error';
        }

        // Log response
        logger[logLevel](`${req.method} ${req.url} - ${statusCode}`, {
            requestId,
            method: req.method,
            url: req.url,
            statusCode,
            duration,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            userId: (req as any).user?.id,
            responseSize: res.get('Content-Length'),
            timestamp: new Date().toISOString()
        });

        // Log slow requests (> 1 second)
        if (duration > 1000) {
            logger.performance('Slow Request', duration, {
                requestId,
                method: req.method,
                url: req.url,
                statusCode,
                userId: (req as any).user?.id
            });
        }

        // Call original end method and return the result
        return originalEnd.call(this, chunk, encoding, cb);
    };

    next();
};

/**
 * Security logging middleware
 * Logs security-related events
 */
export const securityLoggerMiddleware = (req: Request, res: Response, next: NextFunction): void => {
    const requestId = req.headers['x-request-id'] as string;
    
    // Log authentication attempts
    if (req.url.includes('/auth/login') || req.url.includes('/auth/register')) {
        logger.security('Authentication attempt', {
            requestId,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            email: req.body?.email
        });
    }

    // Log admin access attempts
    if (req.url.includes('/admin') || req.headers.authorization) {
        logger.security('Admin access attempt', {
            requestId,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            hasAuth: !!req.headers.authorization
        });
    }

    // Log suspicious activity
    const suspiciousPatterns = [
        /\.\./,  // Directory traversal
        /<script/i,  // XSS attempts
        /union.*select/i,  // SQL injection
        /javascript:/i,  // JavaScript injection
        /eval\(/i,  // Code injection
    ];

    const fullUrl = req.url + JSON.stringify(req.body);
    if (suspiciousPatterns.some(pattern => pattern.test(fullUrl))) {
        logger.security('Suspicious request detected', {
            requestId,
            url: req.url,
            ip: req.ip,
            userAgent: req.get('User-Agent'),
            body: req.body,
            severity: 'high'
        });
    }

    next();
};

/**
 * Rate limiting logger
 * Logs rate limiting events
 */
export const rateLimitLogger = (req: Request, res: Response, next: NextFunction): void => {
    const requestId = req.headers['x-request-id'] as string;
    
    // Check for rate limit headers
    const rateLimitRemaining = res.get('X-RateLimit-Remaining');
    const rateLimitReset = res.get('X-RateLimit-Reset');
    
    if (rateLimitRemaining && parseInt(rateLimitRemaining) < 10) {
        logger.warn('Rate limit approaching', {
            requestId,
            ip: req.ip,
            remaining: rateLimitRemaining,
            reset: rateLimitReset,
            url: req.url
        });
    }

    next();
};

/**
 * Error response logger
 * Logs error responses with additional context
 */
export const errorResponseLogger = (req: Request, res: Response, next: NextFunction): void => {
    const originalJson = res.json;
    
    res.json = function(body: any) {
        const requestId = req.headers['x-request-id'] as string;
        
        // Log error responses
        if (body && body.success === false && res.statusCode >= 400) {
            logger.error('Error response sent', {
                requestId,
                statusCode: res.statusCode,
                error: body.error || body.message,
                url: req.url,
                method: req.method,
                ip: req.ip,
                userId: (req as any).user?.id
            });
        }

        return originalJson.call(this, body);
    };

    next();
};

export default httpLoggerMiddleware;
