import mongoose, { Connection } from 'mongoose';
import { env } from './env';

// Connection state tracking
interface ConnectionState {
    isConnected: boolean;
    isConnecting: boolean;
    connectionAttempts: number;
    lastConnectionAttempt: Date | null;
    lastError: Error | null;
}

class DatabaseManager {
    private static instance: DatabaseManager;
    private connectionState: ConnectionState = {
        isConnected: false,
        isConnecting: false,
        connectionAttempts: 0,
        lastConnectionAttempt: null,
        lastError: null
    };

    private readonly maxRetryAttempts = 5;
    private readonly retryDelay = 5000; // 5 seconds
    private readonly connectionTimeout = 30000; // 30 seconds
    private retryTimer: NodeJS.Timeout | null = null;

    private constructor() {
        this.setupEventListeners();
    }

    public static getInstance(): DatabaseManager {
        if (!DatabaseManager.instance) {
            DatabaseManager.instance = new DatabaseManager();
        }
        return DatabaseManager.instance;
    }

    private setupEventListeners(): void {
        // Connection successful
        mongoose.connection.on('connected', () => {
            console.log('✅ MongoDB connected successfully');
            this.connectionState.isConnected = true;
            this.connectionState.isConnecting = false;
            this.connectionState.connectionAttempts = 0;
            this.connectionState.lastError = null;
        });

        // Connection error
        mongoose.connection.on('error', (error: Error) => {
            console.error('❌ MongoDB connection error:', error.message);
            this.connectionState.lastError = error;
            this.connectionState.isConnected = false;
        });

        // Connection disconnected
        mongoose.connection.on('disconnected', () => {
            console.warn('⚠️  MongoDB disconnected');
            this.connectionState.isConnected = false;

            // Attempt reconnection if not in test environment
            if (env.NODE_ENV !== 'test' && !this.connectionState.isConnecting) {
                this.scheduleReconnection();
            }
        });

        // Connection reconnected
        mongoose.connection.on('reconnected', () => {
            console.log('🔄 MongoDB reconnected');
            this.connectionState.isConnected = true;
            this.connectionState.isConnecting = false;
        });

        // Process termination handlers
        process.on('SIGINT', this.gracefulShutdown.bind(this));
        process.on('SIGTERM', this.gracefulShutdown.bind(this));
    }

    public async connect(): Promise<void> {
        if (this.connectionState.isConnected) {
            console.log('📦 MongoDB already connected');
            return;
        }

        if (this.connectionState.isConnecting) {
            console.log('⏳ MongoDB connection already in progress');
            return;
        }

        this.connectionState.isConnecting = true;
        this.connectionState.connectionAttempts++;
        this.connectionState.lastConnectionAttempt = new Date();

        try {
            console.log(`🔌 Attempting MongoDB connection (attempt ${this.connectionState.connectionAttempts}/${this.maxRetryAttempts})`);

            const connectionOptions = {
                // Connection pool settings
                maxPoolSize: 10, // Maintain up to 10 socket connections
                minPoolSize: 2,  // Maintain at least 2 socket connections

                // Timeout settings
                serverSelectionTimeoutMS: this.connectionTimeout,
                socketTimeoutMS: 45000, // Close sockets after 45 seconds of inactivity
                connectTimeoutMS: this.connectionTimeout,

                // Buffering settings
                bufferCommands: false, // Disable mongoose buffering
                // bufferMaxEntries: 0,   // Deprecated option - removed

                // Heartbeat settings
                heartbeatFrequencyMS: 10000, // Send heartbeat every 10 seconds

                // Retry settings
                retryWrites: true,
                retryReads: true,

                // Additional settings
                maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
                compressors: ['zlib' as const], // Enable compression
            };

            const conn = await mongoose.connect(env.MONGODB_URI, connectionOptions);

            console.log(`✅ MongoDB Connected: ${conn.connection.host}:${conn.connection.port}`);
            console.log(`📊 Database: ${conn.connection.name}`);

        } catch (error) {
            this.connectionState.isConnecting = false;
            this.connectionState.lastError = error as Error;

            console.error(`❌ MongoDB connection failed (attempt ${this.connectionState.connectionAttempts}):`, (error as Error).message);

            if (this.connectionState.connectionAttempts < this.maxRetryAttempts) {
                this.scheduleReconnection();
            } else {
                console.error('💥 Maximum connection attempts reached. Exiting...');
                if (env.NODE_ENV !== 'test') {
                    process.exit(1);
                } else {
                    throw error;
                }
            }
        }
    }

    private scheduleReconnection(): void {
        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
        }

        const delay = this.retryDelay * Math.pow(2, this.connectionState.connectionAttempts - 1); // Exponential backoff
        console.log(`⏰ Scheduling reconnection in ${delay / 1000} seconds...`);

        this.retryTimer = setTimeout(() => {
            this.connect().catch(console.error);
        }, delay);
    }

    public getConnectionState(): ConnectionState {
        return { ...this.connectionState };
    }

    public isHealthy(): boolean {
        return this.connectionState.isConnected && mongoose.connection.readyState === 1;
    }

    public async healthCheck(): Promise<{ status: string; details: any }> {
        try {
            if (!this.isHealthy()) {
                return {
                    status: 'unhealthy',
                    details: {
                        connected: false,
                        readyState: mongoose.connection.readyState,
                        lastError: this.connectionState.lastError?.message,
                        connectionAttempts: this.connectionState.connectionAttempts
                    }
                };
            }

            // Perform a simple database operation to verify connectivity
            await mongoose.connection.db.admin().ping();

            return {
                status: 'healthy',
                details: {
                    connected: true,
                    host: mongoose.connection.host,
                    port: mongoose.connection.port,
                    database: mongoose.connection.name,
                    readyState: mongoose.connection.readyState,
                    connectionAttempts: this.connectionState.connectionAttempts
                }
            };
        } catch (error) {
            return {
                status: 'unhealthy',
                details: {
                    connected: false,
                    error: (error as Error).message,
                    readyState: mongoose.connection.readyState
                }
            };
        }
    }

    private async gracefulShutdown(): Promise<void> {
        console.log('🛑 Received shutdown signal. Closing MongoDB connection...');

        if (this.retryTimer) {
            clearTimeout(this.retryTimer);
        }

        try {
            await mongoose.connection.close();
            console.log('✅ MongoDB connection closed gracefully');
            process.exit(0);
        } catch (error) {
            console.error('❌ Error during MongoDB shutdown:', (error as Error).message);
            process.exit(1);
        }
    }
}

// Export singleton instance
const dbManager = DatabaseManager.getInstance();

// Legacy function for backward compatibility
const connectDB = async (): Promise<void> => {
    await dbManager.connect();
};

export default connectDB;
export { DatabaseManager, dbManager };
