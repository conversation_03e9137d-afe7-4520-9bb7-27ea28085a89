const mongoose = require('mongoose');
const { exportChartData } = require('./dist/controllers/glucose');
require('dotenv').config();

// Mock request and response objects
const createMockReq = (query = {}, user = null) => ({
  query,
  user: user || { _id: '6867ee2ae2291cd9bb40702b' },
  headers: {}
});

const createMockRes = () => {
  const res = {
    status: function(code) {
      this.statusCode = code;
      return this;
    },
    json: function(data) {
      this.data = data;
      console.log(`Response Status: ${this.statusCode || 200}`);
      console.log('Response Data:', JSON.stringify(data, null, 2));
      return this;
    },
    set: function(header, value) {
      this.headers = this.headers || {};
      this.headers[header] = value;
      return this;
    },
    setHeader: function(header, value) {
      this.headers = this.headers || {};
      this.headers[header] = value;
      return this;
    },
    send: function(data) {
      this.body = data;
      console.log(`Response Status: ${this.statusCode || 200}`);
      console.log('Response Body Length:', data ? data.length : 0);
      return this;
    }
  };
  return res;
};

async function testExportFunctionality() {
  try {
    console.log('🧪 Direct Export Function Test');
    console.log('================================\n');

    // Connect to MongoDB
    console.log('📡 Connecting to MongoDB...');
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('✅ Connected to MongoDB\n');

    // Check if glucose data exists for test user
    const GlucoseReading = require('./dist/models/GlucoseReading').default;
    const userId = '6867ee2ae2291cd9bb40702b';
    const glucoseCount = await GlucoseReading.countDocuments({ userId });
    console.log(`📊 Found ${glucoseCount} glucose readings for test user\n`);

    // Test 1: Basic JSON export
    console.log('📊 Test 1: Basic JSON Export');
    console.log('-----------------------------');
    const req1 = createMockReq({ format: 'json', chartType: 'line' });
    const res1 = createMockRes();
    
    await exportChartData(req1, res1);
    console.log('✅ JSON export test completed\n');

    // Test 2: CSV export
    console.log('📊 Test 2: CSV Export');
    console.log('----------------------');
    const req2 = createMockReq({ format: 'csv', chartType: 'bar' });
    const res2 = createMockRes();
    
    await exportChartData(req2, res2);
    console.log('✅ CSV export test completed\n');

    // Test 3: Export with date range
    console.log('📊 Test 3: Export with Date Range');
    console.log('----------------------------------');
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // 30 days ago
    const endDate = new Date();
    
    const req3 = createMockReq({ 
      format: 'json', 
      chartType: 'line',
      startDate: startDate.toISOString(),
      endDate: endDate.toISOString()
    });
    const res3 = createMockRes();
    
    await exportChartData(req3, res3);
    console.log('✅ Date range export test completed\n');

    console.log('🎉 All direct export tests completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Stack trace:', error.stack);
  } finally {
    await mongoose.disconnect();
    console.log('📡 Disconnected from MongoDB');
    process.exit(0);
  }
}

// Run the test
testExportFunctionality();
