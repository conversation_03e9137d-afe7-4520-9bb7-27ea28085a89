import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../constants/app_colors.dart';
import '../../models/glucose_reading.dart';

enum ChartType {
  line('Line Chart', Icons.show_chart),
  area('Area Chart', Icons.area_chart),
  bar('Bar Chart', Icons.bar_chart),
  scatter('Scatter Plot', Icons.scatter_plot);

  const ChartType(this.displayName, this.icon);
  final String displayName;
  final IconData icon;
}

class GlucoseChart extends StatefulWidget {
  final List<GlucoseReading> readings;
  final VoidCallback? onExport;

  const GlucoseChart({super.key, required this.readings, this.onExport});

  @override
  State<GlucoseChart> createState() => _GlucoseChartState();
}

class _GlucoseChartState extends State<GlucoseChart> {
  ChartType _selectedChartType = ChartType.area;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Glucose Trends',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              Row(
                children: [
                  // Chart type selector
                  PopupMenuButton<ChartType>(
                    icon: Icon(
                      _selectedChartType.icon,
                      color: Colors.grey[600],
                    ),
                    tooltip: 'Chart Type',
                    onSelected: (ChartType type) {
                      setState(() {
                        _selectedChartType = type;
                      });
                    },
                    itemBuilder:
                        (context) =>
                            ChartType.values.map((type) {
                              return PopupMenuItem<ChartType>(
                                value: type,
                                child: Row(
                                  children: [
                                    Icon(
                                      type.icon,
                                      size: 20,
                                      color:
                                          _selectedChartType == type
                                              ? AppColors.secondary
                                              : Colors.grey[600],
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      type.displayName,
                                      style: TextStyle(
                                        color:
                                            _selectedChartType == type
                                                ? AppColors.secondary
                                                : Colors.black87,
                                        fontWeight:
                                            _selectedChartType == type
                                                ? FontWeight.w600
                                                : FontWeight.normal,
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            }).toList(),
                  ),
                  if (widget.onExport != null)
                    IconButton(
                      onPressed: widget.onExport,
                      icon: const Icon(Icons.download),
                      color: Colors.grey[600],
                      tooltip: 'Export Chart',
                    ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 20),
          if (widget.readings.isEmpty) ...[
            _buildEmptyChart(),
          ] else ...[
            SizedBox(height: 250, child: _buildChart()),
          ],
        ],
      ),
    );
  }

  Widget _buildEmptyChart() {
    return SizedBox(
      height: 250,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.show_chart, size: 48, color: Colors.grey[400]),
            const SizedBox(height: 12),
            Text(
              'No data to display',
              style: TextStyle(color: Colors.grey[400], fontSize: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildChart() {
    final sortedReadings = List<GlucoseReading>.from(widget.readings)
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));

    switch (_selectedChartType) {
      case ChartType.line:
        return _buildLineChart(sortedReadings);
      case ChartType.area:
        return _buildAreaChart(sortedReadings);
      case ChartType.bar:
        return _buildBarChart(sortedReadings);
      case ChartType.scatter:
        return _buildScatterChart(sortedReadings);
    }
  }

  Widget _buildLineChart(List<GlucoseReading> sortedReadings) {
    final spots =
        sortedReadings.asMap().entries.map((entry) {
          return FlSpot(entry.key.toDouble(), entry.value.value);
        }).toList();

    final minY = 50.0;
    final maxY = 350.0;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 50,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.2),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          show: true,
          rightTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          topTitles: const AxisTitles(
            sideTitles: SideTitles(showTitles: false),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 1,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index >= 0 && index < sortedReadings.length) {
                  final reading = sortedReadings[index];
                  return SideTitleWidget(
                    meta: meta,
                    child: Text(
                      _formatChartDate(reading.timestamp),
                      style: TextStyle(color: Colors.grey[600], fontSize: 10),
                    ),
                  );
                }
                return const Text('');
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              interval: 50,
              getTitlesWidget: (value, meta) {
                return Text(
                  value.toInt().toString(),
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                );
              },
              reservedSize: 42,
            ),
          ),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        minX: 0,
        maxX: (spots.length - 1).toDouble(),
        minY: minY,
        maxY: maxY,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            color: AppColors.secondary,
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                final reading = sortedReadings[index];
                final category = reading.calculatedCategory;
                return FlDotCirclePainter(
                  radius: 4,
                  color: _getCategoryColor(category),
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(show: false),
          ),
        ],
        extraLinesData: ExtraLinesData(
          horizontalLines: [
            // Target range lines
            HorizontalLine(
              y: 80,
              color: AppColors.secondary.withValues(alpha: 0.6),
              strokeWidth: 2,
              dashArray: [5, 5],
              label: HorizontalLineLabel(
                show: true,
                labelResolver: (line) => 'Target Min (80)',
                style: TextStyle(color: AppColors.secondary, fontSize: 10),
              ),
            ),
            HorizontalLine(
              y: 180,
              color: AppColors.secondary.withValues(alpha: 0.6),
              strokeWidth: 2,
              dashArray: [5, 5],
              label: HorizontalLineLabel(
                show: true,
                labelResolver: (line) => 'Target Max (180)',
                style: TextStyle(color: AppColors.secondary, fontSize: 10),
              ),
            ),
          ],
        ),
        lineTouchData: LineTouchData(
          enabled: true,
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((LineBarSpot touchedSpot) {
                final reading = sortedReadings[touchedSpot.x.toInt()];
                return LineTooltipItem(
                  '${reading.value.toStringAsFixed(1)} mg/dL\n${_formatTooltipDate(reading.timestamp)}',
                  const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                );
              }).toList();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildAreaChart(List<GlucoseReading> sortedReadings) {
    final spots =
        sortedReadings.asMap().entries.map((entry) {
          return FlSpot(entry.key.toDouble(), entry.value.value);
        }).toList();

    final minY = 50.0;
    final maxY = 350.0;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          horizontalInterval: 50,
          verticalInterval: 1,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.2),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: _buildTitlesData(sortedReadings),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        minX: 0,
        maxX: (spots.length - 1).toDouble(),
        minY: minY,
        maxY: maxY,
        lineBarsData: [
          LineChartBarData(
            spots: spots,
            isCurved: true,
            gradient: LinearGradient(
              colors: [
                AppColors.secondary.withValues(alpha: 0.8),
                AppColors.secondary,
              ],
            ),
            barWidth: 3,
            isStrokeCapRound: true,
            dotData: FlDotData(
              show: true,
              getDotPainter: (spot, percent, barData, index) {
                final reading = sortedReadings[index];
                final category = reading.calculatedCategory;
                return FlDotCirclePainter(
                  radius: 4,
                  color: _getCategoryColor(category),
                  strokeWidth: 2,
                  strokeColor: Colors.white,
                );
              },
            ),
            belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                colors: [
                  AppColors.secondary.withValues(alpha: 0.3),
                  AppColors.secondary.withValues(alpha: 0.1),
                ],
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
            ),
          ),
        ],
        extraLinesData: _buildTargetLines(),
        lineTouchData: _buildLineTouchData(sortedReadings),
      ),
    );
  }

  Widget _buildBarChart(List<GlucoseReading> sortedReadings) {
    final barGroups =
        sortedReadings.asMap().entries.map((entry) {
          final reading = entry.value;
          final category = reading.calculatedCategory;
          return BarChartGroupData(
            x: entry.key,
            barRods: [
              BarChartRodData(
                toY: reading.value,
                color: _getCategoryColor(category),
                width: 16,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  topRight: Radius.circular(4),
                ),
              ),
            ],
          );
        }).toList();

    return BarChart(
      BarChartData(
        alignment: BarChartAlignment.spaceAround,
        maxY: 350,
        minY: 50,
        barGroups: barGroups,
        gridData: FlGridData(
          show: true,
          horizontalInterval: 50,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: _buildTitlesData(sortedReadings),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        extraLinesData: _buildTargetLines(),
        barTouchData: BarTouchData(
          enabled: true,
          touchTooltipData: BarTouchTooltipData(
            getTooltipItem: (group, groupIndex, rod, rodIndex) {
              final reading = sortedReadings[group.x];
              return BarTooltipItem(
                '${reading.value.toStringAsFixed(1)} mg/dL\n${_formatTooltipDate(reading.timestamp)}',
                const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildScatterChart(List<GlucoseReading> sortedReadings) {
    final spots =
        sortedReadings.asMap().entries.map((entry) {
          return ScatterSpot(entry.key.toDouble(), entry.value.value);
        }).toList();

    return ScatterChart(
      ScatterChartData(
        scatterSpots: spots,
        minX: 0,
        maxX: (spots.length - 1).toDouble(),
        minY: 50,
        maxY: 350,
        gridData: FlGridData(
          show: true,
          horizontalInterval: 50,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.3),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: Colors.grey.withValues(alpha: 0.2),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: _buildTitlesData(sortedReadings),
        borderData: FlBorderData(
          show: true,
          border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
        ),
        scatterTouchData: ScatterTouchData(
          enabled: true,
          touchTooltipData: ScatterTouchTooltipData(
            getTooltipItems: (ScatterSpot touchedSpot) {
              final reading = sortedReadings[touchedSpot.x.toInt()];
              return ScatterTooltipItem(
                '${reading.value.toStringAsFixed(1)} mg/dL\n${_formatTooltipDate(reading.timestamp)}',
                textStyle: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Color _getCategoryColor(GlucoseCategory category) {
    switch (category) {
      case GlucoseCategory.low:
        return Colors.blue;
      case GlucoseCategory.normal:
        return AppColors.secondary;
      case GlucoseCategory.high:
        return Colors.orange;
      case GlucoseCategory.veryHigh:
        return Colors.red;
    }
  }

  // Helper methods
  FlTitlesData _buildTitlesData(List<GlucoseReading> sortedReadings) {
    return FlTitlesData(
      show: true,
      rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      bottomTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 30,
          interval: 1,
          getTitlesWidget: (value, meta) {
            final index = value.toInt();
            if (index >= 0 && index < sortedReadings.length) {
              final reading = sortedReadings[index];
              return SideTitleWidget(
                meta: meta,
                child: Text(
                  _formatChartDate(reading.timestamp),
                  style: TextStyle(color: Colors.grey[600], fontSize: 10),
                ),
              );
            }
            return const Text('');
          },
        ),
      ),
      leftTitles: AxisTitles(
        sideTitles: SideTitles(
          showTitles: true,
          interval: 50,
          getTitlesWidget: (value, meta) {
            return Text(
              value.toInt().toString(),
              style: TextStyle(color: Colors.grey[600], fontSize: 12),
            );
          },
          reservedSize: 42,
        ),
      ),
    );
  }

  ExtraLinesData _buildTargetLines() {
    return ExtraLinesData(
      horizontalLines: [
        // Target range lines
        HorizontalLine(
          y: 80,
          color: AppColors.secondary.withValues(alpha: 0.6),
          strokeWidth: 2,
          dashArray: [5, 5],
          label: HorizontalLineLabel(
            show: true,
            labelResolver: (line) => 'Target Min (80)',
            style: TextStyle(color: AppColors.secondary, fontSize: 10),
          ),
        ),
        HorizontalLine(
          y: 180,
          color: AppColors.secondary.withValues(alpha: 0.6),
          strokeWidth: 2,
          dashArray: [5, 5],
          label: HorizontalLineLabel(
            show: true,
            labelResolver: (line) => 'Target Max (180)',
            style: TextStyle(color: AppColors.secondary, fontSize: 10),
          ),
        ),
      ],
    );
  }

  LineTouchData _buildLineTouchData(List<GlucoseReading> sortedReadings) {
    return LineTouchData(
      enabled: true,
      touchTooltipData: LineTouchTooltipData(
        getTooltipItems: (touchedSpots) {
          return touchedSpots.map((LineBarSpot touchedSpot) {
            final reading = sortedReadings[touchedSpot.x.toInt()];
            return LineTooltipItem(
              '${reading.value.toStringAsFixed(1)} mg/dL\n${_formatTooltipDate(reading.timestamp)}',
              const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            );
          }).toList();
        },
      ),
    );
  }

  String _formatChartDate(DateTime date) {
    return '${date.month}/${date.day}';
  }

  String _formatTooltipDate(DateTime date) {
    return '${date.month}/${date.day} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }
}
