# Flutter Environment Configuration Example
# Copy this file to .env and fill in your actual values
# NEVER commit .env files with real credentials to version control

# Backend Configuration
BACKEND_URL=http://localhost:5000/api

# App Configuration
APP_NAME=GlucoMonitor
APP_VERSION=1.0.0

# Development flags
DEBUG_MODE=true

# Google Vision API for AI Food Recognition
# To get your API key:
# 1. Go to Google Cloud Console (https://console.cloud.google.com/)
# 2. Create a new project or select existing one
# 3. Enable the Vision API
# 4. Create credentials (API Key)
# 5. Replace the value below with your actual API key
GOOGLE_VISION_API_KEY=your_google_vision_api_key_here

# Security Notes:
# - Keep your .env file private and never commit it to version control
# - Add .env to your .gitignore file
# - Use different API keys for development and production
# - Regularly rotate your API keys for security
