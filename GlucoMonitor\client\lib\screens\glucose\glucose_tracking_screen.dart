import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:screenshot/screenshot.dart';
import '../../constants/app_colors.dart';
import '../../models/glucose_reading.dart';
import '../../services/glucose_service.dart';
import '../../providers/auth_provider.dart';

import '../../widgets/glucose/glucose_header.dart';
import '../../widgets/glucose/time_period_selector.dart';
import '../../widgets/glucose/statistics_dashboard.dart';
import '../../widgets/glucose/glucose_chart.dart';
import '../../widgets/glucose/recent_readings_list.dart';
import '../../widgets/glucose/add_reading_modal.dart';
import '../../widgets/export_options_dialog.dart';

class GlucoseTrackingScreen extends StatefulWidget {
  const GlucoseTrackingScreen({super.key});

  @override
  State<GlucoseTrackingScreen> createState() => _GlucoseTrackingScreenState();
}

class _GlucoseTrackingScreenState extends State<GlucoseTrackingScreen> {
  List<GlucoseReading> _readings = [];
  List<GlucoseReading> _filteredReadings = [];
  bool _isLoading = true;
  String? _error;
  TimePeriod _selectedPeriod = TimePeriod.week;
  final ScreenshotController _screenshotController = ScreenshotController();

  @override
  void initState() {
    super.initState();
    _loadReadings();
  }

  Future<void> _loadReadings() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final authProvider = Provider.of<AuthProvider>(context, listen: false);
      final token = authProvider.backendToken;

      if (token == null) {
        throw Exception('Authentication required');
      }

      final response = await GlucoseService.getGlucoseReadings(
        token: token,
        limit: 50,
        sortOrder: 'desc',
      );

      setState(() {
        _readings = response.readings;
        _filteredReadings = _filterReadingsByPeriod(_readings);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  List<GlucoseReading> _filterReadingsByPeriod(List<GlucoseReading> readings) {
    final now = DateTime.now();
    final cutoffDate = now.subtract(Duration(days: _selectedPeriod.days));

    return readings
        .where((reading) => reading.timestamp.isAfter(cutoffDate))
        .toList();
  }

  void _onPeriodChanged(TimePeriod period) {
    setState(() {
      _selectedPeriod = period;
      _filteredReadings = _filterReadingsByPeriod(_readings);
    });
  }

  Future<void> _addNewReading() async {
    final result = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddReadingModal(),
    );

    if (result == true) {
      _loadReadings(); // Refresh the list
    }
  }

  Future<void> _editReading(GlucoseReading reading) async {
    final result = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => AddReadingModal(editReading: reading),
    );

    if (result == true) {
      _loadReadings(); // Refresh the list
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Glucose Tracking'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onBackground,
        actions: [
          IconButton(icon: const Icon(Icons.refresh), onPressed: _loadReadings),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Error loading readings',
              style: TextStyle(fontSize: 18, color: Colors.grey[600]),
            ),
            const SizedBox(height: 8),
            Text(
              _error!,
              style: TextStyle(color: Colors.grey[500]),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _loadReadings,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadReadings,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Header Section with Current Status
            GlucoseHeader(
              latestReading: _readings.isNotEmpty ? _readings.first : null,
              onAddReading: _addNewReading,
            ),
            const SizedBox(height: 20),

            // Time Period Selection
            TimePeriodSelector(
              selectedPeriod: _selectedPeriod,
              onPeriodChanged: _onPeriodChanged,
            ),
            const SizedBox(height: 20),

            // Statistics Dashboard
            StatisticsDashboard(readings: _filteredReadings),
            const SizedBox(height: 20),

            // Interactive Chart
            Screenshot(
              controller: _screenshotController,
              child: GlucoseChart(
                readings: _filteredReadings,
                onExport: _exportChart,
              ),
            ),
            const SizedBox(height: 20),

            // Recent Readings List
            RecentReadingsList(
              readings: _filteredReadings,
              onReadingTap: _editReading,
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _exportChart() async {
    // Get the current date range based on selected period
    final now = DateTime.now();
    DateTime? startDate;
    DateTime? endDate = now;

    switch (_selectedPeriod) {
      case TimePeriod.today:
        startDate = DateTime(now.year, now.month, now.day);
        break;
      case TimePeriod.week:
        startDate = now.subtract(const Duration(days: 7));
        break;
      case TimePeriod.month:
        startDate = now.subtract(const Duration(days: 30));
        break;
      case TimePeriod.quarter:
        startDate = now.subtract(const Duration(days: 90));
        break;
    }

    // Show export options dialog
    showDialog(
      context: context,
      builder:
          (context) => ExportOptionsDialog(
            defaultStartDate: startDate,
            defaultEndDate: endDate,
            defaultChartType: 'line', // Default chart type
          ),
    );
  }
}
