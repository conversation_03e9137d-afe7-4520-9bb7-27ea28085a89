/**
 * Environment configuration validation and type safety
 * Ensures all required environment variables are present and valid
 */

interface EnvConfig {
  NODE_ENV: 'development' | 'production' | 'test';
  PORT: number;
  MONGODB_URI: string;
  JWT_SECRET: string;
  JWT_EXPIRE: string;
  REDIS_URL?: string;
  SMSPORTAL_CLIENT_ID?: string;
  SMSPORTAL_API_SECRET?: string;
  SMSPORTAL_SENDER?: string;
}

/**
 * Validates and parses environment variables
 * Throws an error if required variables are missing or invalid
 */
function validateEnv(): EnvConfig {
  const errors: string[] = [];

  // Required variables
  const requiredVars = {
    MONGODB_URI: process.env.MONGODB_URI,
    JWT_SECRET: process.env.JWT_SECRET,
  };

  // Check for missing required variables
  Object.entries(requiredVars).forEach(([key, value]) => {
    if (!value) {
      errors.push(`Missing required environment variable: ${key}`);
    }
  });

  // Validate JWT_SECRET length
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length < 32) {
    errors.push('JWT_SECRET must be at least 32 characters long');
  }

  // Validate MONGODB_URI format
  if (process.env.MONGODB_URI && !process.env.MONGODB_URI.startsWith('mongodb')) {
    errors.push('MONGODB_URI must be a valid MongoDB connection string');
  }

  // Validate PORT if provided
  const port = process.env.PORT ? parseInt(process.env.PORT, 10) : 5000;
  if (isNaN(port) || port < 1 || port > 65535) {
    errors.push('PORT must be a valid port number (1-65535)');
  }

  // Validate NODE_ENV
  const nodeEnv = process.env.NODE_ENV || 'development';
  if (!['development', 'production', 'test'].includes(nodeEnv)) {
    errors.push('NODE_ENV must be one of: development, production, test');
  }

  // Validate REDIS_URL format if provided
  if (process.env.REDIS_URL && !process.env.REDIS_URL.startsWith('redis://')) {
    errors.push('REDIS_URL must be a valid Redis connection string');
  }

  // If there are validation errors, throw them
  if (errors.length > 0) {
    throw new Error(`Environment validation failed:\n${errors.join('\n')}`);
  }

  return {
    NODE_ENV: nodeEnv as 'development' | 'production' | 'test',
    PORT: port,
    MONGODB_URI: process.env.MONGODB_URI!,
    JWT_SECRET: process.env.JWT_SECRET!,
    JWT_EXPIRE: process.env.JWT_EXPIRE || '30d',
    REDIS_URL: process.env.REDIS_URL,
    SMSPORTAL_CLIENT_ID: process.env.SMSPORTAL_CLIENT_ID,
    SMSPORTAL_API_SECRET: process.env.SMSPORTAL_API_SECRET,
    SMSPORTAL_SENDER: process.env.SMSPORTAL_SENDER,
  };
}

// Validate environment on module load
export const env = validateEnv();

// Log configuration in development
if (env.NODE_ENV === 'development') {
  console.log('Environment Configuration:');
  console.log(`- NODE_ENV: ${env.NODE_ENV}`);
  console.log(`- PORT: ${env.PORT}`);
  console.log(`- MONGODB_URI: ${env.MONGODB_URI.replace(/\/\/.*@/, '//***:***@')}`); // Hide credentials
  console.log(`- JWT_SECRET: ${env.JWT_SECRET.substring(0, 8)}...`); // Hide secret
  console.log(`- JWT_EXPIRE: ${env.JWT_EXPIRE}`);
  console.log(`- REDIS_URL: ${env.REDIS_URL ? 'configured' : 'not configured'}`);
  console.log(`- SMS Service: ${env.SMSPORTAL_CLIENT_ID ? 'configured' : 'not configured'}`);
}

export default env;
