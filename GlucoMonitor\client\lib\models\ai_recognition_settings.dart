import 'package:flutter/material.dart';

/// Model for AI Food Recognition settings and preferences
class AIRecognitionSettings {
  // Confidence thresholds
  final double minimumConfidence;
  final double highConfidenceThreshold;
  final double mediumConfidenceThreshold;
  
  // Recognition preferences
  final bool enableOfflineFirst;
  final bool enableMultiDetection;
  final bool enablePortionEstimation;
  final bool enableRealtimeAnalysis;
  
  // Performance settings
  final int maxDetectionResults;
  final Duration recognitionTimeout;
  final bool enableImageOptimization;
  final double imageQualityThreshold;
  
  // User experience settings
  final bool showConfidenceIndicators;
  final bool enableHapticFeedback;
  final bool autoSaveRecognizedFoods;
  final bool enableLearningMode;
  
  // Advanced settings
  final bool enableDebugMode;
  final bool logRecognitionAttempts;
  final String preferredApiProvider;
  final Map<String, double> customFoodThresholds;

  const AIRecognitionSettings({
    // Default confidence thresholds
    this.minimumConfidence = 0.6,
    this.highConfidenceThreshold = 0.85,
    this.mediumConfidenceThreshold = 0.7,
    
    // Default recognition preferences
    this.enableOfflineFirst = true,
    this.enableMultiDetection = false,
    this.enablePortionEstimation = true,
    this.enableRealtimeAnalysis = true,
    
    // Default performance settings
    this.maxDetectionResults = 5,
    this.recognitionTimeout = const Duration(seconds: 10),
    this.enableImageOptimization = true,
    this.imageQualityThreshold = 0.5,
    
    // Default UX settings
    this.showConfidenceIndicators = true,
    this.enableHapticFeedback = true,
    this.autoSaveRecognizedFoods = false,
    this.enableLearningMode = true,
    
    // Default advanced settings
    this.enableDebugMode = false,
    this.logRecognitionAttempts = true,
    this.preferredApiProvider = 'google_vision',
    this.customFoodThresholds = const {},
  });

  /// Get confidence level based on score
  ConfidenceLevel getConfidenceLevel(double confidence) {
    if (confidence >= highConfidenceThreshold) {
      return ConfidenceLevel.high;
    } else if (confidence >= mediumConfidenceThreshold) {
      return ConfidenceLevel.medium;
    } else if (confidence >= minimumConfidence) {
      return ConfidenceLevel.low;
    } else {
      return ConfidenceLevel.rejected;
    }
  }

  /// Check if confidence meets minimum threshold
  bool isConfidenceAcceptable(double confidence) {
    return confidence >= minimumConfidence;
  }

  /// Get custom threshold for specific food if available
  double getThresholdForFood(String foodName) {
    return customFoodThresholds[foodName.toLowerCase()] ?? minimumConfidence;
  }

  /// Copy with new values
  AIRecognitionSettings copyWith({
    double? minimumConfidence,
    double? highConfidenceThreshold,
    double? mediumConfidenceThreshold,
    bool? enableOfflineFirst,
    bool? enableMultiDetection,
    bool? enablePortionEstimation,
    bool? enableRealtimeAnalysis,
    int? maxDetectionResults,
    Duration? recognitionTimeout,
    bool? enableImageOptimization,
    double? imageQualityThreshold,
    bool? showConfidenceIndicators,
    bool? enableHapticFeedback,
    bool? autoSaveRecognizedFoods,
    bool? enableLearningMode,
    bool? enableDebugMode,
    bool? logRecognitionAttempts,
    String? preferredApiProvider,
    Map<String, double>? customFoodThresholds,
  }) {
    return AIRecognitionSettings(
      minimumConfidence: minimumConfidence ?? this.minimumConfidence,
      highConfidenceThreshold: highConfidenceThreshold ?? this.highConfidenceThreshold,
      mediumConfidenceThreshold: mediumConfidenceThreshold ?? this.mediumConfidenceThreshold,
      enableOfflineFirst: enableOfflineFirst ?? this.enableOfflineFirst,
      enableMultiDetection: enableMultiDetection ?? this.enableMultiDetection,
      enablePortionEstimation: enablePortionEstimation ?? this.enablePortionEstimation,
      enableRealtimeAnalysis: enableRealtimeAnalysis ?? this.enableRealtimeAnalysis,
      maxDetectionResults: maxDetectionResults ?? this.maxDetectionResults,
      recognitionTimeout: recognitionTimeout ?? this.recognitionTimeout,
      enableImageOptimization: enableImageOptimization ?? this.enableImageOptimization,
      imageQualityThreshold: imageQualityThreshold ?? this.imageQualityThreshold,
      showConfidenceIndicators: showConfidenceIndicators ?? this.showConfidenceIndicators,
      enableHapticFeedback: enableHapticFeedback ?? this.enableHapticFeedback,
      autoSaveRecognizedFoods: autoSaveRecognizedFoods ?? this.autoSaveRecognizedFoods,
      enableLearningMode: enableLearningMode ?? this.enableLearningMode,
      enableDebugMode: enableDebugMode ?? this.enableDebugMode,
      logRecognitionAttempts: logRecognitionAttempts ?? this.logRecognitionAttempts,
      preferredApiProvider: preferredApiProvider ?? this.preferredApiProvider,
      customFoodThresholds: customFoodThresholds ?? this.customFoodThresholds,
    );
  }

  /// Convert to JSON for storage
  Map<String, dynamic> toJson() {
    return {
      'minimumConfidence': minimumConfidence,
      'highConfidenceThreshold': highConfidenceThreshold,
      'mediumConfidenceThreshold': mediumConfidenceThreshold,
      'enableOfflineFirst': enableOfflineFirst,
      'enableMultiDetection': enableMultiDetection,
      'enablePortionEstimation': enablePortionEstimation,
      'enableRealtimeAnalysis': enableRealtimeAnalysis,
      'maxDetectionResults': maxDetectionResults,
      'recognitionTimeoutSeconds': recognitionTimeout.inSeconds,
      'enableImageOptimization': enableImageOptimization,
      'imageQualityThreshold': imageQualityThreshold,
      'showConfidenceIndicators': showConfidenceIndicators,
      'enableHapticFeedback': enableHapticFeedback,
      'autoSaveRecognizedFoods': autoSaveRecognizedFoods,
      'enableLearningMode': enableLearningMode,
      'enableDebugMode': enableDebugMode,
      'logRecognitionAttempts': logRecognitionAttempts,
      'preferredApiProvider': preferredApiProvider,
      'customFoodThresholds': customFoodThresholds,
    };
  }

  /// Create from JSON
  factory AIRecognitionSettings.fromJson(Map<String, dynamic> json) {
    return AIRecognitionSettings(
      minimumConfidence: (json['minimumConfidence'] as num?)?.toDouble() ?? 0.6,
      highConfidenceThreshold: (json['highConfidenceThreshold'] as num?)?.toDouble() ?? 0.85,
      mediumConfidenceThreshold: (json['mediumConfidenceThreshold'] as num?)?.toDouble() ?? 0.7,
      enableOfflineFirst: json['enableOfflineFirst'] as bool? ?? true,
      enableMultiDetection: json['enableMultiDetection'] as bool? ?? false,
      enablePortionEstimation: json['enablePortionEstimation'] as bool? ?? true,
      enableRealtimeAnalysis: json['enableRealtimeAnalysis'] as bool? ?? true,
      maxDetectionResults: json['maxDetectionResults'] as int? ?? 5,
      recognitionTimeout: Duration(seconds: json['recognitionTimeoutSeconds'] as int? ?? 10),
      enableImageOptimization: json['enableImageOptimization'] as bool? ?? true,
      imageQualityThreshold: (json['imageQualityThreshold'] as num?)?.toDouble() ?? 0.5,
      showConfidenceIndicators: json['showConfidenceIndicators'] as bool? ?? true,
      enableHapticFeedback: json['enableHapticFeedback'] as bool? ?? true,
      autoSaveRecognizedFoods: json['autoSaveRecognizedFoods'] as bool? ?? false,
      enableLearningMode: json['enableLearningMode'] as bool? ?? true,
      enableDebugMode: json['enableDebugMode'] as bool? ?? false,
      logRecognitionAttempts: json['logRecognitionAttempts'] as bool? ?? true,
      preferredApiProvider: json['preferredApiProvider'] as String? ?? 'google_vision',
      customFoodThresholds: Map<String, double>.from(
        json['customFoodThresholds'] as Map<String, dynamic>? ?? {},
      ),
    );
  }

  /// Get default settings
  static AIRecognitionSettings get defaultSettings => const AIRecognitionSettings();

  /// Get conservative settings (higher thresholds)
  static AIRecognitionSettings get conservativeSettings => const AIRecognitionSettings(
    minimumConfidence: 0.8,
    highConfidenceThreshold: 0.95,
    mediumConfidenceThreshold: 0.85,
    enableMultiDetection: false,
    maxDetectionResults: 3,
  );

  /// Get aggressive settings (lower thresholds)
  static AIRecognitionSettings get aggressiveSettings => const AIRecognitionSettings(
    minimumConfidence: 0.4,
    highConfidenceThreshold: 0.7,
    mediumConfidenceThreshold: 0.55,
    enableMultiDetection: true,
    maxDetectionResults: 10,
  );
}

/// Enum for confidence levels
enum ConfidenceLevel {
  high,
  medium,
  low,
  rejected;

  String get displayName {
    switch (this) {
      case ConfidenceLevel.high:
        return 'High Confidence';
      case ConfidenceLevel.medium:
        return 'Medium Confidence';
      case ConfidenceLevel.low:
        return 'Low Confidence';
      case ConfidenceLevel.rejected:
        return 'Below Threshold';
    }
  }

  Color get color {
    switch (this) {
      case ConfidenceLevel.high:
        return Colors.green;
      case ConfidenceLevel.medium:
        return Colors.orange;
      case ConfidenceLevel.low:
        return Colors.yellow;
      case ConfidenceLevel.rejected:
        return Colors.red;
    }
  }

  IconData get icon {
    switch (this) {
      case ConfidenceLevel.high:
        return Icons.check_circle;
      case ConfidenceLevel.medium:
        return Icons.warning;
      case ConfidenceLevel.low:
        return Icons.help_outline;
      case ConfidenceLevel.rejected:
        return Icons.cancel;
    }
  }
}
