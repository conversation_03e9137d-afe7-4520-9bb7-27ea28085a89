{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc || true", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "dev:watch": "nodemon --watch src --ext ts --exec ts-node src/index.ts", "clean": "rm -rf dist", "prebuild": "npm run clean", "type-check": "tsc --noEmit", "lint": "echo '<PERSON><PERSON> not configured yet'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "docker:build": "docker build -t glucomonitor-backend .", "docker:run": "docker run -p 5000:5000 glucomonitor-backend", "seed:foods": "ts-node src/seeders/enhancedFoodDatabaseSeeder.ts", "seed:foods:force": "ts-node src/seeders/enhancedFoodDatabaseSeeder.ts --force", "seed:foods:clear": "ts-node src/seeders/enhancedFoodDatabaseSeeder.ts clear", "dev:debug": "NODE_ENV=development DEBUG=* nodemon src/index.ts", "dev:memory": "NODE_ENV=development node --expose-gc --max-old-space-size=4096 -r ts-node/register src/index.ts", "dev:inspect": "NODE_ENV=development node --inspect -r ts-node/register src/index.ts", "logs:tail": "tail -f logs/app.log", "logs:error": "tail -f logs/error.log", "logs:clear": "rm -rf logs/*.log", "health:check": "curl http://localhost:5000/api/health", "health:live": "curl http://localhost:5000/api/health/live", "health:ready": "curl http://localhost:5000/api/health/ready", "health:redis": "curl http://localhost:5000/api/health/redis", "db:check": "curl http://localhost:5000/api/health/database", "docker:dev": "docker-compose --profile dev up --build", "docker:prod": "docker-compose --profile prod up --build", "docker:test": "docker-compose --profile test up --build", "docker:logs": "docker-compose logs -f", "docker:clean": "docker-compose down -v && docker system prune -f"}, "keywords": ["typescript", "nodejs", "express", "mongodb", "redis", "jwt", "sms", "glucose-monitoring", "diabetes", "healthcare"], "author": "GlucoMonitor Team", "license": "ISC", "description": "TypeScript-based Node.js backend API for GlucoMonitor glucose monitoring application with JWT authentication, SMS verification, and MongoDB integration", "dependencies": {"@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/pdfkit": "^0.14.0", "axios": "^1.9.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "dropbox": "^10.34.0", "exceljs": "^4.4.0", "express": "^5.1.0", "firebase-admin": "^13.4.0", "fs": "^0.0.1-security", "googleapis": "^150.0.1", "jsonwebtoken": "^9.0.2", "mongoose": "^8.15.1", "node-cron": "^4.2.0", "node-fetch": "^2.7.0", "nodemailer": "^7.0.4", "path": "^0.12.7", "pdfkit": "^0.17.1", "rate-limiter-flexible": "^7.1.1", "redis": "^5.1.1", "sharp": "^0.34.3", "twilio": "^5.6.1", "uuid": "^9.0.0", "winston": "^3.11.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/sharp": "^0.31.1", "@types/supertest": "^6.0.2", "@types/uuid": "^9.0.0", "jest": "^29.7.0", "mongodb-memory-server": "^9.1.6", "nodemon": "^3.1.10", "supertest": "^6.3.4", "ts-jest": "^29.1.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}