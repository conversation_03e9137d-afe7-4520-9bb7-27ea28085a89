<!DOCTYPE html>
<html>
<head>
    <title>Favicon Generator</title>
</head>
<body>
    <canvas id="canvas" width="64" height="64" style="border:1px solid #000;"></canvas>
    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        
        // Set background
        ctx.fillStyle = '#4F46E5'; // Indigo color
        ctx.fillRect(0, 0, 64, 64);
        
        // Draw text
        ctx.fillStyle = 'white';
        ctx.font = '50px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('✓', 32, 32);
        
        // Convert to favicon
        const link = document.createElement('link');
        link.type = 'image/x-icon';
        link.rel = 'shortcut icon';
        link.href = canvas.toDataURL("image/x-icon");
        document.head.appendChild(link);
        
        // Output data URL
        console.log(canvas.toDataURL());
    </script>
</body>
</html> 