import request from 'supertest';
import mongoose from 'mongoose';
import app from '../index';
import User from '../models/User';
import Medication, { MedicationType, MedicationFrequency } from '../models/Medication';
import MedicationReminder, { ReminderStatus } from '../models/MedicationReminder';

describe('Medication API', () => {
    let authToken: string;
    let userId: string;
    let medicationId: string;

    beforeAll(async () => {
        // Create test user and get auth token
        const userData = {
            email: '<EMAIL>',
            password: 'testpassword123',
            phoneNumber: '+27123456789',
            language: 'en'
        };

        const userResponse = await request(app)
            .post('/api/auth/register')
            .send(userData);

        expect(userResponse.status).toBe(201);
        userId = userResponse.body.userId;

        // Now login to get the auth token
        const loginResponse = await request(app)
            .post('/api/auth/login')
            .send({
                email: userData.email,
                password: userData.password
            });

        expect(loginResponse.status).toBe(200);
        authToken = loginResponse.body.token;

        // Verify phone to enable notifications
        await User.findByIdAndUpdate(userId, { isPhoneVerified: true });
    });

    afterAll(async () => {
        // Clean up test data
        await User.findByIdAndDelete(userId);
        await Medication.deleteMany({ userId });
        await MedicationReminder.deleteMany({ userId });
    });

    beforeEach(async () => {
        // Clean up medications and reminders before each test
        await Medication.deleteMany({ userId });
        await MedicationReminder.deleteMany({ userId });
    });

    describe('POST /api/medications', () => {
        it('should create a new medication', async () => {
            const medicationData = {
                name: 'Metformin',
                brandName: 'Glucophage',
                type: MedicationType.TABLET,
                dosage: 500,
                dosageUnit: 'mg',
                frequency: MedicationFrequency.TWICE_DAILY,
                startDate: new Date(),
                reminderEnabled: true,
                reminderMinutesBefore: 15,
                notes: 'Take with food'
            };

            const response = await request(app)
                .post('/api/medications')
                .set('Authorization', `Bearer ${authToken}`)
                .send(medicationData);

            expect(response.status).toBe(201);
            expect(response.body.success).toBe(true);
            expect(response.body.data.name).toBe(medicationData.name);
            expect(response.body.data.brandName).toBe(medicationData.brandName);
            expect(response.body.data.dosage).toBe(medicationData.dosage);

            medicationId = response.body.data._id;
        });

        it('should create medication with custom times', async () => {
            const medicationData = {
                name: 'Insulin',
                type: MedicationType.INJECTION,
                dosage: 10,
                dosageUnit: 'units',
                frequency: MedicationFrequency.CUSTOM,
                customTimes: [
                    { time: '07:00', label: 'Before breakfast' },
                    { time: '12:00', label: 'Before lunch' },
                    { time: '18:00', label: 'Before dinner' }
                ],
                startDate: new Date(),
                reminderEnabled: true
            };

            const response = await request(app)
                .post('/api/medications')
                .set('Authorization', `Bearer ${authToken}`)
                .send(medicationData);

            expect(response.status).toBe(201);
            expect(response.body.data.customTimes).toHaveLength(3);
            expect(response.body.data.customTimes[0].time).toBe('07:00');
        });

        it('should fail validation for invalid medication data', async () => {
            const invalidData = {
                name: '', // Empty name
                type: 'invalid_type',
                dosage: -1, // Negative dosage
                frequency: MedicationFrequency.CUSTOM,
                customTimes: [] // Empty custom times for custom frequency
            };

            const response = await request(app)
                .post('/api/medications')
                .set('Authorization', `Bearer ${authToken}`)
                .send(invalidData);

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
            expect(response.body.errors).toBeDefined();
        });

        it('should require authentication', async () => {
            const medicationData = {
                name: 'Test Medication',
                type: MedicationType.TABLET,
                dosage: 100,
                dosageUnit: 'mg',
                frequency: MedicationFrequency.ONCE_DAILY
            };

            const response = await request(app)
                .post('/api/medications')
                .send(medicationData);

            expect(response.status).toBe(401);
        });
    });

    describe('GET /api/medications', () => {
        beforeEach(async () => {
            // Create test medications
            const medications = [
                {
                    userId,
                    name: 'Medication 1',
                    type: MedicationType.TABLET,
                    dosage: 100,
                    dosageUnit: 'mg',
                    frequency: MedicationFrequency.ONCE_DAILY,
                    isActive: true
                },
                {
                    userId,
                    name: 'Medication 2',
                    type: MedicationType.CAPSULE,
                    dosage: 200,
                    dosageUnit: 'mg',
                    frequency: MedicationFrequency.TWICE_DAILY,
                    isActive: false
                }
            ];

            await Medication.insertMany(medications);
        });

        it('should get all medications for user', async () => {
            const response = await request(app)
                .get('/api/medications')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data).toHaveLength(2);
            expect(response.body.count).toBe(2);
        });

        it('should filter active medications', async () => {
            const response = await request(app)
                .get('/api/medications?active=true')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.data).toHaveLength(1);
            expect(response.body.data[0].isActive).toBe(true);
        });

        it('should support pagination', async () => {
            const response = await request(app)
                .get('/api/medications?page=1&limit=1')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.data).toHaveLength(1);
            expect(response.body.page).toBe(1);
            expect(response.body.pages).toBe(2);
        });
    });

    describe('GET /api/medications/:id', () => {
        beforeEach(async () => {
            const medication = await Medication.create({
                userId,
                name: 'Test Medication',
                type: MedicationType.TABLET,
                dosage: 100,
                dosageUnit: 'mg',
                frequency: MedicationFrequency.ONCE_DAILY
            });
            medicationId = (medication._id as mongoose.Types.ObjectId).toString();
        });

        it('should get single medication', async () => {
            const response = await request(app)
                .get(`/api/medications/${medicationId}`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data._id).toBe(medicationId);
            expect(response.body.data.name).toBe('Test Medication');
        });

        it('should return 404 for non-existent medication', async () => {
            const fakeId = new mongoose.Types.ObjectId();
            const response = await request(app)
                .get(`/api/medications/${fakeId}`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(404);
            expect(response.body.success).toBe(false);
        });

        it('should return 400 for invalid medication ID', async () => {
            const response = await request(app)
                .get('/api/medications/invalid-id')
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(400);
            expect(response.body.success).toBe(false);
        });
    });

    describe('PUT /api/medications/:id', () => {
        beforeEach(async () => {
            const medication = await Medication.create({
                userId,
                name: 'Test Medication',
                type: MedicationType.TABLET,
                dosage: 100,
                dosageUnit: 'mg',
                frequency: MedicationFrequency.ONCE_DAILY,
                reminderEnabled: true
            });
            medicationId = (medication._id as mongoose.Types.ObjectId).toString();
        });

        it('should update medication', async () => {
            const updateData = {
                name: 'Updated Medication',
                dosage: 200,
                notes: 'Updated notes'
            };

            const response = await request(app)
                .put(`/api/medications/${medicationId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);
            expect(response.body.data.name).toBe(updateData.name);
            expect(response.body.data.dosage).toBe(updateData.dosage);
            expect(response.body.data.notes).toBe(updateData.notes);
        });

        it('should regenerate reminders when frequency changes', async () => {
            const updateData = {
                frequency: MedicationFrequency.THREE_TIMES_DAILY
            };

            const response = await request(app)
                .put(`/api/medications/${medicationId}`)
                .set('Authorization', `Bearer ${authToken}`)
                .send(updateData);

            expect(response.status).toBe(200);
            expect(response.body.data.frequency).toBe(updateData.frequency);

            // Check that reminders were regenerated
            const reminders = await MedicationReminder.find({ medicationId });
            expect(reminders.length).toBeGreaterThan(0);
        });
    });

    describe('DELETE /api/medications/:id', () => {
        beforeEach(async () => {
            const medication = await Medication.create({
                userId,
                name: 'Test Medication',
                type: MedicationType.TABLET,
                dosage: 100,
                dosageUnit: 'mg',
                frequency: MedicationFrequency.ONCE_DAILY
            });
            medicationId = (medication._id as mongoose.Types.ObjectId).toString();

            // Create some reminders
            await MedicationReminder.create({
                userId,
                medicationId,
                scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
                status: ReminderStatus.PENDING
            });
        });

        it('should delete medication and associated reminders', async () => {
            const response = await request(app)
                .delete(`/api/medications/${medicationId}`)
                .set('Authorization', `Bearer ${authToken}`);

            expect(response.status).toBe(200);
            expect(response.body.success).toBe(true);

            // Verify medication is deleted
            const medication = await Medication.findById(medicationId);
            expect(medication).toBeNull();

            // Verify reminders are deleted
            const reminders = await MedicationReminder.find({ medicationId });
            expect(reminders).toHaveLength(0);
        });
    });

    describe('Medication Reminders', () => {
        let reminderId: string;

        beforeEach(async () => {
            // Create test medication
            const medication = await Medication.create({
                userId,
                name: 'Test Medication',
                type: MedicationType.TABLET,
                dosage: 100,
                dosageUnit: 'mg',
                frequency: MedicationFrequency.TWICE_DAILY,
                reminderEnabled: true
            });
            medicationId = (medication._id as mongoose.Types.ObjectId).toString();

            // Create test reminder
            const reminder = await MedicationReminder.create({
                userId,
                medicationId,
                scheduledTime: new Date(Date.now() + 24 * 60 * 60 * 1000),
                status: ReminderStatus.PENDING
            });
            reminderId = (reminder._id as mongoose.Types.ObjectId).toString();
        });

        describe('GET /api/medications/reminders', () => {
            it('should get all reminders for user', async () => {
                const response = await request(app)
                    .get('/api/medications/reminders')
                    .set('Authorization', `Bearer ${authToken}`);

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
                expect(response.body.data).toHaveLength(1);
                expect(response.body.data[0]._id).toBe(reminderId);
            });

            it('should filter reminders by date', async () => {
                const today = new Date().toISOString().split('T')[0];
                const response = await request(app)
                    .get(`/api/medications/reminders?date=${today}`)
                    .set('Authorization', `Bearer ${authToken}`);

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
            });

            it('should filter reminders by status', async () => {
                const response = await request(app)
                    .get(`/api/medications/reminders?status=${ReminderStatus.PENDING}`)
                    .set('Authorization', `Bearer ${authToken}`);

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
                expect(response.body.data.every((r: any) => r.status === ReminderStatus.PENDING)).toBe(true);
            });
        });

        describe('PUT /api/medications/reminders/:id', () => {
            it('should update reminder status to taken', async () => {
                const updateData = {
                    status: ReminderStatus.TAKEN,
                    notes: 'Taken with breakfast'
                };

                const response = await request(app)
                    .put(`/api/medications/reminders/${reminderId}`)
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(updateData);

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
                expect(response.body.data.status).toBe(ReminderStatus.TAKEN);
                expect(response.body.data.notes).toBe(updateData.notes);
                expect(response.body.data.actualTime).toBeDefined();
            });

            it('should update reminder status to missed', async () => {
                const updateData = {
                    status: ReminderStatus.MISSED
                };

                const response = await request(app)
                    .put(`/api/medications/reminders/${reminderId}`)
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(updateData);

                expect(response.status).toBe(200);
                expect(response.body.data.status).toBe(ReminderStatus.MISSED);
            });

            it('should fail with invalid status', async () => {
                const updateData = {
                    status: 'invalid_status'
                };

                const response = await request(app)
                    .put(`/api/medications/reminders/${reminderId}`)
                    .set('Authorization', `Bearer ${authToken}`)
                    .send(updateData);

                expect(response.status).toBe(400);
                expect(response.body.success).toBe(false);
            });
        });

        describe('PUT /api/medications/reminders/bulk', () => {
            let reminder2Id: string;

            beforeEach(async () => {
                const reminder2 = await MedicationReminder.create({
                    userId,
                    medicationId,
                    scheduledTime: new Date(Date.now() + 25 * 60 * 60 * 1000),
                    status: ReminderStatus.PENDING
                });
                reminder2Id = (reminder2._id as mongoose.Types.ObjectId).toString();
            });

            it('should bulk update multiple reminders', async () => {
                const updates = [
                    { id: reminderId, status: ReminderStatus.TAKEN },
                    { id: reminder2Id, status: ReminderStatus.SKIPPED, notes: 'Forgot to take' }
                ];

                const response = await request(app)
                    .put('/api/medications/reminders/bulk')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send({ updates });

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
                expect(response.body.data.updated).toHaveLength(2);
                expect(response.body.data.errors).toHaveLength(0);
            });

            it('should handle mixed success and failure in bulk update', async () => {
                const fakeId = new mongoose.Types.ObjectId();
                const updates = [
                    { id: reminderId, status: ReminderStatus.TAKEN },
                    { id: fakeId.toString(), status: ReminderStatus.TAKEN }
                ];

                const response = await request(app)
                    .put('/api/medications/reminders/bulk')
                    .set('Authorization', `Bearer ${authToken}`)
                    .send({ updates });

                expect(response.status).toBe(200);
                expect(response.body.data.updated).toHaveLength(1);
                expect(response.body.data.errors).toHaveLength(1);
            });
        });

        describe('GET /api/medications/stats', () => {
            beforeEach(async () => {
                // Create additional reminders with different statuses
                await MedicationReminder.insertMany([
                    {
                        userId,
                        medicationId,
                        scheduledTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
                        status: ReminderStatus.TAKEN,
                        actualTime: new Date(Date.now() - 24 * 60 * 60 * 1000)
                    },
                    {
                        userId,
                        medicationId,
                        scheduledTime: new Date(Date.now() - 12 * 60 * 60 * 1000),
                        status: ReminderStatus.MISSED
                    }
                ]);
            });

            it('should get medication adherence statistics', async () => {
                const response = await request(app)
                    .get('/api/medications/stats')
                    .set('Authorization', `Bearer ${authToken}`);

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
                expect(response.body.data).toHaveProperty('totalMedications');
                expect(response.body.data).toHaveProperty('activeMedications');
                expect(response.body.data).toHaveProperty('overallAdherence');
                expect(response.body.data).toHaveProperty('medicationAdherence');
                expect(Array.isArray(response.body.data.medicationAdherence)).toBe(true);
            });

            it('should support date range filtering for stats', async () => {
                const startDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString();
                const endDate = new Date().toISOString();

                const response = await request(app)
                    .get(`/api/medications/stats?startDate=${startDate}&endDate=${endDate}`)
                    .set('Authorization', `Bearer ${authToken}`);

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);
            });
        });

        describe('POST /api/medications/:id/generate-reminders', () => {
            it('should generate reminders for medication', async () => {
                // Clear existing reminders
                await MedicationReminder.deleteMany({ medicationId });

                const response = await request(app)
                    .post(`/api/medications/${medicationId}/generate-reminders`)
                    .set('Authorization', `Bearer ${authToken}`)
                    .send({ days: 7 });

                expect(response.status).toBe(200);
                expect(response.body.success).toBe(true);

                // Check that reminders were created
                const reminders = await MedicationReminder.find({ medicationId });
                expect(reminders.length).toBeGreaterThan(0);
            });

            it('should replace existing future reminders', async () => {
                const initialCount = await MedicationReminder.countDocuments({ medicationId });

                const response = await request(app)
                    .post(`/api/medications/${medicationId}/generate-reminders`)
                    .set('Authorization', `Bearer ${authToken}`)
                    .send({ days: 3 });

                expect(response.status).toBe(200);

                const finalCount = await MedicationReminder.countDocuments({ medicationId });
                expect(finalCount).toBeGreaterThanOrEqual(1);
            });
        });
    });
});
