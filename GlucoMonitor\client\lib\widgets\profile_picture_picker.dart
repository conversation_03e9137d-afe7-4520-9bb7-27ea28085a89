import 'dart:io';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import '../constants/app_colors.dart';

class ProfilePicturePicker extends StatefulWidget {
  final String? initialImagePath;
  final Function(String?) onImageSelected;
  final double size;

  const ProfilePicturePicker({
    super.key,
    this.initialImagePath,
    required this.onImageSelected,
    this.size = 120,
  });

  @override
  State<ProfilePicturePicker> createState() => _ProfilePicturePickerState();
}

class _ProfilePicturePickerState extends State<ProfilePicturePicker> {
  String? _imagePath;
  final ImagePicker _picker = ImagePicker();

  @override
  void initState() {
    super.initState();
    _imagePath = widget.initialImagePath;
  }

  Future<void> _showImageSourceDialog() async {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Select Profile Picture'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: const Icon(Icons.camera_alt),
                title: const Text('Take Photo'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.camera);
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('Choose from Gallery'),
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),
              if (_imagePath != null)
                ListTile(
                  leading: const Icon(Icons.delete, color: Colors.red),
                  title: const Text('Remove Photo'),
                  onTap: () {
                    Navigator.pop(context);
                    _removeImage();
                  },
                ),
            ],
          ),
        );
      },
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? image = await _picker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (image != null) {
        // Save image to app directory
        final String savedPath = await _saveImageToAppDirectory(image);
        setState(() {
          _imagePath = savedPath;
        });
        widget.onImageSelected(savedPath);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error selecting image: $e')));
      }
    }
  }

  Future<String> _saveImageToAppDirectory(XFile image) async {
    final Directory appDir = await getApplicationDocumentsDirectory();
    final String profilePicsDir = path.join(appDir.path, 'profile_pictures');

    // Create directory if it doesn't exist
    await Directory(profilePicsDir).create(recursive: true);

    // Generate unique filename
    final String fileName =
        'profile_${DateTime.now().millisecondsSinceEpoch}.jpg';
    final String savedPath = path.join(profilePicsDir, fileName);

    // Copy file to app directory
    await File(image.path).copy(savedPath);

    return savedPath;
  }

  void _removeImage() {
    setState(() {
      _imagePath = null;
    });
    widget.onImageSelected(null);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _showImageSourceDialog,
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: AppColors.surface,
          border: Border.all(
            color: AppColors.onBackground.withValues(alpha: 0.3),
            width: 2,
          ),
        ),
        child:
            _imagePath != null && File(_imagePath!).existsSync()
                ? ClipOval(
                  child: Image.file(
                    File(_imagePath!),
                    width: widget.size,
                    height: widget.size,
                    fit: BoxFit.cover,
                  ),
                )
                : Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.add_a_photo,
                      size: widget.size * 0.3,
                      color: AppColors.onBackground.withValues(alpha: 0.6),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Add Photo',
                      style: TextStyle(
                        color: AppColors.onBackground.withValues(alpha: 0.6),
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
      ),
    );
  }
}
