class GlucoseReading {
  final String? id;
  final String userId;
  final double value;
  final DateTime timestamp;
  final MealTiming? mealTiming;
  final String? notes;
  final List<String>? tags;
  final GlucoseCategory? category;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  GlucoseReading({
    this.id,
    required this.userId,
    required this.value,
    required this.timestamp,
    this.mealTiming,
    this.notes,
    this.tags,
    this.category,
    this.createdAt,
    this.updatedAt,
  });

  factory GlucoseReading.fromJson(Map<String, dynamic> json) {
    return GlucoseReading(
      id: json['_id'] ?? json['id'],
      userId: json['userId'],
      value: (json['value'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp']),
      mealTiming: json['mealTiming'] != null 
          ? MealTiming.fromString(json['mealTiming'])
          : null,
      notes: json['notes'],
      tags: json['tags'] != null 
          ? List<String>.from(json['tags'])
          : null,
      category: json['category'] != null 
          ? GlucoseCategory.fromString(json['category'])
          : null,
      createdAt: json['createdAt'] != null 
          ? DateTime.parse(json['createdAt'])
          : null,
      updatedAt: json['updatedAt'] != null 
          ? DateTime.parse(json['updatedAt'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) '_id': id,
      'userId': userId,
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      if (mealTiming != null) 'mealTiming': mealTiming!.value,
      if (notes != null) 'notes': notes,
      if (tags != null) 'tags': tags,
      if (category != null) 'category': category!.value,
      if (createdAt != null) 'createdAt': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updatedAt': updatedAt!.toIso8601String(),
    };
  }

  Map<String, dynamic> toCreateJson() {
    return {
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      if (mealTiming != null) 'mealTiming': mealTiming!.value,
      if (notes != null && notes!.isNotEmpty) 'notes': notes,
      if (tags != null && tags!.isNotEmpty) 'tags': tags,
    };
  }

  GlucoseReading copyWith({
    String? id,
    String? userId,
    double? value,
    DateTime? timestamp,
    MealTiming? mealTiming,
    String? notes,
    List<String>? tags,
    GlucoseCategory? category,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return GlucoseReading(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      value: value ?? this.value,
      timestamp: timestamp ?? this.timestamp,
      mealTiming: mealTiming ?? this.mealTiming,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
      category: category ?? this.category,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Calculate category based on value
  GlucoseCategory get calculatedCategory {
    if (value < 70) return GlucoseCategory.low;
    if (value <= 140) return GlucoseCategory.normal;
    if (value <= 200) return GlucoseCategory.high;
    return GlucoseCategory.veryHigh;
  }

  @override
  String toString() {
    return 'GlucoseReading(id: $id, value: $value, timestamp: $timestamp, mealTiming: $mealTiming)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is GlucoseReading &&
        other.id == id &&
        other.userId == userId &&
        other.value == value &&
        other.timestamp == timestamp;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        userId.hashCode ^
        value.hashCode ^
        timestamp.hashCode;
  }
}

enum MealTiming {
  beforeBreakfast('before_breakfast', 'Before Breakfast'),
  afterBreakfast('after_breakfast', 'After Breakfast'),
  beforeLunch('before_lunch', 'Before Lunch'),
  afterLunch('after_lunch', 'After Lunch'),
  beforeDinner('before_dinner', 'Before Dinner'),
  afterDinner('after_dinner', 'After Dinner'),
  bedtime('bedtime', 'Bedtime'),
  other('other', 'Other');

  const MealTiming(this.value, this.displayName);

  final String value;
  final String displayName;

  static MealTiming fromString(String value) {
    return MealTiming.values.firstWhere(
      (timing) => timing.value == value,
      orElse: () => MealTiming.other,
    );
  }

  static List<MealTiming> get allTimings => MealTiming.values;
}

enum GlucoseCategory {
  low('low', 'Low', 'Below 70 mg/dL'),
  normal('normal', 'Normal', '70-140 mg/dL'),
  high('high', 'High', '141-200 mg/dL'),
  veryHigh('very_high', 'Very High', 'Above 200 mg/dL');

  const GlucoseCategory(this.value, this.displayName, this.range);

  final String value;
  final String displayName;
  final String range;

  static GlucoseCategory fromString(String value) {
    return GlucoseCategory.values.firstWhere(
      (category) => category.value == value,
      orElse: () => GlucoseCategory.normal,
    );
  }

  static GlucoseCategory fromValue(double glucoseValue) {
    if (glucoseValue < 70) return GlucoseCategory.low;
    if (glucoseValue <= 140) return GlucoseCategory.normal;
    if (glucoseValue <= 200) return GlucoseCategory.high;
    return GlucoseCategory.veryHigh;
  }
}

class GlucoseReadingStats {
  final int count;
  final double average;
  final double min;
  final double max;
  final int lowCount;
  final int normalCount;
  final int highCount;
  final int veryHighCount;

  GlucoseReadingStats({
    required this.count,
    required this.average,
    required this.min,
    required this.max,
    required this.lowCount,
    required this.normalCount,
    required this.highCount,
    required this.veryHighCount,
  });

  factory GlucoseReadingStats.fromJson(Map<String, dynamic> json) {
    return GlucoseReadingStats(
      count: json['count'] ?? 0,
      average: (json['average'] ?? 0).toDouble(),
      min: (json['min'] ?? 0).toDouble(),
      max: (json['max'] ?? 0).toDouble(),
      lowCount: json['lowCount'] ?? 0,
      normalCount: json['normalCount'] ?? 0,
      highCount: json['highCount'] ?? 0,
      veryHighCount: json['veryHighCount'] ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'count': count,
      'average': average,
      'min': min,
      'max': max,
      'lowCount': lowCount,
      'normalCount': normalCount,
      'highCount': highCount,
      'veryHighCount': veryHighCount,
    };
  }
}

class GlucoseReadingResponse {
  final List<GlucoseReading> readings;
  final GlucoseReadingPagination? pagination;

  GlucoseReadingResponse({
    required this.readings,
    this.pagination,
  });

  factory GlucoseReadingResponse.fromJson(Map<String, dynamic> json) {
    return GlucoseReadingResponse(
      readings: (json['readings'] as List)
          .map((reading) => GlucoseReading.fromJson(reading))
          .toList(),
      pagination: json['pagination'] != null
          ? GlucoseReadingPagination.fromJson(json['pagination'])
          : null,
    );
  }
}

class GlucoseReadingPagination {
  final int current;
  final int pages;
  final int total;
  final int limit;

  GlucoseReadingPagination({
    required this.current,
    required this.pages,
    required this.total,
    required this.limit,
  });

  factory GlucoseReadingPagination.fromJson(Map<String, dynamic> json) {
    return GlucoseReadingPagination(
      current: json['current'],
      pages: json['pages'],
      total: json['total'],
      limit: json['limit'],
    );
  }
}
