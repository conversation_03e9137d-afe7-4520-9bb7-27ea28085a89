import 'package:flutter/material.dart';
import 'food_entry.dart';

/// Result of food recognition analysis
class FoodRecognitionResult {
  final List<FoodEntry> detectedFoods;
  final double confidence;
  final DateTime analysisTime;
  final bool isRealtime;
  final ImageMetadata? imageMetadata;
  final int? processingTimeMs;
  final List<DetectedFood>? detectedItems;
  final PortionEstimation? portionEstimation;

  const FoodRecognitionResult({
    required this.detectedFoods,
    required this.confidence,
    required this.analysisTime,
    required this.isRealtime,
    this.imageMetadata,
    this.processingTimeMs,
    this.detectedItems,
    this.portionEstimation,
  });

  /// Create a copy with updated values
  FoodRecognitionResult copyWith({
    List<FoodEntry>? detectedFoods,
    double? confidence,
    DateTime? analysisTime,
    bool? isRealtime,
    ImageMetadata? imageMetadata,
    int? processingTimeMs,
    List<DetectedFood>? detectedItems,
    PortionEstimation? portionEstimation,
  }) {
    return FoodRecognitionResult(
      detectedFoods: detectedFoods ?? this.detectedFoods,
      confidence: confidence ?? this.confidence,
      analysisTime: analysisTime ?? this.analysisTime,
      isRealtime: isRealtime ?? this.isRealtime,
      imageMetadata: imageMetadata ?? this.imageMetadata,
      processingTimeMs: processingTimeMs ?? this.processingTimeMs,
      detectedItems: detectedItems ?? this.detectedItems,
      portionEstimation: portionEstimation ?? this.portionEstimation,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'detectedFoods': detectedFoods.map((food) => food.toJson()).toList(),
      'confidence': confidence,
      'analysisTime': analysisTime.toIso8601String(),
      'isRealtime': isRealtime,
      'imageMetadata': imageMetadata?.toJson(),
      'processingTimeMs': processingTimeMs,
      'detectedItems': detectedItems?.map((item) => item.toJson()).toList(),
      'portionEstimation': portionEstimation?.toJson(),
    };
  }

  /// Create from JSON
  factory FoodRecognitionResult.fromJson(Map<String, dynamic> json) {
    return FoodRecognitionResult(
      detectedFoods: (json['detectedFoods'] as List)
          .map((food) => FoodEntry.fromJson(food))
          .toList(),
      confidence: json['confidence']?.toDouble() ?? 0.0,
      analysisTime: DateTime.parse(json['analysisTime']),
      isRealtime: json['isRealtime'] ?? false,
      imageMetadata: json['imageMetadata'] != null
          ? ImageMetadata.fromJson(json['imageMetadata'])
          : null,
      processingTimeMs: json['processingTimeMs'],
      detectedItems: json['detectedItems'] != null
          ? (json['detectedItems'] as List)
              .map((item) => DetectedFood.fromJson(item))
              .toList()
          : null,
      portionEstimation: json['portionEstimation'] != null
          ? PortionEstimation.fromJson(json['portionEstimation'])
          : null,
    );
  }
}

/// Individual detected food item with bounding box
class DetectedFood {
  final String name;
  final double confidence;
  final Rect boundingBox;
  final FoodCategory category;
  final bool isSelected;
  final List<String> alternativeNames;

  const DetectedFood({
    required this.name,
    required this.confidence,
    required this.boundingBox,
    required this.category,
    this.isSelected = true,
    this.alternativeNames = const [],
  });

  /// Create a copy with updated values
  DetectedFood copyWith({
    String? name,
    double? confidence,
    Rect? boundingBox,
    FoodCategory? category,
    bool? isSelected,
    List<String>? alternativeNames,
  }) {
    return DetectedFood(
      name: name ?? this.name,
      confidence: confidence ?? this.confidence,
      boundingBox: boundingBox ?? this.boundingBox,
      category: category ?? this.category,
      isSelected: isSelected ?? this.isSelected,
      alternativeNames: alternativeNames ?? this.alternativeNames,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'confidence': confidence,
      'boundingBox': {
        'left': boundingBox.left,
        'top': boundingBox.top,
        'right': boundingBox.right,
        'bottom': boundingBox.bottom,
      },
      'category': category.toString(),
      'isSelected': isSelected,
      'alternativeNames': alternativeNames,
    };
  }

  /// Create from JSON
  factory DetectedFood.fromJson(Map<String, dynamic> json) {
    final bbox = json['boundingBox'];
    return DetectedFood(
      name: json['name'],
      confidence: json['confidence']?.toDouble() ?? 0.0,
      boundingBox: Rect.fromLTRB(
        bbox['left']?.toDouble() ?? 0.0,
        bbox['top']?.toDouble() ?? 0.0,
        bbox['right']?.toDouble() ?? 0.0,
        bbox['bottom']?.toDouble() ?? 0.0,
      ),
      category: FoodCategory.values.firstWhere(
        (cat) => cat.toString() == json['category'],
        orElse: () => FoodCategory.other,
      ),
      isSelected: json['isSelected'] ?? true,
      alternativeNames: List<String>.from(json['alternativeNames'] ?? []),
    );
  }
}

/// Image metadata for recognition analysis
class ImageMetadata {
  final int size;
  final String format;
  final Size dimensions;
  final String? lighting;
  final String? clarity;

  const ImageMetadata({
    required this.size,
    required this.format,
    required this.dimensions,
    this.lighting,
    this.clarity,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'size': size,
      'format': format,
      'dimensions': {
        'width': dimensions.width,
        'height': dimensions.height,
      },
      'lighting': lighting,
      'clarity': clarity,
    };
  }

  /// Create from JSON
  factory ImageMetadata.fromJson(Map<String, dynamic> json) {
    final dims = json['dimensions'];
    return ImageMetadata(
      size: json['size'],
      format: json['format'],
      dimensions: Size(
        dims['width']?.toDouble() ?? 0.0,
        dims['height']?.toDouble() ?? 0.0,
      ),
      lighting: json['lighting'],
      clarity: json['clarity'],
    );
  }
}

/// Portion size estimation result
class PortionEstimation {
  final double estimatedWeight;
  final String unit;
  final double confidence;
  final String method;
  final List<ReferenceObject>? referenceObjects;

  const PortionEstimation({
    required this.estimatedWeight,
    required this.unit,
    required this.confidence,
    required this.method,
    this.referenceObjects,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'estimatedWeight': estimatedWeight,
      'unit': unit,
      'confidence': confidence,
      'method': method,
      'referenceObjects': referenceObjects?.map((obj) => obj.toJson()).toList(),
    };
  }

  /// Create from JSON
  factory PortionEstimation.fromJson(Map<String, dynamic> json) {
    return PortionEstimation(
      estimatedWeight: json['estimatedWeight']?.toDouble() ?? 0.0,
      unit: json['unit'],
      confidence: json['confidence']?.toDouble() ?? 0.0,
      method: json['method'],
      referenceObjects: json['referenceObjects'] != null
          ? (json['referenceObjects'] as List)
              .map((obj) => ReferenceObject.fromJson(obj))
              .toList()
          : null,
    );
  }
}

/// Reference object for portion estimation
class ReferenceObject {
  final String name;
  final Rect boundingBox;
  final double knownSize;
  final String unit;

  const ReferenceObject({
    required this.name,
    required this.boundingBox,
    required this.knownSize,
    required this.unit,
  });

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'boundingBox': {
        'left': boundingBox.left,
        'top': boundingBox.top,
        'right': boundingBox.right,
        'bottom': boundingBox.bottom,
      },
      'knownSize': knownSize,
      'unit': unit,
    };
  }

  /// Create from JSON
  factory ReferenceObject.fromJson(Map<String, dynamic> json) {
    final bbox = json['boundingBox'];
    return ReferenceObject(
      name: json['name'],
      boundingBox: Rect.fromLTRB(
        bbox['left']?.toDouble() ?? 0.0,
        bbox['top']?.toDouble() ?? 0.0,
        bbox['right']?.toDouble() ?? 0.0,
        bbox['bottom']?.toDouble() ?? 0.0,
      ),
      knownSize: json['knownSize']?.toDouble() ?? 0.0,
      unit: json['unit'],
    );
  }
}
