// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have been removed for macOS and windows platforms - '
          'these platforms are not supported in this application.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAnhXORilSM48d-dn9NQawED08ZQ5ZKR7o',
    appId: '1:1030117935003:web:56dfc7c732a822bb85033e',
    messagingSenderId: '1030117935003',
    projectId: 'zocial-38a45',
    authDomain: 'zocial-38a45.firebaseapp.com',
    storageBucket: 'zocial-38a45.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAOdalRmEJb9ZI0FiJ_lQIBZTiWISC27wg',
    appId: '1:1030117935003:android:da282cde0d3e26c185033e',
    messagingSenderId: '1030117935003',
    projectId: 'zocial-38a45',
    storageBucket: 'zocial-38a45.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCq_L1zLgWDH38R9gjUFgbGflg1jHUCTFk',
    appId: '1:1030117935003:ios:26bc57b5c9c17fd685033e',
    messagingSenderId: '1030117935003',
    projectId: 'zocial-38a45',
    storageBucket: 'zocial-38a45.firebasestorage.app',
    iosBundleId: 'com.sentebale.zocial',
  );
}
