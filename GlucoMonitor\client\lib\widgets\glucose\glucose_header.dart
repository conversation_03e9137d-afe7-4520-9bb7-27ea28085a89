import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/glucose_reading.dart';

class GlucoseHeader extends StatelessWidget {
  final GlucoseReading? latestReading;
  final VoidCallback onAddReading;

  const GlucoseHeader({
    super.key,
    this.latestReading,
    required this.onAddReading,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const Text(
                'Current Status',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.black87,
                ),
              ),
              ElevatedButton.icon(
                onPressed: onAddReading,
                icon: const Icon(Icons.add, size: 18),
                label: const Text('Add Reading'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (latestReading != null) ...[
            _buildCurrentReading(),
          ] else ...[
            _buildNoReadings(),
          ],
        ],
      ),
    );
  }

  Widget _buildCurrentReading() {
    final reading = latestReading!;
    final category = reading.calculatedCategory;
    final categoryColor = _getCategoryColor(category);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: categoryColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                reading.value.toStringAsFixed(1),
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: categoryColor,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'mg/dL',
                    style: TextStyle(fontSize: 16, color: Colors.grey[400]),
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: categoryColor.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Text(
                      category.displayName,
                      style: TextStyle(
                        color: categoryColor,
                        fontWeight: FontWeight.w600,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            _buildTrendIndicator(),
          ],
        ),
        const SizedBox(height: 12),
        Text(
          'Target Range: 80-180 mg/dL',
          style: TextStyle(color: Colors.grey[400], fontSize: 14),
        ),
        const SizedBox(height: 8),
        Text(
          _formatTimestamp(reading.timestamp),
          style: TextStyle(color: Colors.grey[500], fontSize: 12),
        ),
      ],
    );
  }

  Widget _buildNoReadings() {
    return Column(
      children: [
        Icon(Icons.timeline, size: 48, color: Colors.grey[400]),
        const SizedBox(height: 12),
        Text(
          'No readings yet',
          style: TextStyle(fontSize: 16, color: Colors.grey[400]),
        ),
        const SizedBox(height: 4),
        Text(
          'Add your first glucose reading to get started',
          style: TextStyle(fontSize: 12, color: Colors.grey[500]),
        ),
      ],
    );
  }

  Widget _buildTrendIndicator() {
    // For now, show a neutral trend. In a real app, you'd calculate this
    // based on recent readings comparison
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Icon(Icons.trending_flat, color: Colors.grey[400], size: 20),
    );
  }

  Color _getCategoryColor(GlucoseCategory category) {
    switch (category) {
      case GlucoseCategory.low:
        return Colors.blue;
      case GlucoseCategory.normal:
        return AppColors.secondary; // Green
      case GlucoseCategory.high:
        return Colors.orange;
      case GlucoseCategory.veryHigh:
        return Colors.red;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 60) {
      return '${difference.inMinutes} minutes ago';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} hours ago';
    } else {
      return '${difference.inDays} days ago';
    }
  }
}
