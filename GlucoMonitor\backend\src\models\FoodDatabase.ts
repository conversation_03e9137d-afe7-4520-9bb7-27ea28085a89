import mongoose, { Document, Schema } from 'mongoose';
import { GlycemicIndex, FoodCategory, BloodSugarImpact } from './FoodEntry';

// Food Database Entry interface - for the master food database
export interface IFoodDatabaseEntry extends Document {
    name: string;
    brand?: string;
    category: FoodCategory;
    
    // Nutritional information per 100g
    caloriesPer100g: number;
    carbohydratesPer100g: number;
    proteinPer100g: number;
    fatPer100g: number;
    fiberPer100g: number;
    sugarPer100g?: number;
    sodiumPer100g?: number; // mg
    
    // Glycemic information
    glycemicIndex: GlycemicIndex;
    glycemicLoad?: number;
    
    // Common serving sizes
    commonPortions: IPortionSize[];
    
    // South African specific information
    isTraditionalSA: boolean;
    localNames?: ILocalizedName[]; // Names in different SA languages
    seasonality?: string[]; // Months when in season
    region?: string; // Where commonly found in SA

    // Multilingual support
    nameTranslations: INameTranslations;

    // Enhanced nutrition data
    vitamins?: IVitaminContent;
    minerals?: IMineralContent;

    // Dietary information
    dietaryTags?: string[]; // e.g., 'halal', 'kosher', 'vegan', 'vegetarian'
    healthBenefits?: string[];

    // Food origin and availability
    origin: string; // 'traditional_sa', 'international', 'local_adaptation'
    availability: string; // 'year_round', 'seasonal', 'imported'
    
    // Additional metadata
    description?: string;
    preparationNotes?: string;
    diabeticNotes?: string;
    allergens?: string[];
    
    // Database management
    isVerified: boolean;
    source?: string; // Data source reference
    lastUpdated: Date;
    createdAt: Date;
    updatedAt: Date;

    // Virtual properties
    bloodSugarImpact: BloodSugarImpact;
    isDiabetesFriendly: boolean;
    searchableText: string;
}

// Portion Size interface
export interface IPortionSize {
    description: string; // e.g., "1 cup", "1 slice", "1 medium"
    weight: number; // grams
    unit: string; // e.g., "g", "ml", "pieces"
    isCommon: boolean; // Most frequently used portions
}

// Localized Name interface for SA languages
export interface ILocalizedName {
    language: 'en' | 'zu' | 'af' | 'st' | 'xh' | 'nso' | 'tn' | 'ss' | 've' | 'ts' | 'nr';
    name: string;
    isCommon: boolean; // Whether this name is commonly used
}

// Name Translations interface
export interface INameTranslations {
    english: string;
    zulu?: string;
    afrikaans?: string;
    sesotho?: string;
    xhosa?: string;
    sepedi?: string;
    setswana?: string;
    siswati?: string;
    tshivenda?: string;
    xitsonga?: string;
    ndebele?: string;
}

// Vitamin Content interface
export interface IVitaminContent {
    vitaminA?: number; // mcg RAE
    vitaminC?: number; // mg
    vitaminD?: number; // mcg
    vitaminE?: number; // mg
    vitaminK?: number; // mcg
    thiamine?: number; // mg (B1)
    riboflavin?: number; // mg (B2)
    niacin?: number; // mg (B3)
    vitaminB6?: number; // mg
    folate?: number; // mcg
    vitaminB12?: number; // mcg
    biotin?: number; // mcg
    pantothenicAcid?: number; // mg (B5)
}

// Mineral Content interface
export interface IMineralContent {
    calcium?: number; // mg
    iron?: number; // mg
    magnesium?: number; // mg
    phosphorus?: number; // mg
    potassium?: number; // mg
    sodium?: number; // mg
    zinc?: number; // mg
    copper?: number; // mg
    manganese?: number; // mg
    selenium?: number; // mcg
    iodine?: number; // mcg
}

const portionSizeSchema = new Schema<IPortionSize>({
    description: {
        type: String,
        required: true,
        trim: true,
        maxlength: [50, 'Portion description cannot exceed 50 characters']
    },
    weight: {
        type: Number,
        required: true,
        min: [0.1, 'Portion weight must be greater than 0']
    },
    unit: {
        type: String,
        required: true,
        trim: true,
        maxlength: [10, 'Unit cannot exceed 10 characters']
    },
    isCommon: {
        type: Boolean,
        default: false
    }
}, { _id: false });

const foodDatabaseSchema = new Schema<IFoodDatabaseEntry>({
    name: {
        type: String,
        required: [true, 'Food name is required'],
        trim: true,
        maxlength: [100, 'Food name cannot exceed 100 characters'],
        index: true
    },
    brand: {
        type: String,
        trim: true,
        maxlength: [50, 'Brand name cannot exceed 50 characters'],
        index: true
    },
    category: {
        type: String,
        enum: Object.values(FoodCategory),
        required: [true, 'Food category is required'],
        index: true
    },
    caloriesPer100g: {
        type: Number,
        required: [true, 'Calories per 100g is required'],
        min: [0, 'Calories cannot be negative']
    },
    carbohydratesPer100g: {
        type: Number,
        required: [true, 'Carbohydrates per 100g is required'],
        min: [0, 'Carbohydrates cannot be negative']
    },
    proteinPer100g: {
        type: Number,
        required: [true, 'Protein per 100g is required'],
        min: [0, 'Protein cannot be negative']
    },
    fatPer100g: {
        type: Number,
        required: [true, 'Fat per 100g is required'],
        min: [0, 'Fat cannot be negative']
    },
    fiberPer100g: {
        type: Number,
        required: [true, 'Fiber per 100g is required'],
        min: [0, 'Fiber cannot be negative']
    },
    sugarPer100g: {
        type: Number,
        min: [0, 'Sugar cannot be negative']
    },
    sodiumPer100g: {
        type: Number,
        min: [0, 'Sodium cannot be negative']
    },
    glycemicIndex: {
        type: String,
        enum: Object.values(GlycemicIndex),
        required: [true, 'Glycemic index is required'],
        index: true
    },
    glycemicLoad: {
        type: Number,
        min: [0, 'Glycemic load cannot be negative'],
        max: [50, 'Glycemic load cannot exceed 50']
    },
    commonPortions: [portionSizeSchema],
    isTraditionalSA: {
        type: Boolean,
        default: false,
        index: true
    },
    localNames: [{
        language: {
            type: String,
            enum: ['en', 'zu', 'af', 'st', 'xh', 'nso', 'tn', 'ss', 've', 'ts', 'nr'],
            required: true
        },
        name: {
            type: String,
            trim: true,
            maxlength: [50, 'Local name cannot exceed 50 characters'],
            required: true
        },
        isCommon: {
            type: Boolean,
            default: false
        }
    }],
    nameTranslations: {
        english: {
            type: String,
            required: true,
            trim: true,
            maxlength: [100, 'English name cannot exceed 100 characters']
        },
        zulu: {
            type: String,
            trim: true,
            maxlength: [100, 'Zulu name cannot exceed 100 characters']
        },
        afrikaans: {
            type: String,
            trim: true,
            maxlength: [100, 'Afrikaans name cannot exceed 100 characters']
        },
        sesotho: {
            type: String,
            trim: true,
            maxlength: [100, 'Sesotho name cannot exceed 100 characters']
        },
        xhosa: {
            type: String,
            trim: true,
            maxlength: [100, 'Xhosa name cannot exceed 100 characters']
        },
        sepedi: {
            type: String,
            trim: true,
            maxlength: [100, 'Sepedi name cannot exceed 100 characters']
        },
        setswana: {
            type: String,
            trim: true,
            maxlength: [100, 'Setswana name cannot exceed 100 characters']
        },
        siswati: {
            type: String,
            trim: true,
            maxlength: [100, 'Siswati name cannot exceed 100 characters']
        },
        tshivenda: {
            type: String,
            trim: true,
            maxlength: [100, 'Tshivenda name cannot exceed 100 characters']
        },
        xitsonga: {
            type: String,
            trim: true,
            maxlength: [100, 'Xitsonga name cannot exceed 100 characters']
        },
        ndebele: {
            type: String,
            trim: true,
            maxlength: [100, 'Ndebele name cannot exceed 100 characters']
        }
    },
    seasonality: [{
        type: String,
        enum: ['January', 'February', 'March', 'April', 'May', 'June', 
               'July', 'August', 'September', 'October', 'November', 'December']
    }],
    region: {
        type: String,
        trim: true,
        maxlength: [100, 'Region cannot exceed 100 characters']
    },
    description: {
        type: String,
        trim: true,
        maxlength: [500, 'Description cannot exceed 500 characters']
    },
    preparationNotes: {
        type: String,
        trim: true,
        maxlength: [300, 'Preparation notes cannot exceed 300 characters']
    },
    diabeticNotes: {
        type: String,
        trim: true,
        maxlength: [300, 'Diabetic notes cannot exceed 300 characters']
    },
    allergens: [{
        type: String,
        enum: ['gluten', 'dairy', 'eggs', 'nuts', 'soy', 'fish', 'shellfish', 'sesame']
    }],
    vitamins: {
        vitaminA: { type: Number, min: 0 }, // mcg RAE
        vitaminC: { type: Number, min: 0 }, // mg
        vitaminD: { type: Number, min: 0 }, // mcg
        vitaminE: { type: Number, min: 0 }, // mg
        vitaminK: { type: Number, min: 0 }, // mcg
        thiamine: { type: Number, min: 0 }, // mg (B1)
        riboflavin: { type: Number, min: 0 }, // mg (B2)
        niacin: { type: Number, min: 0 }, // mg (B3)
        vitaminB6: { type: Number, min: 0 }, // mg
        folate: { type: Number, min: 0 }, // mcg
        vitaminB12: { type: Number, min: 0 }, // mcg
        biotin: { type: Number, min: 0 }, // mcg
        pantothenicAcid: { type: Number, min: 0 } // mg (B5)
    },
    minerals: {
        calcium: { type: Number, min: 0 }, // mg
        iron: { type: Number, min: 0 }, // mg
        magnesium: { type: Number, min: 0 }, // mg
        phosphorus: { type: Number, min: 0 }, // mg
        potassium: { type: Number, min: 0 }, // mg
        sodium: { type: Number, min: 0 }, // mg
        zinc: { type: Number, min: 0 }, // mg
        copper: { type: Number, min: 0 }, // mg
        manganese: { type: Number, min: 0 }, // mg
        selenium: { type: Number, min: 0 }, // mcg
        iodine: { type: Number, min: 0 } // mcg
    },
    dietaryTags: [{
        type: String,
        enum: ['halal', 'kosher', 'vegan', 'vegetarian', 'gluten-free', 'dairy-free', 'nut-free', 'low-sodium', 'high-protein', 'low-carb', 'keto-friendly']
    }],
    healthBenefits: [{
        type: String,
        trim: true,
        maxlength: [200, 'Health benefit cannot exceed 200 characters']
    }],
    origin: {
        type: String,
        enum: ['traditional_sa', 'international', 'local_adaptation'],
        default: 'international',
        index: true
    },
    availability: {
        type: String,
        enum: ['year_round', 'seasonal', 'imported'],
        default: 'year_round',
        index: true
    },
    isVerified: {
        type: Boolean,
        default: false,
        index: true
    },
    source: {
        type: String,
        trim: true,
        maxlength: [100, 'Source cannot exceed 100 characters']
    },
    lastUpdated: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance including multilingual search
foodDatabaseSchema.index({
    name: 'text',
    brand: 'text',
    'localNames.name': 'text',
    'nameTranslations.english': 'text',
    'nameTranslations.zulu': 'text',
    'nameTranslations.afrikaans': 'text',
    'nameTranslations.sesotho': 'text',
    'nameTranslations.xhosa': 'text',
    description: 'text'
});
foodDatabaseSchema.index({ category: 1, glycemicIndex: 1 });
foodDatabaseSchema.index({ isTraditionalSA: 1, category: 1 });
foodDatabaseSchema.index({ isVerified: 1, category: 1 });
foodDatabaseSchema.index({ origin: 1, availability: 1 });
foodDatabaseSchema.index({ dietaryTags: 1 });

// Virtual properties
foodDatabaseSchema.virtual('bloodSugarImpact').get(function(this: IFoodDatabaseEntry) {
    if (this.carbohydratesPer100g <= 15 && this.glycemicIndex === GlycemicIndex.LOW) {
        return BloodSugarImpact.LOW;
    } else if (this.carbohydratesPer100g <= 30 && this.glycemicIndex !== GlycemicIndex.HIGH) {
        return BloodSugarImpact.MODERATE;
    } else {
        return BloodSugarImpact.HIGH;
    }
});

foodDatabaseSchema.virtual('isDiabetesFriendly').get(function(this: IFoodDatabaseEntry) {
    return this.carbohydratesPer100g <= 15 && this.glycemicIndex === GlycemicIndex.LOW;
});

foodDatabaseSchema.virtual('searchableText').get(function(this: IFoodDatabaseEntry) {
    const parts = [this.name];
    if (this.brand) parts.push(this.brand);
    if (this.localNames) parts.push(...this.localNames.map(ln => ln.name));
    if (this.description) parts.push(this.description);
    return parts.join(' ').toLowerCase();
});

// Static methods
foodDatabaseSchema.statics.searchFoods = function(query: string, category?: FoodCategory, limit: number = 50) {
    const searchCriteria: any = {
        isVerified: true,
        $text: { $search: query }
    };

    if (category) {
        searchCriteria.category = category;
    }

    return this.find(searchCriteria)
        .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions isTraditionalSA nameTranslations localNames origin')
        .limit(limit)
        .sort({ score: { $meta: 'textScore' } });
};

// Enhanced multilingual search
foodDatabaseSchema.statics.searchFoodsMultilingual = function(query: string, language?: string, category?: FoodCategory, limit: number = 50) {
    const searchQuery = query.toLowerCase().trim();

    // Build search criteria for multilingual search
    const searchCriteria: any = {
        isVerified: true,
        $or: [
            { name: { $regex: searchQuery, $options: 'i' } },
            { brand: { $regex: searchQuery, $options: 'i' } },
            { 'nameTranslations.english': { $regex: searchQuery, $options: 'i' } },
            { 'nameTranslations.zulu': { $regex: searchQuery, $options: 'i' } },
            { 'nameTranslations.afrikaans': { $regex: searchQuery, $options: 'i' } },
            { 'nameTranslations.sesotho': { $regex: searchQuery, $options: 'i' } },
            { 'nameTranslations.xhosa': { $regex: searchQuery, $options: 'i' } },
            { 'localNames.name': { $regex: searchQuery, $options: 'i' } }
        ]
    };

    if (category) {
        searchCriteria.category = category;
    }

    // If specific language is requested, prioritize that language
    if (language) {
        const languageField = `nameTranslations.${language}`;
        searchCriteria.$or.unshift({ [languageField]: { $regex: searchQuery, $options: 'i' } });
    }

    return this.find(searchCriteria)
        .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions isTraditionalSA nameTranslations localNames origin availability')
        .limit(limit)
        .sort({ name: 1 });
};

foodDatabaseSchema.statics.getFoodsByCategory = function(category: FoodCategory, limit: number = 100) {
    return this.find({ category, isVerified: true })
        .select('name brand caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
        .limit(limit)
        .sort({ name: 1 });
};

foodDatabaseSchema.statics.getDiabetesFriendlyFoods = function(limit: number = 50) {
    return this.find({
        isVerified: true,
        carbohydratesPer100g: { $lte: 15 },
        glycemicIndex: GlycemicIndex.LOW
    })
        .select('name brand category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
        .limit(limit)
        .sort({ carbohydratesPer100g: 1 });
};

foodDatabaseSchema.statics.getTraditionalSAFoods = function(limit: number = 100) {
    return this.find({ isTraditionalSA: true, isVerified: true })
        .select('name nameTranslations localNames category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions region')
        .limit(limit)
        .sort({ name: 1 });
};

// Get foods by origin
foodDatabaseSchema.statics.getFoodsByOrigin = function(origin: string, limit: number = 100) {
    return this.find({ origin, isVerified: true })
        .select('name nameTranslations category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
        .limit(limit)
        .sort({ name: 1 });
};

// Get foods by dietary tags
foodDatabaseSchema.statics.getFoodsByDietaryTags = function(tags: string[], limit: number = 100) {
    return this.find({
        dietaryTags: { $in: tags },
        isVerified: true
    })
        .select('name nameTranslations category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions dietaryTags')
        .limit(limit)
        .sort({ name: 1 });
};

// Advanced nutrition search
foodDatabaseSchema.statics.searchByNutrition = function(criteria: any, limit: number = 50) {
    const searchCriteria: any = { isVerified: true };

    if (criteria.maxCarbs) {
        searchCriteria.carbohydratesPer100g = { $lte: criteria.maxCarbs };
    }
    if (criteria.minProtein) {
        searchCriteria.proteinPer100g = { $gte: criteria.minProtein };
    }
    if (criteria.maxCalories) {
        searchCriteria.caloriesPer100g = { $lte: criteria.maxCalories };
    }
    if (criteria.glycemicIndex) {
        searchCriteria.glycemicIndex = criteria.glycemicIndex;
    }
    if (criteria.category) {
        searchCriteria.category = criteria.category;
    }

    return this.find(searchCriteria)
        .select('name nameTranslations category caloriesPer100g carbohydratesPer100g proteinPer100g fatPer100g fiberPer100g glycemicIndex commonPortions')
        .limit(limit)
        .sort({ carbohydratesPer100g: 1 });
};

// Pre-save middleware to update lastUpdated
foodDatabaseSchema.pre('save', function(next) {
    this.lastUpdated = new Date();
    next();
});

const FoodDatabase = mongoose.model<IFoodDatabaseEntry>('FoodDatabase', foodDatabaseSchema);

export default FoodDatabase;
