# GlucoMonitor Backend API

A TypeScript-based Node.js backend API for the GlucoMonitor glucose monitoring application.

## 🚀 Recent Updates

**✅ TypeScript Migration Complete** - The entire backend has been successfully migrated from JavaScript to TypeScript with enhanced type safety and developer experience.

## 📋 Table of Contents

- [Features](#features)
- [Tech Stack](#tech-stack)
- [Prerequisites](#prerequisites)
- [Installation](#installation)
- [Development](#development)
- [Production](#production)
- [Docker](#docker)
- [API Endpoints](#api-endpoints)
- [Environment Variables](#environment-variables)
- [Testing](#testing)
- [TypeScript Migration](#typescript-migration)

## ✨ Features

- **Email/Password Authentication** with JWT tokens
- **Phone Number Verification** via SMS (SMSPortal integration)
- **Password Reset** functionality with secure tokens
- **Medication Reminder System** with automated notifications
- **Glucose Reading Management** with comprehensive tracking
- **Rate Limiting** with Redis (fallback to in-memory)
- **MongoDB Integration** with Mongoose ODM
- **TypeScript Support** for enhanced development experience
- **Docker Ready** with production and development configurations
- **Graceful Fallbacks** for external service failures

## 🛠 Tech Stack

- **Runtime**: Node.js 18+
- **Language**: TypeScript 5.x
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT (jsonwebtoken)
- **Caching**: Redis (with in-memory fallback)
- **SMS Service**: SMSPortal
- **Password Hashing**: bcryptjs
- **Development**: nodemon + ts-node
- **Build**: TypeScript Compiler (tsc)

## 📦 Prerequisites

- Node.js 18 or higher
- MongoDB database
- Redis server (optional - has in-memory fallback)
- SMSPortal account for SMS services

## 🔧 Installation

1. **Clone and navigate to backend directory**:
   ```bash
   cd backend
   ```

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your actual values
   ```

4. **Build TypeScript** (for production):
   ```bash
   npm run build
   ```

## 🚀 Development

### Start Development Server
```bash
npm run dev
```

This runs the server with:
- **Hot reload** via nodemon
- **TypeScript compilation** via ts-node
- **Development mode** with relaxed rate limits
- **Fixed OTP** "123456" for testing

### Development Features
- Automatic TypeScript compilation
- Hot reload on file changes
- Enhanced error logging
- In-memory fallbacks for external services
- Test-friendly OTP system

## 🏭 Production

### Build and Start
```bash
# Build TypeScript to JavaScript
npm run build

# Start production server
npm start
```

### Production Features
- Compiled JavaScript for optimal performance
- Redis integration for caching and rate limiting
- Strict rate limiting
- Production-grade error handling

## 🐳 Docker

### Quick Start
```bash
# Production mode
docker-compose up --build

# Development mode
docker-compose --profile dev up api-dev redis --build
```

### Docker Services
- **API**: Main backend service
- **Redis**: Caching and rate limiting
- **Development Override**: Hot-reload development environment

See [DOCKER.md](./DOCKER.md) for detailed Docker documentation.

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login with email/password
- `POST /api/auth/verify-otp` - Verify phone number with OTP
- `POST /api/auth/forgot-password` - Request password reset
- `POST /api/auth/reset-password` - Reset password with token
- `GET /api/auth/me` - Get current user profile (protected)
- `PUT /api/auth/profile` - Update user profile (protected)

### Development Only
- `POST /api/auth/clear-rate-limit` - Clear rate limits for testing

## 🔐 Environment Variables

```env
# Database
MONGODB_URI=mongodb://localhost:27017/glucomonitor

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=30d

# SMS Service (SMSPortal)
SMSPORTAL_CLIENT_ID=your-client-id
SMSPORTAL_API_SECRET=your-api-secret
SMSPORTAL_SENDER=your-sender-name

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Medication Notifications
NOTIFICATION_ENABLED=true
NOTIFICATION_ADVANCE_MINUTES=5
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_INTERVAL=300000

# Development Mode
DEVELOPMENT_MODE=false

# Server
PORT=5000
NODE_ENV=development
```

## 🧪 Testing

### Manual API Testing
```bash
# Register a user
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","phoneNumber":"+27123456789","name":"Test User"}'

# Verify OTP (development uses fixed OTP: 123456)
curl -X POST http://localhost:5000/api/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"+27123456789","otp":"123456"}'

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Health Check
```bash
curl http://localhost:5000/
```

## 📝 TypeScript Migration

### What Changed
- ✅ **All `.js` files** converted to `.ts`
- ✅ **Type annotations** added throughout codebase
- ✅ **Express types** properly implemented
- ✅ **Mongoose types** with proper schemas
- ✅ **Redis client** updated to v5.1.1
- ✅ **Build system** configured for TypeScript
- ✅ **Development workflow** enhanced with ts-node

### Benefits
- **Type Safety**: Catch errors at compile time
- **Better IDE Support**: Enhanced autocomplete and refactoring
- **Improved Maintainability**: Self-documenting code
- **Enhanced Developer Experience**: Better debugging and tooling

### Migration Notes
- All functionality preserved during migration
- No breaking changes to API contracts
- Enhanced error handling and fallback systems
- Improved Redis client with modern syntax

## 🔧 Scripts

```bash
npm run dev          # Start development server with hot reload
npm run build        # Build TypeScript to JavaScript
npm run start        # Start production server
npm run clean        # Clean build directory
```

## 🛡️ Error Handling

The application includes comprehensive error handling:

- **Redis Unavailable**: Automatic fallback to in-memory storage
- **SMS Service Down**: Development mode simulation
- **Database Errors**: Graceful error responses
- **Rate Limiting**: Memory-based when Redis unavailable
- **Validation Errors**: Clear error messages

## 💊 Medication Reminder System

The backend includes a comprehensive medication reminder system with the following features:

### Core Features
- **Medication Management**: Full CRUD operations for medications
- **Automatic Reminder Generation**: Based on medication frequency and schedule
- **SMS Notifications**: Automated reminders via SMSPortal integration
- **Adherence Tracking**: Comprehensive statistics and reporting
- **Bulk Operations**: Efficient management of multiple reminders

### Key Components
- **Models**: `Medication` and `MedicationReminder` with full TypeScript support
- **Controllers**: Complete business logic for medication management
- **Notification Service**: Automated scheduling with cron jobs
- **Statistics Engine**: Real-time adherence calculations

### Medication Types Supported
- Insulin
- Metformin
- Supplements
- Other medications

### Frequency Options
- Once daily
- Twice daily
- Three times daily
- Four times daily
- Weekly
- Custom schedules

### Environment Configuration
```env
# Notification Settings
NOTIFICATION_ENABLED=true
NOTIFICATION_ADVANCE_MINUTES=5
NOTIFICATION_RETRY_ATTEMPTS=3
NOTIFICATION_RETRY_INTERVAL=300000

# Development Mode
DEVELOPMENT_MODE=false
```

## 📚 Additional Documentation

- [Medication API Documentation](./docs/MEDICATION_API.md) - Complete medication system API reference
- [Medication Deployment Guide](./docs/MEDICATION_DEPLOYMENT.md) - Production deployment instructions
- [Docker Setup](./DOCKER.md) - Comprehensive Docker configuration guide
- [API Documentation](./API.md) - Detailed API endpoint documentation
- [TypeScript Migration Guide](./TYPESCRIPT_MIGRATION.md) - Complete migration process documentation

## 🤝 Contributing

1. Ensure TypeScript compilation passes: `npm run build`
2. Test all endpoints manually or with automated tests
3. Follow existing code patterns and type annotations
4. Update documentation for any API changes

## 📄 License

This project is part of the GlucoMonitor application suite.
