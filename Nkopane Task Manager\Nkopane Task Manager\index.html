<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Task Manager with Pomodoro</title>
    <link rel="icon" type="image/x-icon" href="favicon.ico">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">Task Manager with Pomodoro</h1>
        
        <!-- Pomodoro Timer Section -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Pomodoro Timer</h2>
            <div class="text-center">
                <div class="mb-4 space-x-4">
                    <button id="timer25" class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 active">
                        25 min
                    </button>
                    <button id="timer50" class="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg hover:bg-blue-200">
                        50 min
                    </button>
                    <button id="timer75" class="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg hover:bg-blue-200">
                        75 min
                    </button>
                </div>
                <div class="text-6xl font-bold mb-4 text-gray-800" id="timer">25:00</div>
                <div class="space-x-4">
                    <button id="startTimer" class="bg-green-500 text-white px-6 py-2 rounded-lg hover:bg-green-600">
                        Start
                    </button>
                    <button id="pauseTimer" class="bg-yellow-500 text-white px-6 py-2 rounded-lg hover:bg-yellow-600">
                        Pause
                    </button>
                    <button id="resetTimer" class="bg-red-500 text-white px-6 py-2 rounded-lg hover:bg-red-600">
                        Reset
                    </button>
                </div>
            </div>
        </div>

        <!-- Task Management Section -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4 text-gray-700">Tasks</h2>
            <div class="mb-4">
                <form id="taskForm" class="space-y-4">
                    <div class="flex gap-2">
                        <input type="text" id="taskInput" placeholder="Enter a new task" 
                            class="flex-1 border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                    </div>
                    <div class="flex gap-4">
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" id="startDate" 
                                class="w-full border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <div class="flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">Due Date</label>
                            <input type="date" id="dueDate" 
                                class="w-full border rounded-lg px-4 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500" required>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" 
                                class="bg-blue-500 text-white px-6 py-2 rounded-lg hover:bg-blue-600">
                                Add Task
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div id="taskList" class="space-y-2">
                <!-- Tasks will be added here dynamically -->
            </div>
        </div>
    </div>
    <script src="app.js"></script>
</body>
</html> 