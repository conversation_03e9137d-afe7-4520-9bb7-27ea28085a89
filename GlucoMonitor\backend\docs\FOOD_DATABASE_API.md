# Enhanced South African Food Database API

## Overview

The Enhanced South African Food Database API provides comprehensive access to a multilingual food database containing traditional South African foods and international foods commonly consumed in South Africa. The API supports advanced search capabilities, nutritional filtering, and multilingual queries.

## Base URL
```
http://localhost:5000/api/food-diary
```

## Authentication
All endpoints require authentication. Include the JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

## Food Database Endpoints

### 1. Basic Food Search

**Endpoint:** `GET /search`

**Description:** Basic text search across food names, brands, and descriptions.

**Query Parameters:**
- `query` (required): Search term (minimum 2 characters)
- `category` (optional): Food category filter
- `limit` (optional): Maximum results (default: 50)

**Example:**
```bash
GET /api/food-diary/search?query=chicken&category=proteins&limit=20
```

**Response:**
```json
{
  "success": true,
  "count": 5,
  "data": [
    {
      "_id": "...",
      "name": "Chicken Breast (Skinless)",
      "category": "proteins",
      "caloriesPer100g": 165,
      "carbohydratesPer100g": 0,
      "proteinPer100g": 31,
      "fatPer100g": 3.6,
      "fiberPer100g": 0,
      "glycemicIndex": "low",
      "commonPortions": [...],
      "nameTranslations": {...}
    }
  ]
}
```

### 2. Multilingual Food Search

**Endpoint:** `GET /search/multilingual`

**Description:** Advanced search supporting multiple South African languages.

**Query Parameters:**
- `query` (required): Search term in any supported language
- `language` (optional): Preferred language code (en, zu, af, st, xh, nso, tn, ss, ve, ts, nr)
- `category` (optional): Food category filter
- `limit` (optional): Maximum results (default: 50)

**Supported Languages:**
- `en` - English
- `zu` - Zulu
- `af` - Afrikaans
- `st` - Sesotho
- `xh` - Xhosa
- `nso` - Sepedi
- `tn` - Setswana
- `ss` - Siswati
- `ve` - Tshivenda
- `ts` - Xitsonga
- `nr` - Ndebele

**Example:**
```bash
GET /api/food-diary/search/multilingual?query=pap&language=zu&limit=10
```

**Response:**
```json
{
  "success": true,
  "count": 3,
  "data": [...],
  "searchLanguage": "zu"
}
```

### 3. Advanced Nutrition Search

**Endpoint:** `GET /search/nutrition`

**Description:** Search foods based on nutritional criteria.

**Query Parameters:**
- `maxCarbs` (optional): Maximum carbohydrates per 100g
- `minProtein` (optional): Minimum protein per 100g
- `maxCalories` (optional): Maximum calories per 100g
- `glycemicIndex` (optional): Glycemic index (low, medium, high)
- `category` (optional): Food category
- `limit` (optional): Maximum results (default: 50)

**Example:**
```bash
GET /api/food-diary/search/nutrition?maxCarbs=10&minProtein=20&glycemicIndex=low
```

**Response:**
```json
{
  "success": true,
  "count": 8,
  "data": [...],
  "searchCriteria": {
    "maxCarbs": 10,
    "minProtein": 20,
    "glycemicIndex": "low"
  }
}
```

### 4. Foods by Category

**Endpoint:** `GET /foods/category/:category`

**Description:** Get foods by specific category.

**Categories:**
- `grains_starches`
- `vegetables`
- `fruits`
- `proteins`
- `dairy`
- `fats_oils`
- `beverages`
- `snacks_sweets`
- `traditional_sa`
- `fast_food`

**Example:**
```bash
GET /api/food-diary/foods/category/traditional_sa?limit=20
```

### 5. Foods by Origin

**Endpoint:** `GET /foods/origin/:origin`

**Description:** Get foods by origin type.

**Origins:**
- `traditional_sa` - Traditional South African foods
- `international` - International foods
- `local_adaptation` - Locally adapted international foods

**Example:**
```bash
GET /api/food-diary/foods/origin/traditional_sa?limit=50
```

### 6. Foods by Dietary Tags

**Endpoint:** `GET /foods/dietary`

**Description:** Get foods matching specific dietary requirements.

**Query Parameters:**
- `tags` (required): Comma-separated list of dietary tags
- `limit` (optional): Maximum results (default: 100)

**Dietary Tags:**
- `halal`
- `kosher`
- `vegan`
- `vegetarian`
- `gluten-free`
- `dairy-free`
- `nut-free`
- `low-sodium`
- `high-protein`
- `low-carb`
- `keto-friendly`

**Example:**
```bash
GET /api/food-diary/foods/dietary?tags=vegan,gluten-free,high-protein&limit=30
```

### 7. Diabetes-Friendly Foods

**Endpoint:** `GET /foods/diabetes-friendly`

**Description:** Get foods suitable for diabetics (low carbs and low GI).

**Query Parameters:**
- `limit` (optional): Maximum results (default: 50)

**Example:**
```bash
GET /api/food-diary/foods/diabetes-friendly?limit=25
```

### 8. Traditional South African Foods

**Endpoint:** `GET /foods/traditional-sa`

**Description:** Get traditional South African foods with multilingual names.

**Query Parameters:**
- `limit` (optional): Maximum results (default: 100)

**Example:**
```bash
GET /api/food-diary/foods/traditional-sa?limit=50
```

## Food Data Structure

Each food item contains the following fields:

```json
{
  "_id": "unique_identifier",
  "name": "Food Name",
  "category": "food_category",
  "caloriesPer100g": 165,
  "carbohydratesPer100g": 0,
  "proteinPer100g": 31,
  "fatPer100g": 3.6,
  "fiberPer100g": 0,
  "sugarPer100g": 0,
  "sodiumPer100g": 74,
  "glycemicIndex": "low",
  "glycemicLoad": 0,
  "isTraditionalSA": false,
  "origin": "international",
  "availability": "year_round",
  "nameTranslations": {
    "english": "Chicken Breast",
    "zulu": "Isifuba senkukhu",
    "afrikaans": "Hoenderbors"
  },
  "localNames": [
    {
      "language": "en",
      "name": "Chicken breast",
      "isCommon": true
    }
  ],
  "commonPortions": [
    {
      "description": "1 breast (150g)",
      "weight": 150,
      "unit": "g",
      "isCommon": true
    }
  ],
  "vitamins": {
    "vitaminA": 6,
    "vitaminC": 0,
    "niacin": 8.5,
    "vitaminB6": 0.5
  },
  "minerals": {
    "iron": 0.7,
    "calcium": 15,
    "potassium": 256,
    "phosphorus": 196
  },
  "dietaryTags": ["high-protein", "low-carb"],
  "healthBenefits": ["High protein", "Low fat", "B vitamins"],
  "diabeticNotes": "Excellent choice - high protein, no carbs",
  "region": "Nationwide",
  "seasonality": ["year_round"],
  "description": "Lean protein source, very popular in SA",
  "preparationNotes": "Can be grilled, baked, or pan-fried",
  "allergens": [],
  "isVerified": true,
  "source": "USDA Food Database"
}
```

## Error Responses

All endpoints return consistent error responses:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error message"
}
```

## Database Seeding

To populate the database with food data:

```bash
# Seed the database (skips if data exists)
npm run seed:foods

# Force re-seed (clears existing data)
npm run seed:foods:force

# Clear all food data
npm run seed:foods:clear
```

## Usage Examples

### Search for Traditional Foods in Zulu
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:5000/api/food-diary/search/multilingual?query=iphutu&language=zu"
```

### Find Low-Carb, High-Protein Foods
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:5000/api/food-diary/search/nutrition?maxCarbs=5&minProtein=15"
```

### Get Vegan Traditional SA Foods
```bash
curl -H "Authorization: Bearer <token>" \
  "http://localhost:5000/api/food-diary/foods/dietary?tags=vegan" | \
  jq '.data[] | select(.isTraditionalSA == true)'
```
