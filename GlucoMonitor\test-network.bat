@echo off
REM Network Connectivity Test Script for GlucoMonitor (Windows)
REM This script helps test network connectivity between client and backend

setlocal enabledelayedexpansion

set "INFO=[INFO]"
set "SUCCESS=[SUCCESS]"
set "WARNING=[WARNING]"
set "ERROR=[ERROR]"

echo %INFO% GlucoMonitor Network Connectivity Test
echo ======================================

REM Test 1: Check if backend is running locally
echo %INFO% Test 1: Checking localhost backend...
curl -f -s http://localhost:5000/api/health >nul 2>&1
if errorlevel 1 (
    echo %ERROR% Backend is not running on localhost:5000
    echo %WARNING% Please start the backend server first:
    echo   cd backend ^&^& npm run dev
    pause
    exit /b 1
) else (
    echo %SUCCESS% Backend is running on localhost:5000
    curl -s http://localhost:5000/api/health
)

echo.

REM Test 2: Get network interfaces
echo %INFO% Test 2: Network interface information...
ipconfig | findstr /C:"IPv4 Address"

echo.

REM Test 3: Test common IP ranges
echo %INFO% Test 3: Testing network accessibility...
echo %INFO% Testing common network IP ranges...

REM Get the current computer's IP addresses
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /C:"IPv4 Address"') do (
    set "ip=%%a"
    set "ip=!ip: =!"
    if not "!ip!"=="127.0.0.1" (
        echo %INFO% Testing backend accessibility on !ip!:5000...
        curl -f -s http://!ip!:5000/api/health >nul 2>&1
        if errorlevel 1 (
            echo %WARNING% Backend is not accessible on !ip!:5000
        ) else (
            echo %SUCCESS% Backend is accessible on !ip!:5000
            echo Use this URL in your Flutter app:
            echo BACKEND_URL=http://!ip!:5000/api
        )
    )
)

echo.

REM Test 4: Port availability
echo %INFO% Test 4: Checking port 5000 availability...
netstat -an | findstr ":5000"

echo.

REM Test 5: Firewall check
echo %INFO% Test 5: Firewall recommendations...
echo %WARNING% If the backend is not accessible from other devices:
echo 1. Check Windows Defender Firewall
echo 2. Go to Windows Security ^> Firewall ^& network protection
echo 3. Click "Allow an app through firewall"
echo 4. Add Node.js or allow port 5000 for incoming connections
echo 5. Make sure "Private" and "Public" are both checked

echo.

REM Test 6: Device-specific configurations
echo %INFO% Test 6: Device-specific configuration...
echo %SUCCESS% For Android Emulator, use: http://********:5000/api
echo %SUCCESS% For iOS Simulator, use: http://localhost:5000/api
echo %SUCCESS% For physical devices, use: http://YOUR_IP:5000/api

echo.

REM Test 7: Quick connectivity test
echo %INFO% Test 7: Quick connectivity test...
echo %INFO% Testing if port 5000 is reachable...
powershell -Command "Test-NetConnection -ComputerName localhost -Port 5000" 2>nul
if errorlevel 1 (
    echo %WARNING% PowerShell Test-NetConnection not available or failed
) else (
    echo %SUCCESS% Port connectivity test completed
)

echo.
echo %INFO% Network test completed!
echo %WARNING% Update client\.env with the correct BACKEND_URL based on the test results above.
echo.
echo Common configurations:
echo   Android Emulator: BACKEND_URL=http://********:5000/api
echo   Physical Device:  BACKEND_URL=http://YOUR_COMPUTER_IP:5000/api
echo   iOS Simulator:    BACKEND_URL=http://localhost:5000/api

pause
endlocal
