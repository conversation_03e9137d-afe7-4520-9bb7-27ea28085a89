import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/glucose_reading.dart';

class StatisticsDashboard extends StatelessWidget {
  final List<GlucoseReading> readings;

  const StatisticsDashboard({super.key, required this.readings});

  @override
  Widget build(BuildContext context) {
    if (readings.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Text(
            'No data available for this period',
            style: TextStyle(color: Colors.grey[600], fontSize: 16),
          ),
        ),
      );
    }

    final stats = _calculateStats();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Statistics',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 16),
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            mainAxisSpacing: 12,
            crossAxisSpacing: 12,
            childAspectRatio: 1.5,
            children: [
              _StatCard(
                title: 'Average',
                value: '${stats.average.toStringAsFixed(1)} mg/dL',
                icon: Icons.trending_up,
                color: _getAverageColor(stats.average),
                trend: _getAverageTrend(stats.average),
              ),
              _StatCard(
                title: 'In Range',
                value: '${stats.inRangePercentage.toStringAsFixed(0)}%',
                icon: Icons.check_circle,
                color: AppColors.secondary,
                trend: TrendDirection.neutral,
              ),
              _StatCard(
                title: 'High Readings',
                value: '${stats.highPercentage.toStringAsFixed(0)}%',
                icon: Icons.arrow_upward,
                color: Colors.orange,
                trend: TrendDirection.neutral,
              ),
              _StatCard(
                title: 'Low Readings',
                value: '${stats.lowPercentage.toStringAsFixed(0)}%',
                icon: Icons.arrow_downward,
                color: Colors.blue,
                trend: TrendDirection.neutral,
              ),
            ],
          ),
        ],
      ),
    );
  }

  _GlucoseStats _calculateStats() {
    final total = readings.length;
    final sum = readings.fold<double>(0, (sum, reading) => sum + reading.value);
    final average = sum / total;

    final inRange =
        readings.where((r) => r.value >= 80 && r.value <= 180).length;
    final high = readings.where((r) => r.value > 180).length;
    final low = readings.where((r) => r.value < 80).length;

    return _GlucoseStats(
      average: average,
      inRangePercentage: (inRange / total) * 100,
      highPercentage: (high / total) * 100,
      lowPercentage: (low / total) * 100,
    );
  }

  Color _getAverageColor(double average) {
    if (average < 80) return Colors.blue;
    if (average <= 180) return AppColors.secondary;
    if (average <= 250) return Colors.orange;
    return Colors.red;
  }

  TrendDirection _getAverageTrend(double average) {
    // In a real app, you'd compare with previous period
    if (average >= 80 && average <= 180) return TrendDirection.neutral;
    return TrendDirection.neutral;
  }
}

class _StatCard extends StatelessWidget {
  final String title;
  final String value;
  final IconData icon;
  final Color color;
  final TrendDirection trend;

  const _StatCard({
    required this.title,
    required this.value,
    required this.icon,
    required this.color,
    required this.trend,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [Icon(icon, color: color, size: 20), _buildTrendIcon()],
          ),
          const Spacer(),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 2),
          Text(title, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
        ],
      ),
    );
  }

  Widget _buildTrendIcon() {
    IconData trendIcon;
    Color trendColor;

    switch (trend) {
      case TrendDirection.up:
        trendIcon = Icons.trending_up;
        trendColor = Colors.green;
        break;
      case TrendDirection.down:
        trendIcon = Icons.trending_down;
        trendColor = Colors.red;
        break;
      case TrendDirection.neutral:
        trendIcon = Icons.trending_flat;
        trendColor = Colors.grey;
        break;
    }

    return Icon(trendIcon, color: trendColor, size: 16);
  }
}

enum TrendDirection { up, down, neutral }

class _GlucoseStats {
  final double average;
  final double inRangePercentage;
  final double highPercentage;
  final double lowPercentage;

  _GlucoseStats({
    required this.average,
    required this.inRangePercentage,
    required this.highPercentage,
    required this.lowPercentage,
  });
}
