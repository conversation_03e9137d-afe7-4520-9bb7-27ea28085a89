import 'package:flutter/material.dart';
import '../../models/food_entry.dart';
import '../../models/food_recognition_result.dart';
import '../../constants/app_colors.dart';

/// Screen for reviewing and selecting multiple detected foods
class MultiFoodDetectionScreen extends StatefulWidget {
  final FoodRecognitionResult recognitionResult;
  final MealType mealType;
  final Function(List<FoodEntry>) onFoodsSelected;

  const MultiFoodDetectionScreen({
    super.key,
    required this.recognitionResult,
    required this.mealType,
    required this.onFoodsSelected,
  });

  @override
  State<MultiFoodDetectionScreen> createState() =>
      _MultiFoodDetectionScreenState();
}

class _MultiFoodDetectionScreenState extends State<MultiFoodDetectionScreen> {
  late List<DetectedFood> _detectedItems;
  late List<FoodEntry> _selectedFoods;

  @override
  void initState() {
    super.initState();
    _initializeDetectedItems();
  }

  void _initializeDetectedItems() {
    // Convert FoodEntry list to DetectedFood list if needed
    if (widget.recognitionResult.detectedItems != null) {
      _detectedItems = List.from(widget.recognitionResult.detectedItems!);
    } else {
      // Create DetectedFood items from FoodEntry list
      _detectedItems =
          widget.recognitionResult.detectedFoods.map((food) {
            return DetectedFood(
              name: food.name,
              confidence: 0.8, // Default confidence
              boundingBox: const Rect.fromLTWH(0, 0, 100, 100), // Default box
              category: food.category,
              isSelected: true,
            );
          }).toList();
    }

    _selectedFoods = List.from(widget.recognitionResult.detectedFoods);
  }

  void _toggleFoodSelection(int index) {
    setState(() {
      _detectedItems[index] = _detectedItems[index].copyWith(
        isSelected: !_detectedItems[index].isSelected,
      );

      // Update selected foods list
      if (_detectedItems[index].isSelected) {
        if (index < widget.recognitionResult.detectedFoods.length) {
          _selectedFoods.add(widget.recognitionResult.detectedFoods[index]);
        }
      } else {
        _selectedFoods.removeWhere(
          (food) => food.name == _detectedItems[index].name,
        );
      }
    });
  }

  void _adjustPortion(int index, double newPortion) {
    if (index < _selectedFoods.length) {
      setState(() {
        _selectedFoods[index] = _selectedFoods[index].copyWith(
          portionSize: newPortion,
          portion: '${newPortion.toInt()}g',
        );
      });
    }
  }

  void _confirmSelection() {
    widget.onFoodsSelected(
      _selectedFoods.where((food) {
        return _detectedItems.any(
          (detected) => detected.name == food.name && detected.isSelected,
        );
      }).toList(),
    );
    Navigator.of(context).pop();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Select Detected Foods'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _selectedFoods.isNotEmpty ? _confirmSelection : null,
            child: Text(
              'Add (${_selectedFoods.where((food) {
                return _detectedItems.any((detected) => detected.name == food.name && detected.isSelected);
              }).length})',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          // Detection summary
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              border: Border(
                bottom: BorderSide(
                  color: AppColors.primary.withValues(alpha: 0.2),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Found ${_detectedItems.length} food items',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: AppColors.primary,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Tap items to select/deselect for your ${widget.mealType.displayName.toLowerCase()}',
                  style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          // Detected foods list
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: _detectedItems.length,
              itemBuilder: (context, index) {
                final detectedFood = _detectedItems[index];
                final foodEntry =
                    index < widget.recognitionResult.detectedFoods.length
                        ? widget.recognitionResult.detectedFoods[index]
                        : null;

                return _buildFoodItem(detectedFood, foodEntry, index);
              },
            ),
          ),

          // Action buttons
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withValues(alpha: 0.2),
                  spreadRadius: 1,
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      side: const BorderSide(color: AppColors.primary),
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: const Text(
                      'Cancel',
                      style: TextStyle(color: AppColors.primary),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        _selectedFoods.isNotEmpty ? _confirmSelection : null,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      'Add Selected (${_selectedFoods.where((food) {
                        return _detectedItems.any((detected) => detected.name == food.name && detected.isSelected);
                      }).length})',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodItem(
    DetectedFood detectedFood,
    FoodEntry? foodEntry,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              detectedFood.isSelected
                  ? AppColors.primary
                  : Colors.grey.withValues(alpha: 0.3),
          width: detectedFood.isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: InkWell(
        onTap: () => _toggleFoodSelection(index),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  // Selection checkbox
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color:
                          detectedFood.isSelected
                              ? AppColors.primary
                              : Colors.transparent,
                      border: Border.all(
                        color:
                            detectedFood.isSelected
                                ? AppColors.primary
                                : Colors.grey,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child:
                        detectedFood.isSelected
                            ? const Icon(
                              Icons.check,
                              color: Colors.white,
                              size: 16,
                            )
                            : null,
                  ),
                  const SizedBox(width: 12),

                  // Food info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          detectedFood.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: _getConfidenceColor(
                                  detectedFood.confidence,
                                ),
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                '${(detectedFood.confidence * 100).toInt()}% confident',
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              detectedFood.category.displayName,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),

              // Nutrition info and portion adjustment
              if (detectedFood.isSelected && foodEntry != null) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 8),

                // Nutrition summary
                Row(
                  children: [
                    _buildNutritionChip('${foodEntry.calories.toInt()} cal'),
                    const SizedBox(width: 8),
                    _buildNutritionChip(
                      '${foodEntry.carbohydrates.toInt()}g carbs',
                    ),
                    const SizedBox(width: 8),
                    _buildNutritionChip(
                      '${foodEntry.protein.toInt()}g protein',
                    ),
                  ],
                ),

                const SizedBox(height: 12),

                // Portion adjustment
                Row(
                  children: [
                    const Text(
                      'Portion: ',
                      style: TextStyle(fontWeight: FontWeight.w500),
                    ),
                    Expanded(
                      child: Slider(
                        value: foodEntry.portionSize,
                        min: 50,
                        max: 500,
                        divisions: 18,
                        activeColor: AppColors.primary,
                        label: '${foodEntry.portionSize.toInt()}g',
                        onChanged: (value) => _adjustPortion(index, value),
                      ),
                    ),
                    Text(
                      '${foodEntry.portionSize.toInt()}g',
                      style: const TextStyle(fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNutritionChip(String text) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w500),
      ),
    );
  }

  Color _getConfidenceColor(double confidence) {
    if (confidence >= 0.8) return Colors.green;
    if (confidence >= 0.6) return Colors.orange;
    return Colors.red;
  }
}
