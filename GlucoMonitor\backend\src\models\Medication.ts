import mongoose, { Document, Schema } from 'mongoose';

// Enums matching frontend
export enum MedicationType {
    TABLET = 'tablet',
    CAPSULE = 'capsule',
    LIQUID = 'liquid',
    INJECTION = 'injection',
    INHALER = 'inhaler',
    CREAM = 'cream',
    DROPS = 'drops',
    PATCH = 'patch',
    OTHER = 'other'
}

export enum MedicationFrequency {
    ONCE_DAILY = 'onceDaily',
    TWICE_DAILY = 'twiceDaily',
    THREE_TIMES_DAILY = 'threeTimesDaily',
    FOUR_TIMES_DAILY = 'fourTimesDaily',
    EVERY_OTHER_DAY = 'everyOtherDay',
    WEEKLY = 'weekly',
    AS_NEEDED = 'asNeeded',
    CUSTOM = 'custom'
}

// Medication time interface
export interface IMedicationTime {
    time: string; // HH:mm format
    label?: string;
}

// Medication interface
export interface IMedication extends Document {
    userId: mongoose.Types.ObjectId;
    name: string;
    brandName?: string;
    type: MedicationType;
    dosage: number;
    dosageUnit: string;
    frequency: MedicationFrequency;
    customTimes: IMedicationTime[];
    startDate: Date;
    endDate?: Date;
    isActive: boolean;
    reminderEnabled: boolean;
    reminderMinutesBefore: number;
    notes?: string;
    color?: string;
    prescribedBy?: string;
    refillReminder?: boolean;
    refillDate?: Date;
    createdAt: Date;
    updatedAt: Date;

    // Virtual properties
    displayName: string;
    dosageDisplay: string;
    typeDisplay: string;
    frequencyDisplay: string;
}

const medicationTimeSchema = new Schema<IMedicationTime>({
    time: {
        type: String,
        required: true,
        match: [/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Time must be in HH:mm format']
    },
    label: {
        type: String,
        trim: true
    }
}, { _id: false });

const medicationSchema = new Schema<IMedication>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
        index: true
    },
    name: {
        type: String,
        required: [true, 'Medication name is required'],
        trim: true,
        maxlength: [100, 'Medication name cannot exceed 100 characters']
    },
    brandName: {
        type: String,
        trim: true,
        maxlength: [100, 'Brand name cannot exceed 100 characters']
    },
    type: {
        type: String,
        enum: Object.values(MedicationType),
        required: [true, 'Medication type is required']
    },
    dosage: {
        type: Number,
        required: [true, 'Dosage is required'],
        min: [0.01, 'Dosage must be greater than 0']
    },
    dosageUnit: {
        type: String,
        required: [true, 'Dosage unit is required'],
        trim: true,
        maxlength: [20, 'Dosage unit cannot exceed 20 characters']
    },
    frequency: {
        type: String,
        enum: Object.values(MedicationFrequency),
        required: [true, 'Frequency is required']
    },
    customTimes: {
        type: [medicationTimeSchema],
        default: [],
        validate: {
            validator: function(this: IMedication, times: IMedicationTime[]) {
                if (this.frequency === MedicationFrequency.CUSTOM) {
                    return times.length > 0;
                }
                return true;
            },
            message: 'Custom times are required when frequency is custom'
        }
    },
    startDate: {
        type: Date,
        required: [true, 'Start date is required'],
        default: Date.now
    },
    endDate: {
        type: Date,
        validate: {
            validator: function(this: IMedication, endDate: Date) {
                if (endDate) {
                    return endDate > this.startDate;
                }
                return true;
            },
            message: 'End date must be after start date'
        }
    },
    isActive: {
        type: Boolean,
        default: true,
        index: true
    },
    reminderEnabled: {
        type: Boolean,
        default: true
    },
    reminderMinutesBefore: {
        type: Number,
        default: 15,
        min: [0, 'Reminder minutes cannot be negative'],
        max: [1440, 'Reminder minutes cannot exceed 24 hours']
    },
    notes: {
        type: String,
        trim: true,
        maxlength: [500, 'Notes cannot exceed 500 characters']
    },
    color: {
        type: String,
        trim: true,
        match: [/^#[0-9A-Fa-f]{6}$/, 'Color must be a valid hex color']
    },
    prescribedBy: {
        type: String,
        trim: true,
        maxlength: [100, 'Prescribed by cannot exceed 100 characters']
    },
    refillReminder: {
        type: Boolean,
        default: false
    },
    refillDate: {
        type: Date
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
medicationSchema.index({ userId: 1, isActive: 1 });
medicationSchema.index({ userId: 1, startDate: 1 });
medicationSchema.index({ userId: 1, endDate: 1 });

// Virtual properties
medicationSchema.virtual('displayName').get(function(this: IMedication) {
    return this.brandName ? `${this.brandName} (${this.name})` : this.name;
});

medicationSchema.virtual('dosageDisplay').get(function(this: IMedication) {
    return `${this.dosage} ${this.dosageUnit}`;
});

medicationSchema.virtual('typeDisplay').get(function(this: IMedication) {
    const typeMap: Record<MedicationType, string> = {
        [MedicationType.TABLET]: 'Tablet',
        [MedicationType.CAPSULE]: 'Capsule',
        [MedicationType.LIQUID]: 'Liquid',
        [MedicationType.INJECTION]: 'Injection',
        [MedicationType.INHALER]: 'Inhaler',
        [MedicationType.CREAM]: 'Cream',
        [MedicationType.DROPS]: 'Drops',
        [MedicationType.PATCH]: 'Patch',
        [MedicationType.OTHER]: 'Other'
    };
    return typeMap[this.type];
});

medicationSchema.virtual('frequencyDisplay').get(function(this: IMedication) {
    const frequencyMap: Record<MedicationFrequency, string> = {
        [MedicationFrequency.ONCE_DAILY]: 'Once daily',
        [MedicationFrequency.TWICE_DAILY]: 'Twice daily',
        [MedicationFrequency.THREE_TIMES_DAILY]: 'Three times daily',
        [MedicationFrequency.FOUR_TIMES_DAILY]: 'Four times daily',
        [MedicationFrequency.EVERY_OTHER_DAY]: 'Every other day',
        [MedicationFrequency.WEEKLY]: 'Weekly',
        [MedicationFrequency.AS_NEEDED]: 'As needed',
        [MedicationFrequency.CUSTOM]: 'Custom schedule'
    };
    return frequencyMap[this.frequency];
});

// Pre-save middleware
medicationSchema.pre<IMedication>('save', function(next) {
    // Ensure end date is after start date if provided
    if (this.endDate && this.endDate <= this.startDate) {
        next(new Error('End date must be after start date'));
        return;
    }

    // Set default color if not provided
    if (!this.color) {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'];
        this.color = colors[Math.floor(Math.random() * colors.length)];
    }

    next();
});

const Medication = mongoose.model<IMedication>('Medication', medicationSchema);

export default Medication;
