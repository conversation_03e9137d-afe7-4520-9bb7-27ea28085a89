# 🧪 Testing Export Functionality

## 📋 Prerequisites

1. **Backend server running** on port 5000
2. **MongoDB connected** with test data
3. **Environment variables configured** (`.env` file)
4. **Google Service Account J<PERSON><PERSON> key** (if testing Google Drive)

## 🚀 Quick Test Setup

### Step 1: Start the Backend
```bash
cd backend
npm run dev
```

### Step 2: Get Authentication Token
```bash
# Update credentials in get-test-token.js first
node get-test-token.js
```

### Step 3: Run Export Tests
```bash
# Copy the token from step 2 into test-export.js
node test-export.js
```

## 🔧 Manual Testing with cURL

### 1. Test JSON Export (Basic)
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=json&chartType=line" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. Test PDF Export
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=pdf&chartType=line" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  --output glucose_report.pdf
```

### 3. Test Excel Export
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=excel&chartType=bar" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  --output glucose_data.xlsx
```

### 4. Test CSV Export
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=csv&chartType=area" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  --output glucose_data.csv
```

### 5. Test Email Export
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=pdf&destination=email&email=<EMAIL>" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 6. Test Google Drive Export
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=excel&destination=googledrive" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 7. Test Dropbox Export
```bash
curl -X GET "http://localhost:5000/api/glucose/export/chart?format=pdf&destination=dropbox" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 8. Test Bulk Export
```bash
curl -X POST "http://localhost:5000/api/glucose/export/bulk" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "chartType": "line",
    "formats": ["pdf", "excel", "csv"],
    "destinations": ["download"],
    "includeAllFormats": false
  }'
```

## 🔍 What to Check

### ✅ Successful Export Should:
- Return 200 status code
- Include proper Content-Type headers
- Generate files with correct extensions
- Contain actual glucose data
- Include proper metadata

### ❌ Common Issues:

1. **401 Unauthorized**
   - Check JWT token is valid
   - Ensure user exists and is authenticated

2. **400 Bad Request**
   - Verify parameter formats
   - Check required fields (email for email destination)

3. **500 Server Error**
   - Check environment variables
   - Verify service account credentials
   - Check MongoDB connection

## 📊 Test Data Requirements

For meaningful tests, ensure you have:
- At least 10 glucose readings in the database
- Readings with different timestamps
- Various glucose values (normal, high, low)
- Different meal timings and contexts

### Create Test Data (Optional)
```javascript
// Run this in MongoDB or through the API
const testReadings = [
  { userId: "USER_ID", value: 95, timestamp: new Date("2024-01-01T08:00:00Z"), mealTiming: "before_breakfast" },
  { userId: "USER_ID", value: 140, timestamp: new Date("2024-01-01T10:00:00Z"), mealTiming: "after_breakfast" },
  { userId: "USER_ID", value: 110, timestamp: new Date("2024-01-01T14:00:00Z"), mealTiming: "before_lunch" },
  // Add more test data...
];
```

## 🐛 Debugging Tips

### Check Logs
```bash
# Backend logs
docker logs glucomonitor-backend

# Or if running locally
npm run dev
```

### Verify Environment Variables
```bash
# Check if variables are loaded
node -e "console.log(process.env.EMAIL_USER)"
node -e "console.log(process.env.GOOGLE_DRIVE_FOLDER_ID)"
```

### Test Individual Components

1. **Email Configuration**
   ```javascript
   const nodemailer = require('nodemailer');
   const transporter = nodemailer.createTransport({
     service: 'gmail',
     auth: { user: process.env.EMAIL_USER, pass: process.env.EMAIL_PASSWORD }
   });
   transporter.verify().then(console.log).catch(console.error);
   ```

2. **Google Drive Access**
   ```javascript
   const { google } = require('googleapis');
   const auth = new google.auth.GoogleAuth({
     keyFile: process.env.GOOGLE_SERVICE_ACCOUNT_KEY,
     scopes: ['https://www.googleapis.com/auth/drive.file']
   });
   auth.getClient().then(() => console.log('Google Auth OK')).catch(console.error);
   ```

## 📈 Expected Results

### PDF Export
- Professional report with header
- Summary statistics
- Recent readings table
- Proper formatting

### Excel Export
- Multiple worksheets
- Summary, Raw Data, Chart Data sheets
- Proper column headers
- Formatted data

### CSV Export
- Comma-separated values
- Headers: Index, Value, Timestamp, Category
- ISO timestamp format

### JSON Export
- Structured data with metadata
- Chart data points
- Export timestamp
- User statistics

## 🎯 Success Criteria

✅ **All formats export without errors**  
✅ **Files contain actual user data**  
✅ **Email delivery works**  
✅ **Cloud storage uploads succeed**  
✅ **Bulk export processes multiple formats**  
✅ **Error handling works for invalid requests**  

## 📞 Need Help?

If tests fail:
1. Check the error messages in console
2. Verify environment configuration
3. Ensure all dependencies are installed
4. Check service account permissions
5. Verify email credentials
