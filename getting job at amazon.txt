getting job at amazon

- Knowledge of Computer Science fundamentals such as object-oriented design, algorithm design, data structures, problem solving and complexity analysis.

Key job responsibilities
- Collaborate with experienced cross-disciplinary Amazonians to conceive, design, and bring to market innovative products and services.
- Design and build innovative technologies in a large distributed computing environment and help lead fundamental changes in the industry.
- Create solutions to run predictions on distributed systems with exposure to innovative technologies at incredible scale and speed.
- Build distributed storage, index, and query systems that are scalable, fault-tolerant, low cost, and easy to manage/use.
- Work in an agile environment to deliver high quality software.