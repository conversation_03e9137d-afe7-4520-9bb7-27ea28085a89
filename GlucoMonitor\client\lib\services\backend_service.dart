import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import '../utils/retry_utils.dart';
import './offline_queue.dart';

class BackendService {
  static String get baseUrl {
    if (kReleaseMode) {
      // Use your production server URL
      return 'https://your-production-server.com/api';
    } else {
      // For development, use environment variable or fallback to localhost
      return dotenv.env['BACKEND_URL'] ?? 'http://localhost:5000/api';
    }
  }

  static final OfflineQueue _offlineQueue = OfflineQueue();
  static bool _initialized = false;
  static final ValueNotifier<String?> retryStatus = ValueNotifier<String?>(
    null,
  );

  static Future<void> init() async {
    if (!_initialized) {
      await _offlineQueue.init();
      _initialized = true;
      // Start offline sync in the background without blocking
      _startOfflineSync().catchError((e) {
        debugPrint('Offline sync error: $e');
      });
    }
  }

  static final _defaultRetryConfig = RetryConfig(
    maxAttempts: 3,
    initialDelay: const Duration(seconds: 1),
    backoffMultiplier: 2.0,
    maxDelay: const Duration(seconds: 10),
  );

  static bool _shouldRetryRequest(Exception e) {
    if (e is http.ClientException) {
      // Retry network errors
      return true;
    }
    if (e is RetryException) {
      // Don't retry if it's already a retry exception
      return false;
    }
    // Add more conditions as needed
    return false;
  }

  static void _updateRetryStatus(String message) {
    retryStatus.value = message;
    // Clear status after 3 seconds
    Future.delayed(const Duration(seconds: 3), () {
      if (retryStatus.value == message) {
        retryStatus.value = null;
      }
    });
  }

  static Future<void> _startOfflineSync() async {
    while (true) {
      if (!await checkConnection()) {
        await Future.delayed(const Duration(seconds: 30));
        continue;
      }

      final operations = await _offlineQueue.getOperations();
      for (final operation in operations) {
        try {
          switch (operation.type) {
            case 'syncUser':
              await syncUser(
                firebaseToken: operation.data['firebaseToken'],
                phoneNumber: operation.data['phoneNumber'],
                name: operation.data['name'],
                ageGroup: operation.data['ageGroup'],
                diabetesType: operation.data['diabetesType'],
                language: operation.data['language'],
                givePopiaConsent: operation.data['givePopiaConsent'],
              );
              break;
            // Add more operation types as needed
          }
          await _offlineQueue.remove(operation);
        } catch (e) {
          debugPrint('Failed to sync operation: ${operation.type} - $e');
        }
      }
      await Future.delayed(const Duration(minutes: 1));
    }
  }

  // Sync user with backend after Firebase auth
  static Future<Map<String, dynamic>> syncUser({
    required String firebaseToken,
    required String phoneNumber,
    String? name,
    String? ageGroup,
    String? diabetesType,
    String? language,
    bool? givePopiaConsent,
  }) async {
    try {
      return await withRetry(
        operation: () async {
          final response = await http.post(
            Uri.parse('$baseUrl/auth/update-profile'),
            headers: {
              'Content-Type': 'application/json',
              'Authorization': 'Bearer $firebaseToken',
            },
            body: jsonEncode({
              'phoneNumber': phoneNumber,
              if (name != null) 'name': name,
              if (ageGroup != null) 'ageGroup': ageGroup,
              if (diabetesType != null) 'diabetesType': diabetesType,
              if (language != null) 'language': language,
              if (givePopiaConsent != null)
                'givePopiaConsent': givePopiaConsent,
            }),
          );

          if (response.statusCode == 200) {
            return jsonDecode(response.body);
          }
          throw Exception(
            jsonDecode(response.body)['message'] ?? 'Failed to sync user',
          );
        },
        operationName: 'syncUser',
        config: _defaultRetryConfig,
        shouldRetry: _shouldRetryRequest,
      );
    } catch (e) {
      _updateRetryStatus('Failed to sync user data. Will retry when online.');
      // Queue for offline sync
      await _offlineQueue.add(
        QueuedOperation(
          type: 'syncUser',
          data: {
            'firebaseToken': firebaseToken,
            'phoneNumber': phoneNumber,
            if (name != null) 'name': name,
            if (ageGroup != null) 'ageGroup': ageGroup,
            if (diabetesType != null) 'diabetesType': diabetesType,
            if (language != null) 'language': language,
            if (givePopiaConsent != null) 'givePopiaConsent': givePopiaConsent,
          },
        ),
      );
      rethrow;
    }
  }

  // Send OTP via backend (Twilio)
  static Future<bool> sendOTP(String phoneNumber, String language) async {
    debugPrint('Attempting to send OTP to: $phoneNumber');
    return withRetry(
      operation: () async {
        try {
          final response = await http.post(
            Uri.parse('$baseUrl/auth/verify-phone'),
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode({
              'phoneNumber': phoneNumber,
              'language': language,
            }),
          );

          debugPrint('OTP API Response Status: ${response.statusCode}');
          debugPrint('OTP API Response Body: ${response.body}');

          if (response.statusCode == 200) {
            return true;
          }
          throw Exception(
            jsonDecode(response.body)['message'] ?? 'Failed to send OTP',
          );
        } catch (e) {
          debugPrint('Error sending OTP: $e');
          rethrow;
        }
      },
      operationName: 'sendOTP',
      config: _defaultRetryConfig.copyWith(maxAttempts: 2),
    );
  }

  // Get user profile from backend
  static Future<Map<String, dynamic>> getProfile(String token) async {
    return withRetry(
      operation: () async {
        final response = await http.get(
          Uri.parse('$baseUrl/auth/me'),
          headers: {'Authorization': 'Bearer $token'},
        );

        if (response.statusCode == 200) {
          return jsonDecode(response.body);
        }
        throw Exception(
          jsonDecode(response.body)['message'] ?? 'Failed to get profile',
        );
      },
      operationName: 'getProfile',
      config: _defaultRetryConfig,
      shouldRetry: _shouldRetryRequest,
    );
  }

  // Verify connection to backend using health check endpoint
  static Future<bool> checkConnection() async {
    try {
      final healthUrl = '${baseUrl.replaceAll('/api', '')}/api/health';
      debugPrint('Checking backend health at: $healthUrl');

      final response = await http
          .get(
            Uri.parse(healthUrl),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      debugPrint('Health check response: ${response.statusCode}');

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final isHealthy = data['success'] == true;
        debugPrint('Backend health status: $isHealthy');
        return isHealthy;
      }

      debugPrint('Health check failed with status: ${response.statusCode}');
      return false;
    } catch (e) {
      debugPrint('Health check failed: $e');
      debugPrint('Backend URL: $baseUrl');
      return false;
    }
  }

  // Get backend server info
  static Future<Map<String, dynamic>?> getServerInfo() async {
    try {
      final healthUrl = '${baseUrl.replaceAll('/api', '')}/api/health';
      final response = await http
          .get(
            Uri.parse(healthUrl),
            headers: {'Content-Type': 'application/json'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        return jsonDecode(response.body);
      }
      return null;
    } catch (e) {
      debugPrint('Failed to get server info: $e');
      return null;
    }
  }
}
