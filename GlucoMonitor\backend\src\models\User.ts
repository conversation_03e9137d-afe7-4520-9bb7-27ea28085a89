import mongoose, { Document, Schema } from 'mongoose';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';

// User interface
export interface IUser extends Document {
    email: string;
    password: string;
    phoneNumber: string;
    isPhoneVerified: boolean;
    otpAttempts: number;
    lastOtpSent?: Date;
    currentOTP?: string;
    otpExpiresAt?: Date;
    name?: string;
    ageGroup?: 'Under 18' | '18-30' | '31-45' | '46-60' | 'Over 60';
    diabetesType?: 'Type 1' | 'Type 2' | 'Gestational' | 'Pre-diabetes' | 'Not sure' | null;
    language: 'af' | 'en' | 'nr' | 'xh' | 'zu' | 'nso' | 'st' | 'tn' | 'ss' | 've' | 'ts';
    popiaConsent?: Date;
    profilePicturePath?: string;
    guardianName?: string;
    guardianPhone?: string;
    guardianEmail?: string;
    resetPasswordToken?: string;
    resetPasswordExpire?: Date;
    role?: string;
    createdAt: Date;
    updatedAt: Date;

    // Methods
    getSignedJwtToken(): string;
    matchPassword(enteredPassword: string): Promise<boolean>;
    setOTP(otp: string): Promise<void>;
    matchOTP(enteredOtp: string): Promise<boolean>;
    getResetPasswordToken(): string;
}

const userSchema = new Schema<IUser>({
    email: {
        type: String,
        required: [true, 'Please provide an email address'],
        unique: true,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email address']
    },
    password: {
        type: String,
        required: [true, 'Please provide a password'],
        minlength: [8, 'Password must be at least 8 characters long']
    },
    phoneNumber: {
        type: String,
        required: [true, 'Please provide a phone number'],
        unique: true,
        trim: true,
        match: [/^\+27[1-9]\d{8}$/, 'Please enter a valid South African phone number']
    },
    isPhoneVerified: {
        type: Boolean,
        default: false
    },
    otpAttempts: {
        type: Number,
        default: 0
    },
    lastOtpSent: {
        type: Date
    },
    currentOTP: {
        type: String
    },
    otpExpiresAt: {
        type: Date
    },
    name: {
        type: String,
        trim: true
    },
    ageGroup: {
        type: String,
        enum: ['Under 18', '18-30', '31-45', '46-60', 'Over 60']
    },
    diabetesType: {
        type: String,
        enum: ['Type 1', 'Type 2', 'Gestational', 'Pre-diabetes', 'Not sure', null],
        default: null
    },
    language: {
        type: String,
        default: 'en',
        enum: ['af', 'en', 'nr', 'xh', 'zu', 'nso', 'st', 'tn', 'ss', 've', 'ts']
    },
    popiaConsent: {
        type: Date,
        default: null
    },
    profilePicturePath: {
        type: String,
        trim: true
    },
    guardianName: {
        type: String,
        trim: true
    },
    guardianPhone: {
        type: String,
        trim: true,
        match: [/^\+27[1-9]\d{8}$/, 'Please enter a valid South African phone number']
    },
    guardianEmail: {
        type: String,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email address']
    },
    resetPasswordToken: {
        type: String
    },
    resetPasswordExpire: {
        type: Date
    }
}, {
    timestamps: true
});

// Hash password before saving
userSchema.pre<IUser>('save', async function(next) {
    if (!this.isModified('password')) {
        next();
    }

    const salt = await bcrypt.genSalt(10);
    this.password = await bcrypt.hash(this.password, salt);
    next();
});

// Generate JWT token
userSchema.methods.getSignedJwtToken = function(this: IUser): string {
    return jwt.sign(
        { id: this._id },
        process.env.JWT_SECRET as string
    );
};

// Match password
userSchema.methods.matchPassword = async function(this: IUser, enteredPassword: string): Promise<boolean> {
    return await bcrypt.compare(enteredPassword, this.password);
};

// Store OTP
userSchema.methods.setOTP = async function(this: IUser, otp: string): Promise<void> {
    this.currentOTP = otp;
    this.otpExpiresAt = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes expiry
    await this.save();
};

// Match OTP
userSchema.methods.matchOTP = async function(this: IUser, enteredOtp: string): Promise<boolean> {
    if (!this.currentOTP || !this.otpExpiresAt) {
        return false;
    }

    // Check if OTP has expired
    if (Date.now() > this.otpExpiresAt.getTime()) {
        return false;
    }

    // Compare OTPs
    const isMatch = this.currentOTP === enteredOtp;

    if (isMatch) {
        // Clear OTP after successful verification
        this.currentOTP = undefined;
        this.otpExpiresAt = undefined;
        this.isPhoneVerified = true;
        await this.save();
    }

    return isMatch;
};

// Generate password reset token
userSchema.methods.getResetPasswordToken = function(this: IUser): string {
    // Generate token
    const resetToken = crypto.randomBytes(20).toString('hex');

    // Hash token and set to resetPasswordToken field
    this.resetPasswordToken = crypto
        .createHash('sha256')
        .update(resetToken)
        .digest('hex');

    // Set expire
    this.resetPasswordExpire = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

    return resetToken;
};

const User = mongoose.model<IUser>('User', userSchema);

export default User;
