import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../providers/food_diary_provider.dart';
import '../../services/food_diary_service.dart';
import '../../widgets/food_diary/enhanced_add_food_modal.dart';
import '../../services/barcode_scanner_service.dart';
import 'package:intl/intl.dart';

class FoodDiaryScreen extends StatefulWidget {
  const FoodDiaryScreen({super.key});

  @override
  State<FoodDiaryScreen> createState() => _FoodDiaryScreenState();
}

class _FoodDiaryScreenState extends State<FoodDiaryScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize the food diary provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        context.read<FoodDiaryProvider>().initialize();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Food Diary'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onBackground,
        actions: [
          // Debug button for adding sample data (development only)
          IconButton(
            icon: const Icon(Icons.bug_report),
            onPressed: _addSampleData,
            tooltip: 'Add Sample Data',
          ),
          // Date navigation
          Consumer<FoodDiaryProvider>(
            builder: (context, provider, child) {
              return Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  IconButton(
                    icon: const Icon(Icons.chevron_left),
                    onPressed: provider.goToPreviousDay,
                    tooltip: 'Previous Day',
                  ),
                  TextButton(
                    onPressed: provider.goToToday,
                    child: Text(
                      provider.formattedSelectedDate,
                      style: const TextStyle(
                        color: AppColors.onBackground,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.chevron_right),
                    onPressed:
                        provider.isFutureDate ? null : provider.goToNextDay,
                    tooltip: 'Next Day',
                  ),
                ],
              );
            },
          ),
        ],
      ),
      body: Consumer<FoodDiaryProvider>(
        builder: (context, provider, child) {
          if (provider.isLoading && provider.dailyNutrition == null) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.primary),
            );
          }

          if (provider.error != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: AppColors.error),
                  const SizedBox(height: 16),
                  Text(
                    'Error loading food diary',
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: AppColors.onBackground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    provider.error!,
                    style: const TextStyle(
                      fontSize: 14,
                      color: AppColors.textSecondary,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: provider.refresh,
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: provider.refresh,
            color: AppColors.primary,
            child: CustomScrollView(
              slivers: [
                // App Bar with date and summary
                SliverToBoxAdapter(
                  child: Container(
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: AppColors.surface,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Today\'s Nutrition',
                                  style: const TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppColors.onSurface,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  _formatDate(provider.selectedDate),
                                  style: const TextStyle(
                                    fontSize: 16,
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                              ],
                            ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              decoration: BoxDecoration(
                                color: AppColors.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                '${provider.dailyNutrition?.totalCalories.toStringAsFixed(0) ?? '0'} cal',
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.w600,
                                  color: AppColors.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _buildNutritionSummary(provider),
                        const SizedBox(height: 16),
                        _buildQuickActions(),
                      ],
                    ),
                  ),
                ),

                // Meal sections
                SliverList(
                  delegate: SliverChildBuilderDelegate((context, index) {
                    final mealType = MealType.values[index];
                    return Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                      child: _buildModernMealCard(context, provider, mealType),
                    );
                  }, childCount: MealType.values.length),
                ),

                // Bottom padding for FAB
                const SliverToBoxAdapter(child: SizedBox(height: 100)),
              ],
            ),
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "food_diary_fab",
        onPressed: _showAddFoodModal,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  void _showAddFoodModal({MealType? initialMealType}) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => EnhancedAddFoodModal(initialMealType: initialMealType),
    );
  }

  void _addSampleData() async {
    try {
      await FoodDiaryService.addSampleData();
      if (mounted) {
        final provider = context.read<FoodDiaryProvider>();
        await provider.refresh();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Sample data added successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add sample data: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selectedDay = DateTime(date.year, date.month, date.day);

    if (selectedDay == today) {
      return 'Today';
    } else if (selectedDay == today.subtract(const Duration(days: 1))) {
      return 'Yesterday';
    } else if (selectedDay == today.add(const Duration(days: 1))) {
      return 'Tomorrow';
    } else {
      final formatter = DateFormat('EEEE, MMM d');
      return formatter.format(date);
    }
  }

  Widget _buildNutritionSummary(FoodDiaryProvider provider) {
    final nutrition = provider.dailyNutrition;
    if (nutrition == null) return const SizedBox.shrink();

    return Row(
      children: [
        Expanded(
          child: _buildNutrientCard(
            'Carbs',
            '${nutrition.totalCarbohydrates.toStringAsFixed(0)}g',
            AppColors.primary,
            Icons.grain,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildNutrientCard(
            'Protein',
            '${nutrition.totalProtein.toStringAsFixed(0)}g',
            Colors.green,
            Icons.fitness_center,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildNutrientCard(
            'Fat',
            '${nutrition.totalFat.toStringAsFixed(0)}g',
            Colors.orange,
            Icons.opacity,
          ),
        ),
      ],
    );
  }

  Widget _buildNutrientCard(
    String label,
    String value,
    Color color,
    IconData icon,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      children: [
        // First row: Manual Add and AI Recognition
        Row(
          children: [
            Expanded(
              child: OutlinedButton.icon(
                onPressed: _showAddFoodModal,
                icon: const Icon(Icons.add, size: 20),
                label: const Text('Add Food'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  side: const BorderSide(color: AppColors.primary),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton.icon(
                onPressed: _showMealTypeSelectionForAI,
                icon: const Icon(Icons.camera_alt, size: 20),
                label: const Text('AI Recognize'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // Second row: Barcode Scanner
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _scanBarcode,
            icon: const Icon(Icons.qr_code_scanner, size: 20),
            label: const Text('Scan Barcode'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppColors.textSecondary,
              side: BorderSide(
                color: AppColors.textSecondary.withValues(alpha: 0.3),
              ),
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernMealCard(
    BuildContext context,
    FoodDiaryProvider provider,
    MealType mealType,
  ) {
    final mealNutrition = provider.getMealNutrition(mealType);
    final entries = mealNutrition.entries;
    final hasEntries = entries.isNotEmpty;

    return Container(
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Meal header
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // Meal icon
                Container(
                  width: 48,
                  height: 48,
                  decoration: BoxDecoration(
                    color: mealType.color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(mealType.icon, color: mealType.color, size: 24),
                ),
                const SizedBox(width: 16),

                // Meal info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mealType.displayName,
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onSurface,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (hasEntries) ...[
                        Text(
                          '${entries.length} item${entries.length == 1 ? '' : 's'} • ${mealNutrition.totalCalories.toStringAsFixed(0)} cal',
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ] else ...[
                        const Text(
                          'No foods added yet',
                          style: TextStyle(
                            fontSize: 14,
                            color: AppColors.textSecondary,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                // Add button
                Container(
                  width: 36,
                  height: 36,
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(18),
                  ),
                  child: IconButton(
                    onPressed:
                        () => _showAddFoodModal(initialMealType: mealType),
                    icon: const Icon(
                      Icons.add,
                      color: AppColors.primary,
                      size: 20,
                    ),
                    padding: EdgeInsets.zero,
                  ),
                ),
              ],
            ),
          ),

          // Food entries (if any)
          if (hasEntries) ...[
            const Divider(height: 1, color: AppColors.textSecondary),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children:
                    entries.map((entry) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 8),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.background,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    entry.name,
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                      color: AppColors.onSurface,
                                    ),
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    '${entry.portion} • ${entry.calories} cal • ${entry.carbohydrates.toStringAsFixed(0)}g carbs',
                                    style: const TextStyle(
                                      fontSize: 12,
                                      color: AppColors.textSecondary,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            IconButton(
                              onPressed: () => _deleteFoodEntry(context, entry),
                              icon: const Icon(
                                Icons.delete,
                                color: Colors.red,
                                size: 16,
                              ),
                              padding: EdgeInsets.zero,
                              constraints: const BoxConstraints(),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _deleteFoodEntry(BuildContext context, FoodEntry entry) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Delete Food Entry'),
            content: Text('Are you sure you want to delete "${entry.name}"?'),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  context.read<FoodDiaryProvider>().deleteFoodEntry(entry.id);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: const Text('Delete'),
              ),
            ],
          ),
    );
  }

  void _scanBarcode() async {
    try {
      final scannedFood = await BarcodeScannerService.showBarcodeScanner(
        context,
      );

      if (scannedFood != null && mounted) {
        // Show the add food modal with the scanned food pre-filled
        // For scanned foods, add directly to the provider
        final provider = context.read<FoodDiaryProvider>();
        await provider.addFoodEntry(scannedFood);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Added ${scannedFood.name} from barcode scan'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error scanning barcode: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showMealTypeSelectionForAI() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            decoration: const BoxDecoration(
              color: AppColors.surface,
              borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
            ),
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.textSecondary.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                const Text(
                  'Add AI Recognized Food to:',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
                const SizedBox(height: 20),

                // Meal type options - show all available meal types
                ...MealType.values.where((type) => type != MealType.custom).map(
                  (mealType) {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 12),
                      child: _buildMealTypeOption(
                        mealType,
                        mealType.icon,
                        mealType.displayName,
                      ),
                    );
                  },
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
    );
  }

  Widget _buildMealTypeOption(MealType mealType, IconData icon, String label) {
    return InkWell(
      onTap: () {
        Navigator.of(context).pop();
        _recognizeFood(mealType: mealType);
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.primary.withValues(alpha: 0.2)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(icon, color: AppColors.primary, size: 24),
            const SizedBox(width: 16),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: AppColors.onSurface,
              ),
            ),
            const Spacer(),
            Icon(Icons.camera_alt, color: AppColors.textSecondary, size: 20),
          ],
        ),
      ),
    );
  }

  void _recognizeFood({MealType? mealType}) async {
    try {
      // Show enhanced recognition options
      final result = await _showEnhancedRecognitionOptions(
        context,
        mealType ?? MealType.breakfast,
      );

      if (result != null && mounted) {
        final provider = context.read<FoodDiaryProvider>();

        if (result is List<FoodEntry>) {
          // Multiple foods detected
          for (final food in result) {
            await provider.addFoodEntry(food);
          }

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(
                  'Added ${result.length} foods from AI recognition',
                ),
                backgroundColor: Colors.green,
              ),
            );
          }
        } else if (result is FoodEntry) {
          // Single food detected
          await provider.addFoodEntry(result);

          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('Added ${result.name} from AI recognition'),
                backgroundColor: Colors.green,
              ),
            );
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error recognizing food: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Show enhanced recognition options dialog
  Future<dynamic> _showEnhancedRecognitionOptions(
    BuildContext context,
    MealType mealType,
  ) async {
    return showModalBottomSheet<dynamic>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            height: MediaQuery.of(context).size.height * 0.7,
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(20),
                topRight: Radius.circular(20),
              ),
            ),
            child: Column(
              children: [
                // Handle bar
                Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),

                // Header
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: Row(
                    children: [
                      const Text(
                        'AI Food Recognition',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const Spacer(),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                      ),
                    ],
                  ),
                ),

                const Divider(),

                // Recognition options
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      children: [
                        // Quick recognition
                        _buildRecognitionOption(
                          icon: Icons.camera_alt,
                          title: 'Quick Recognition',
                          description:
                              'Take a photo for instant food identification',
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            navigator.pop();
                            final result = await navigator.pushNamed(
                              '/ai-recognition',
                            );
                            if (mounted && result != null) {
                              navigator.pop(result);
                            }
                          },
                        ),

                        const SizedBox(height: 16),

                        // Real-time camera
                        _buildRecognitionOption(
                          icon: Icons.videocam,
                          title: 'Real-time Camera',
                          description:
                              'Live food detection with instant feedback',
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            navigator.pop();
                            final result = await _showRealtimeCamera(mealType);
                            if (mounted) {
                              navigator.pop(result);
                            }
                          },
                        ),

                        const SizedBox(height: 16),

                        // Multi-detection
                        _buildRecognitionOption(
                          icon: Icons.grid_view,
                          title: 'Multi-food Detection',
                          description: 'Identify multiple foods in one photo',
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            navigator.pop();
                            final result = await _showMultiDetection(mealType);
                            if (mounted) {
                              navigator.pop(result);
                            }
                          },
                        ),

                        const SizedBox(height: 16),

                        // Barcode scanner
                        _buildRecognitionOption(
                          icon: Icons.qr_code_scanner,
                          title: 'Barcode Scanner',
                          description:
                              'Scan product barcodes for nutrition info',
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            navigator.pop();
                            final result = await navigator.pushNamed(
                              '/barcode-scanner',
                            );
                            if (mounted && result != null) {
                              navigator.pop(result);
                            }
                          },
                        ),

                        const SizedBox(height: 16),

                        // Voice input
                        _buildRecognitionOption(
                          icon: Icons.mic,
                          title: 'Voice Input',
                          description: 'Describe your food with voice commands',
                          onTap: () async {
                            final navigator = Navigator.of(context);
                            navigator.pop();
                            final result = await navigator.pushNamed(
                              '/voice-input',
                            );
                            if (mounted && result != null) {
                              navigator.pop(result);
                            }
                          },
                        ),

                        const SizedBox(height: 16),

                        // Enhanced features demo
                        _buildRecognitionOption(
                          icon: Icons.auto_awesome,
                          title: 'Enhanced Features Demo',
                          description:
                              'Explore all AI recognition capabilities',
                          onTap: () {
                            Navigator.of(context).pop();
                            Navigator.of(
                              context,
                            ).pushNamed('/enhanced-ai-demo');
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildRecognitionOption({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey[300]!),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: AppColors.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: AppColors.primary, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    description,
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, color: Colors.grey[400], size: 16),
          ],
        ),
      ),
    );
  }

  Future<dynamic> _showRealtimeCamera(MealType mealType) async {
    // Placeholder for real-time camera implementation
    // This would show the RealtimeCameraWidget in a full-screen modal
    return showDialog<dynamic>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Real-time Camera'),
            content: const Text(
              'Real-time camera recognition would be implemented here.\n\n'
              'Features:\n'
              '• Live food detection\n'
              '• Instant feedback\n'
              '• Optimized camera settings',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }

  Future<dynamic> _showMultiDetection(MealType mealType) async {
    // Placeholder for multi-detection implementation
    return showDialog<dynamic>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Multi-food Detection'),
            content: const Text(
              'Multi-food detection would be implemented here.\n\n'
              'Features:\n'
              '• Multiple food identification\n'
              '• Individual confidence scores\n'
              '• Selective food addition',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Close'),
              ),
            ],
          ),
    );
  }
}
