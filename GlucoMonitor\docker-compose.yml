# GlucoMonitor - Docker Compose Configuration
#
# Usage:
#   Development: docker-compose --profile dev up
#   Production:  docker-compose --profile prod up
#   Testing:     docker-compose --profile test up
#
version: '3.8'

services:
  # Redis service for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: glucomonitor-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    networks:
      - glucomonitor-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Production Backend
  backend-prod:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: glucomonitor-backend-prod
    restart: unless-stopped
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
      - REDIS_URL=redis://redis:6379
      - PORT=5000
    env_file:
      - .env.docker
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - glucomonitor-network
    volumes:
      # Mount Google Service Account key for export functionality
      - ./config/google:/app/config/google:ro
      # Mount uploads directory for local exports
      - ./uploads:/app/uploads
      # Mount logs directory
      - ./backend/logs:/app/logs
    profiles:
      - prod
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development Backend
  backend-dev:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: glucomonitor-backend-dev
    ports:
      - "5000:5000"
      - "9229:9229"  # Debug port
    environment:
      - NODE_ENV=development
      - REDIS_URL=redis://redis:6379
      - PORT=5000
      - DEVELOPMENT_MODE=true
    env_file:
      - .env.docker
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - glucomonitor-network
    volumes:
      # Mount source code for hot reloading
      - ./backend/src:/app/src
      - ./backend/package.json:/app/package.json
      - ./backend/tsconfig.json:/app/tsconfig.json
      # Mount Google Service Account key for export functionality
      - ./config/google:/app/config/google:ro
      # Mount uploads directory for local exports
      - ./uploads:/app/uploads
      # Mount logs directory
      - ./backend/logs:/app/logs
      # Exclude node_modules from being overwritten
      - /app/node_modules
    profiles:
      - dev
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Test Backend
  backend-test:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: development
    container_name: glucomonitor-backend-test
    environment:
      - NODE_ENV=test
      - REDIS_URL=redis://redis:6379
      - PORT=5000
    env_file:
      - .env.test
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - glucomonitor-network
    volumes:
      - ./backend:/app
      - /app/node_modules
    profiles:
      - test
    command: ["npm", "test"]

volumes:
  redis_data:
    driver: local

networks:
  glucomonitor-network:
    driver: bridge
    name: glucomonitor-network
