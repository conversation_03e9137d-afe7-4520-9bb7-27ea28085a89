import 'dart:convert';
import 'dart:math';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:http/http.dart' as http;
import '../models/food_entry.dart';
import '../models/daily_nutrition.dart';
import 'backend_service.dart';

class FoodDiaryService {
  static const String _foodEntriesKey = 'food_entries';
  static const String _nutritionGoalsKey = 'nutrition_goals';
  static const String _bloodSugarReadingsKey = 'blood_sugar_readings';

  // Get authentication token from shared preferences
  static Future<String?> _getAuthToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('backend_token');
  }

  // Get headers with authentication
  static Future<Map<String, String>> _getHeaders() async {
    final token = await _getAuthToken();
    return {
      'Content-Type': 'application/json',
      if (token != null) 'Authorization': 'Bearer $token',
    };
  }

  // Handle API errors
  static void _handleApiError(http.Response response) {
    if (response.statusCode == 401) {
      throw Exception('Authentication required. Please log in again.');
    } else if (response.statusCode == 403) {
      throw Exception('Access denied.');
    } else if (response.statusCode >= 400) {
      try {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'API request failed');
      } catch (e) {
        throw Exception('Request failed with status ${response.statusCode}');
      }
    }
  }

  // API-based food database methods

  /// Search foods in the backend database
  static Future<List<FoodEntry>> searchFoods(
    String query, {
    FoodCategory? category,
    int limit = 50,
  }) async {
    try {
      final headers = await _getHeaders();
      final uri = Uri.parse(
        '${BackendService.baseUrl}/food-diary/search',
      ).replace(
        queryParameters: {
          'query': query,
          if (category != null) 'category': category.name,
          'limit': limit.toString(),
        },
      );

      final response = await http.get(uri, headers: headers);
      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] == true) {
        final List<dynamic> foodList = data['data'];
        return foodList
            .map((json) => _convertBackendFoodToFoodEntry(json))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error searching foods: $e');
      // Fallback to local cache if available
      return _getFallbackFoods(query);
    }
  }

  /// Get foods by category from backend
  static Future<List<FoodEntry>> getFoodsByCategory(
    FoodCategory category, {
    int limit = 100,
  }) async {
    try {
      final headers = await _getHeaders();
      final uri = Uri.parse(
        '${BackendService.baseUrl}/food-diary/foods/category/${category.name}',
      ).replace(queryParameters: {'limit': limit.toString()});

      final response = await http.get(uri, headers: headers);
      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] == true) {
        final List<dynamic> foodList = data['data'];
        return foodList
            .map((json) => _convertBackendFoodToFoodEntry(json))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting foods by category: $e');
      return _getFallbackFoodsByCategory(category);
    }
  }

  /// Get diabetes-friendly foods from backend
  static Future<List<FoodEntry>> getDiabetesFriendlyFoods({
    int limit = 50,
  }) async {
    try {
      final headers = await _getHeaders();
      final uri = Uri.parse(
        '${BackendService.baseUrl}/food-diary/foods/diabetes-friendly',
      ).replace(queryParameters: {'limit': limit.toString()});

      final response = await http.get(uri, headers: headers);
      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] == true) {
        final List<dynamic> foodList = data['data'];
        return foodList
            .map((json) => _convertBackendFoodToFoodEntry(json))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting diabetes-friendly foods: $e');
      return _getFallbackDiabetesFriendlyFoods();
    }
  }

  /// Get traditional South African foods from backend
  static Future<List<FoodEntry>> getTraditionalSAFoods({
    int limit = 100,
  }) async {
    try {
      final headers = await _getHeaders();
      final uri = Uri.parse(
        '${BackendService.baseUrl}/food-diary/foods/traditional-sa',
      ).replace(queryParameters: {'limit': limit.toString()});

      final response = await http.get(uri, headers: headers);
      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] == true) {
        final List<dynamic> foodList = data['data'];
        return foodList
            .map((json) => _convertBackendFoodToFoodEntry(json))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error getting traditional SA foods: $e');
      return _getFallbackTraditionalSAFoods();
    }
  }

  /// Convert backend food data to FoodEntry model
  static FoodEntry _convertBackendFoodToFoodEntry(Map<String, dynamic> json) {
    // Convert backend food database entry to FoodEntry
    return FoodEntry(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      carbohydrates: (json['carbohydratesPer100g'] ?? 0).toDouble(),
      calories: (json['caloriesPer100g'] ?? 0).toDouble(),
      protein: (json['proteinPer100g'] ?? 0).toDouble(),
      fat: (json['fatPer100g'] ?? 0).toDouble(),
      fiber: (json['fiberPer100g'] ?? 0).toDouble(),
      glycemicIndex: _parseGlycemicIndex(json['glycemicIndex']),
      category: _parseFoodCategory(json['category']),
      portion:
          json['commonPortions']?.isNotEmpty == true
              ? json['commonPortions'][0]['description'] ?? '100g'
              : '100g',
      portionSize:
          json['commonPortions']?.isNotEmpty == true
              ? (json['commonPortions'][0]['weight'] ?? 100).toDouble()
              : 100.0,
      unit:
          json['commonPortions']?.isNotEmpty == true
              ? json['commonPortions'][0]['unit'] ?? 'g'
              : 'g',
      mealType: MealType.breakfast, // Default, will be set when adding to diary
      timestamp: DateTime.now(),
      notes: json['description'],
      brand: json['brand'],
      isCustom: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Parse glycemic index from backend string
  static GlycemicIndex _parseGlycemicIndex(String? value) {
    switch (value?.toLowerCase()) {
      case 'low':
        return GlycemicIndex.low;
      case 'medium':
        return GlycemicIndex.medium;
      case 'high':
        return GlycemicIndex.high;
      default:
        return GlycemicIndex.medium;
    }
  }

  /// Parse food category from backend string
  static FoodCategory _parseFoodCategory(String? value) {
    switch (value) {
      case 'grains_starches':
        return FoodCategory.grains;
      case 'vegetables':
        return FoodCategory.vegetables;
      case 'fruits':
        return FoodCategory.fruits;
      case 'proteins':
        return FoodCategory.proteins;
      case 'dairy':
        return FoodCategory.dairy;
      case 'fats_oils':
        return FoodCategory.fats;
      case 'beverages':
        return FoodCategory.beverages;
      case 'snacks_sweets':
        return FoodCategory.sweets;
      case 'traditional_sa':
        return FoodCategory.grains; // Map to closest category
      case 'fast_food':
        return FoodCategory.sweets; // Map to closest category
      default:
        return FoodCategory.grains;
    }
  }

  // Fallback methods for offline mode
  static final List<FoodEntry> _fallbackFoods = [
    // Grains & Starches
    FoodEntry(
      id: 'pap_1',
      name: 'Pap (Maize Porridge)',
      carbohydrates: 30.0,
      calories: 130.0,
      protein: 3.0,
      fat: 0.5,
      fiber: 2.0,
      glycemicIndex: GlycemicIndex.high,
      category: FoodCategory.grains,
      portion: '1 cup cooked',
      portionSize: 200.0,
      unit: 'g',
      mealType: MealType.breakfast,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    FoodEntry(
      id: 'brown_bread_1',
      name: 'Brown Bread',
      carbohydrates: 12.0,
      calories: 80.0,
      protein: 3.0,
      fat: 1.0,
      fiber: 2.5,
      glycemicIndex: GlycemicIndex.medium,
      category: FoodCategory.grains,
      portion: '1 slice',
      portionSize: 30.0,
      unit: 'g',
      mealType: MealType.breakfast,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    FoodEntry(
      id: 'oats_1',
      name: 'Oats',
      carbohydrates: 27.0,
      calories: 150.0,
      protein: 5.0,
      fat: 3.0,
      fiber: 4.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.grains,
      portion: '1/2 cup dry',
      portionSize: 40.0,
      unit: 'g',
      mealType: MealType.breakfast,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),

    // Vegetables
    FoodEntry(
      id: 'chakalaka_1',
      name: 'Chakalaka',
      carbohydrates: 8.0,
      calories: 45.0,
      protein: 2.0,
      fat: 1.0,
      fiber: 3.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.vegetables,
      portion: '1/2 cup',
      portionSize: 100.0,
      unit: 'g',
      mealType: MealType.lunch,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    FoodEntry(
      id: 'spinach_1',
      name: 'Spinach (Morogo)',
      carbohydrates: 3.0,
      calories: 20.0,
      protein: 3.0,
      fat: 0.3,
      fiber: 2.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.vegetables,
      portion: '1 cup cooked',
      portionSize: 150.0,
      unit: 'g',
      mealType: MealType.lunch,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),

    // Proteins
    FoodEntry(
      id: 'chicken_1',
      name: 'Chicken Breast',
      carbohydrates: 0.0,
      calories: 165.0,
      protein: 31.0,
      fat: 3.6,
      fiber: 0.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.proteins,
      portion: '100g cooked',
      portionSize: 100.0,
      unit: 'g',
      mealType: MealType.lunch,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    FoodEntry(
      id: 'fish_1',
      name: 'Hake (Fish)',
      carbohydrates: 0.0,
      calories: 90.0,
      protein: 20.0,
      fat: 1.0,
      fiber: 0.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.proteins,
      portion: '100g cooked',
      portionSize: 100.0,
      unit: 'g',
      mealType: MealType.dinner,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),

    // Fruits
    FoodEntry(
      id: 'apple_1',
      name: 'Apple',
      carbohydrates: 25.0,
      calories: 95.0,
      protein: 0.5,
      fat: 0.3,
      fiber: 4.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.fruits,
      portion: '1 medium',
      portionSize: 180.0,
      unit: 'g',
      mealType: MealType.afternoonSnack,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
    FoodEntry(
      id: 'orange_1',
      name: 'Orange',
      carbohydrates: 15.0,
      calories: 60.0,
      protein: 1.2,
      fat: 0.2,
      fiber: 3.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.fruits,
      portion: '1 medium',
      portionSize: 150.0,
      unit: 'g',
      mealType: MealType.morningSnack,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),

    // Beverages
    FoodEntry(
      id: 'rooibos_1',
      name: 'Rooibos Tea',
      carbohydrates: 0.0,
      calories: 2.0,
      protein: 0.0,
      fat: 0.0,
      fiber: 0.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.beverages,
      portion: '1 cup',
      portionSize: 240.0,
      unit: 'ml',
      mealType: MealType.eveningSnack,
      timestamp: DateTime.now(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    ),
  ];

  // Fallback methods for offline mode
  static List<FoodEntry> _getFallbackFoods(String query) {
    return _fallbackFoods
        .where((food) => food.name.toLowerCase().contains(query.toLowerCase()))
        .toList();
  }

  static List<FoodEntry> _getFallbackFoodsByCategory(FoodCategory category) {
    return _fallbackFoods.where((food) => food.category == category).toList();
  }

  static List<FoodEntry> _getFallbackDiabetesFriendlyFoods() {
    return _fallbackFoods
        .where(
          (food) =>
              food.glycemicIndex == GlycemicIndex.low &&
              food.carbohydrates <= 15,
        )
        .toList();
  }

  static List<FoodEntry> _getFallbackTraditionalSAFoods() {
    // Return foods that are traditionally South African
    return _fallbackFoods
        .where(
          (food) =>
              food.name.contains('Pap') ||
              food.name.contains('Biltong') ||
              food.name.contains('Boerewors') ||
              food.name.contains('Morogo'),
        )
        .toList();
  }

  /// Get all food entries for a specific date
  static Future<List<FoodEntry>> getFoodEntriesForDate(DateTime date) async {
    try {
      final headers = await _getHeaders();
      final dateString =
          '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

      final uri = Uri.parse(
        '${BackendService.baseUrl}/food-diary/entries',
      ).replace(queryParameters: {'date': dateString});

      final response = await http.get(uri, headers: headers);
      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] == true) {
        final List<dynamic> entriesList = data['data'];
        return entriesList
            .map((json) => _convertBackendEntryToFoodEntry(json))
            .toList();
      }
      return [];
    } catch (e) {
      debugPrint('Error loading food entries: $e');
      // Fallback to local storage if API fails
      return _getFoodEntriesFromLocalStorage(date);
    }
  }

  /// Convert backend food entry to FoodEntry model
  static FoodEntry _convertBackendEntryToFoodEntry(Map<String, dynamic> json) {
    return FoodEntry(
      id: json['_id'] ?? '',
      name: json['name'] ?? '',
      carbohydrates: (json['carbohydrates'] ?? 0).toDouble(),
      calories: (json['calories'] ?? 0).toDouble(),
      protein: (json['protein'] ?? 0).toDouble(),
      fat: (json['fat'] ?? 0).toDouble(),
      fiber: (json['fiber'] ?? 0).toDouble(),
      glycemicIndex: _parseGlycemicIndex(json['glycemicIndex']),
      category: _parseFoodCategory(json['category']),
      portion: json['portion'] ?? '100g',
      portionSize: (json['portionSize'] ?? 100).toDouble(),
      unit: json['unit'] ?? 'g',
      mealType: _parseMealType(json['mealType']),
      timestamp: DateTime.parse(
        json['timestamp'] ?? DateTime.now().toIso8601String(),
      ),
      notes: json['notes'],
      brand: json['brand'],
      isCustom: json['isCustom'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  /// Parse meal type from backend string
  static MealType _parseMealType(String? value) {
    switch (value?.toLowerCase()) {
      case 'breakfast':
        return MealType.breakfast;
      case 'morning_snack':
        return MealType.morningSnack;
      case 'lunch':
        return MealType.lunch;
      case 'afternoon_snack':
        return MealType.afternoonSnack;
      case 'dinner':
        return MealType.dinner;
      case 'evening_snack':
        return MealType.eveningSnack;
      case 'late_night':
        return MealType.lateNight;
      case 'custom':
        return MealType.custom;
      case 'snacks': // Legacy support
        return MealType.afternoonSnack;
      default:
        return MealType.breakfast;
    }
  }

  /// Fallback to local storage
  static Future<List<FoodEntry>> _getFoodEntriesFromLocalStorage(
    DateTime date,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = prefs.getString(_foodEntriesKey);

      if (entriesJson == null) return [];

      final List<dynamic> entriesList = json.decode(entriesJson);
      final allEntries =
          entriesList.map((json) => FoodEntry.fromJson(json)).toList();

      // Filter entries for the specific date
      final targetDate = DateTime(date.year, date.month, date.day);
      return allEntries.where((entry) {
        final entryDate = DateTime(
          entry.timestamp.year,
          entry.timestamp.month,
          entry.timestamp.day,
        );
        return entryDate.isAtSameMomentAs(targetDate);
      }).toList();
    } catch (e) {
      debugPrint('Error loading food entries from local storage: $e');
      return [];
    }
  }

  /// Save a food entry
  static Future<void> saveFoodEntry(FoodEntry entry) async {
    try {
      final headers = await _getHeaders();

      // Convert FoodEntry to backend format
      final entryData = {
        'name': entry.name,
        'carbohydrates': entry.carbohydrates,
        'calories': entry.calories,
        'protein': entry.protein,
        'fat': entry.fat,
        'fiber': entry.fiber,
        'glycemicIndex': entry.glycemicIndex.name,
        'category': _convertFoodCategoryToBackend(entry.category),
        'portion': entry.portion,
        'portionSize': entry.portionSize,
        'unit': entry.unit,
        'mealType': entry.mealType.name,
        'timestamp': entry.timestamp.toIso8601String(),
        'notes': entry.notes,
        'brand': entry.brand,
        'isCustom': entry.isCustom,
      };

      http.Response response;

      if (entry.id.isNotEmpty && !entry.id.startsWith('food_')) {
        // Update existing entry
        response = await http.put(
          Uri.parse('${BackendService.baseUrl}/food-diary/entries/${entry.id}'),
          headers: headers,
          body: jsonEncode(entryData),
        );
      } else {
        // Create new entry
        response = await http.post(
          Uri.parse('${BackendService.baseUrl}/food-diary/entries'),
          headers: headers,
          body: jsonEncode(entryData),
        );
      }

      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] != true) {
        throw Exception(data['message'] ?? 'Failed to save food entry');
      }
    } catch (e) {
      debugPrint('Error saving food entry: $e');
      // Fallback to local storage
      await _saveFoodEntryToLocalStorage(entry);
    }
  }

  /// Convert FoodCategory to backend format
  static String _convertFoodCategoryToBackend(FoodCategory category) {
    switch (category) {
      case FoodCategory.grains:
        return 'grains_starches';
      case FoodCategory.vegetables:
        return 'vegetables';
      case FoodCategory.fruits:
        return 'fruits';
      case FoodCategory.proteins:
        return 'proteins';
      case FoodCategory.dairy:
        return 'dairy';
      case FoodCategory.fats:
        return 'fats_oils';
      case FoodCategory.beverages:
        return 'beverages';
      case FoodCategory.sweets:
        return 'snacks_sweets';
      case FoodCategory.other:
        return 'other';
    }
  }

  /// Fallback to save to local storage
  static Future<void> _saveFoodEntryToLocalStorage(FoodEntry entry) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = prefs.getString(_foodEntriesKey);

      List<FoodEntry> allEntries = [];
      if (entriesJson != null) {
        final List<dynamic> entriesList = json.decode(entriesJson);
        allEntries =
            entriesList.map((json) => FoodEntry.fromJson(json)).toList();
      }

      // Add or update entry
      final existingIndex = allEntries.indexWhere((e) => e.id == entry.id);
      if (existingIndex >= 0) {
        allEntries[existingIndex] = entry.copyWith(updatedAt: DateTime.now());
      } else {
        allEntries.add(entry);
      }

      // Save back to storage
      final updatedJson = json.encode(
        allEntries.map((entry) => entry.toJson()).toList(),
      );
      await prefs.setString(_foodEntriesKey, updatedJson);
    } catch (e) {
      debugPrint('Error saving food entry to local storage: $e');
      rethrow;
    }
  }

  /// Delete a food entry
  static Future<void> deleteFoodEntry(String entryId) async {
    try {
      final headers = await _getHeaders();

      final response = await http.delete(
        Uri.parse('${BackendService.baseUrl}/food-diary/entries/$entryId'),
        headers: headers,
      );

      _handleApiError(response);

      final data = jsonDecode(response.body);
      if (data['success'] != true) {
        throw Exception(data['message'] ?? 'Failed to delete food entry');
      }
    } catch (e) {
      debugPrint('Error deleting food entry: $e');
      // Fallback to local storage
      await _deleteFoodEntryFromLocalStorage(entryId);
    }
  }

  /// Fallback to delete from local storage
  static Future<void> _deleteFoodEntryFromLocalStorage(String entryId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final entriesJson = prefs.getString(_foodEntriesKey);

      if (entriesJson == null) return;

      final List<dynamic> entriesList = json.decode(entriesJson);
      final allEntries =
          entriesList.map((json) => FoodEntry.fromJson(json)).toList();

      // Remove entry
      allEntries.removeWhere((entry) => entry.id == entryId);

      // Save back to storage
      final updatedJson = json.encode(
        allEntries.map((entry) => entry.toJson()).toList(),
      );
      await prefs.setString(_foodEntriesKey, updatedJson);
    } catch (e) {
      debugPrint('Error deleting food entry from local storage: $e');
      rethrow;
    }
  }

  /// Get daily nutrition summary for a specific date
  static Future<DailyNutrition> getDailyNutrition(DateTime date) async {
    final foodEntries = await getFoodEntriesForDate(date);
    final goals = await getNutritionGoals();
    final bloodSugarReadings = await getBloodSugarReadingsForDate(date);

    // Calculate average blood sugar change
    double? averageChange;
    if (bloodSugarReadings.length >= 2) {
      final beforeMealReadings =
          bloodSugarReadings.where((r) => r.isBeforeMeal).toList();
      final afterMealReadings =
          bloodSugarReadings.where((r) => !r.isBeforeMeal).toList();

      if (beforeMealReadings.isNotEmpty && afterMealReadings.isNotEmpty) {
        final avgBefore =
            beforeMealReadings.map((r) => r.value).reduce((a, b) => a + b) /
            beforeMealReadings.length;
        final avgAfter =
            afterMealReadings.map((r) => r.value).reduce((a, b) => a + b) /
            afterMealReadings.length;
        averageChange = avgAfter - avgBefore;
      }
    }

    return DailyNutrition(
      date: date,
      foodEntries: foodEntries,
      goals: goals,
      averageBloodSugarChange: averageChange,
      bloodSugarReadings: bloodSugarReadings,
    );
  }

  /// Get nutrition goals
  static Future<NutritionGoals> getNutritionGoals() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final goalsJson = prefs.getString(_nutritionGoalsKey);

      if (goalsJson == null) {
        // Return default goals for diabetic patients
        return NutritionGoals.diabeticDefault();
      }

      return NutritionGoals.fromJson(json.decode(goalsJson));
    } catch (e) {
      debugPrint('Error loading nutrition goals: $e');
      return NutritionGoals.diabeticDefault();
    }
  }

  /// Save nutrition goals
  static Future<void> saveNutritionGoals(NutritionGoals goals) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_nutritionGoalsKey, json.encode(goals.toJson()));
    } catch (e) {
      debugPrint('Error saving nutrition goals: $e');
      rethrow;
    }
  }

  /// Get food suggestions (overloaded method without parameters)
  static Future<List<FoodEntry>> getFoodSuggestions([
    MealType? mealType,
  ]) async {
    return getFoodSuggestionsForMeal(mealType ?? MealType.breakfast);
  }

  /// Get food suggestions based on meal type and diabetes-friendly criteria
  static Future<List<FoodEntry>> getFoodSuggestionsForMeal(
    MealType mealType,
  ) async {
    try {
      // Get diabetes-friendly foods first
      final diabetesFriendlyFoods = await getDiabetesFriendlyFoods(limit: 20);

      // Filter by meal type preferences
      final suggestions =
          diabetesFriendlyFoods.where((food) {
            // Suggest appropriate foods for each meal type
            switch (mealType) {
              case MealType.breakfast:
                return food.category == FoodCategory.grains ||
                    food.category == FoodCategory.fruits ||
                    food.category == FoodCategory.dairy;
              case MealType.lunch:
              case MealType.dinner:
                return food.category == FoodCategory.proteins ||
                    food.category == FoodCategory.vegetables ||
                    food.category == FoodCategory.grains;
              case MealType.morningSnack:
              case MealType.afternoonSnack:
              case MealType.eveningSnack:
                return food.category == FoodCategory.fruits ||
                    food.category == FoodCategory.vegetables ||
                    food.carbohydrates <= 15;
              case MealType.lateNight:
                return food.category == FoodCategory.vegetables ||
                    (food.category == FoodCategory.proteins &&
                        food.carbohydrates <= 5);
              case MealType.custom:
                return true; // Allow all foods for custom meal types
            }
          }).toList();

      // If we have suggestions, return them, otherwise return fallback
      if (suggestions.isNotEmpty) {
        return suggestions;
      } else {
        return _getFallbackSuggestions(mealType);
      }
    } catch (e) {
      debugPrint('Error getting food suggestions: $e');
      return _getFallbackSuggestions(mealType);
    }
  }

  /// Fallback suggestions for offline mode
  static List<FoodEntry> _getFallbackSuggestions(MealType mealType) {
    return _fallbackFoods.where((food) {
      final isDiabetesFriendly =
          food.glycemicIndex == GlycemicIndex.low && food.carbohydrates <= 15;

      switch (mealType) {
        case MealType.breakfast:
          return (food.category == FoodCategory.grains ||
                  food.category == FoodCategory.fruits ||
                  food.category == FoodCategory.dairy) &&
              isDiabetesFriendly;
        case MealType.lunch:
        case MealType.dinner:
          return (food.category == FoodCategory.proteins ||
                  food.category == FoodCategory.vegetables ||
                  food.category == FoodCategory.grains) &&
              isDiabetesFriendly;
        case MealType.morningSnack:
        case MealType.afternoonSnack:
        case MealType.eveningSnack:
          return (food.category == FoodCategory.fruits ||
                  food.category == FoodCategory.vegetables ||
                  food.carbohydrates <= 15) &&
              isDiabetesFriendly;
        case MealType.lateNight:
          return (food.category == FoodCategory.vegetables ||
                  (food.category == FoodCategory.proteins &&
                      food.carbohydrates <= 5)) &&
              isDiabetesFriendly;
        case MealType.custom:
          return isDiabetesFriendly; // Allow all diabetes-friendly foods for custom meal types
      }
    }).toList();
  }

  /// Test connectivity to food diary API
  static Future<bool> testApiConnectivity() async {
    try {
      final headers = await _getHeaders();
      final response = await http
          .get(
            Uri.parse(
              '${BackendService.baseUrl}/food-diary/foods/diabetes-friendly?limit=1',
            ),
            headers: headers,
          )
          .timeout(const Duration(seconds: 10));

      return response.statusCode == 200;
    } catch (e) {
      debugPrint('Food diary API connectivity test failed: $e');
      return false;
    }
  }

  /// Get API status and information
  static Future<Map<String, dynamic>> getApiStatus() async {
    try {
      final headers = await _getHeaders();
      final response = await http
          .get(
            Uri.parse(
              '${BackendService.baseUrl}/food-diary/foods/diabetes-friendly?limit=1',
            ),
            headers: headers,
          )
          .timeout(const Duration(seconds: 10));

      final isConnected = response.statusCode == 200;
      final data = isConnected ? jsonDecode(response.body) : null;

      return {
        'connected': isConnected,
        'statusCode': response.statusCode,
        'hasData': data?['data']?.isNotEmpty ?? false,
        'dataCount': data?['count'] ?? 0,
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'connected': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Generate a unique ID for food entries
  static String generateId() {
    return 'food_${DateTime.now().millisecondsSinceEpoch}_${Random().nextInt(1000)}';
  }

  /// Get blood sugar readings for a specific date
  static Future<List<BloodSugarReading>> getBloodSugarReadingsForDate(
    DateTime date,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readingsJson = prefs.getString(_bloodSugarReadingsKey);

      if (readingsJson == null) return [];

      final List<dynamic> readingsList = json.decode(readingsJson);
      final allReadings =
          readingsList.map((json) => BloodSugarReading.fromJson(json)).toList();

      // Filter readings for the specific date
      final targetDate = DateTime(date.year, date.month, date.day);
      return allReadings.where((reading) {
        final readingDate = DateTime(
          reading.timestamp.year,
          reading.timestamp.month,
          reading.timestamp.day,
        );
        return readingDate.isAtSameMomentAs(targetDate);
      }).toList();
    } catch (e) {
      debugPrint('Error loading blood sugar readings: $e');
      return [];
    }
  }

  /// Save blood sugar reading
  static Future<void> saveBloodSugarReading(BloodSugarReading reading) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final readingsJson = prefs.getString(_bloodSugarReadingsKey);

      List<BloodSugarReading> allReadings = [];
      if (readingsJson != null) {
        final List<dynamic> readingsList = json.decode(readingsJson);
        allReadings =
            readingsList
                .map((json) => BloodSugarReading.fromJson(json))
                .toList();
      }

      // Add or update reading
      final existingIndex = allReadings.indexWhere((r) => r.id == reading.id);
      if (existingIndex >= 0) {
        allReadings[existingIndex] = reading;
      } else {
        allReadings.add(reading);
      }

      // Save back to storage
      final updatedJson = json.encode(
        allReadings.map((reading) => reading.toJson()).toList(),
      );
      await prefs.setString(_bloodSugarReadingsKey, updatedJson);
    } catch (e) {
      debugPrint('Error saving blood sugar reading: $e');
      rethrow;
    }
  }

  /// Get weekly nutrition summary
  static Future<Map<DateTime, DailyNutrition>> getWeeklyNutrition(
    DateTime startDate,
  ) async {
    final Map<DateTime, DailyNutrition> weeklyData = {};

    for (int i = 0; i < 7; i++) {
      final date = startDate.add(Duration(days: i));
      final dailyNutrition = await getDailyNutrition(date);
      weeklyData[date] = dailyNutrition;
    }

    return weeklyData;
  }

  /// Add sample data for testing (development only)
  static Future<void> addSampleData() async {
    final today = DateTime.now();

    // Sample breakfast
    final breakfast1 = FoodEntry(
      id: generateId(),
      name: 'Oatmeal with Berries',
      carbohydrates: 30.0,
      calories: 150.0,
      protein: 5.0,
      fat: 3.0,
      fiber: 4.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.grains,
      portion: '1 cup',
      portionSize: 200.0,
      unit: 'g',
      mealType: MealType.breakfast,
      timestamp: DateTime(today.year, today.month, today.day, 8, 0),
      notes: 'Added blueberries and cinnamon',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Sample lunch
    final lunch1 = FoodEntry(
      id: generateId(),
      name: 'Grilled Chicken Salad',
      carbohydrates: 15.0,
      calories: 250.0,
      protein: 30.0,
      fat: 8.0,
      fiber: 5.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.proteins,
      portion: '1 large bowl',
      portionSize: 300.0,
      unit: 'g',
      mealType: MealType.lunch,
      timestamp: DateTime(today.year, today.month, today.day, 12, 30),
      notes: 'Mixed greens with olive oil dressing',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Sample snack
    final snack1 = FoodEntry(
      id: generateId(),
      name: 'Apple with Almonds',
      carbohydrates: 20.0,
      calories: 180.0,
      protein: 4.0,
      fat: 8.0,
      fiber: 4.0,
      glycemicIndex: GlycemicIndex.low,
      category: FoodCategory.fruits,
      portion: '1 medium apple + 10 almonds',
      portionSize: 200.0,
      unit: 'g',
      mealType: MealType.afternoonSnack,
      timestamp: DateTime(today.year, today.month, today.day, 15, 0),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // Save sample entries
    await saveFoodEntry(breakfast1);
    await saveFoodEntry(lunch1);
    await saveFoodEntry(snack1);

    // Add sample blood sugar readings
    final beforeBreakfast = BloodSugarReading(
      id: generateId(),
      value: 95.0,
      timestamp: DateTime(today.year, today.month, today.day, 7, 45),
      isBeforeMeal: true,
      notes: 'Fasting glucose',
    );

    final afterBreakfast = BloodSugarReading(
      id: generateId(),
      value: 125.0,
      timestamp: DateTime(today.year, today.month, today.day, 9, 30),
      isBeforeMeal: false,
      notes: '2 hours after breakfast',
    );

    await saveBloodSugarReading(beforeBreakfast);
    await saveBloodSugarReading(afterBreakfast);

    debugPrint('Sample food diary data added successfully');
  }

  /// Search food database by query
  static Future<List<FoodEntry>> searchFoodDatabase(String query) async {
    try {
      // First try to search from backend
      final headers = await _getHeaders();
      final response = await http
          .get(
            Uri.parse(
              '${BackendService.baseUrl}/food-diary/search?q=${Uri.encodeComponent(query)}&limit=20',
            ),
            headers: headers,
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final List<dynamic> foodsJson = data['foods'] ?? [];
        return foodsJson
            .map((json) => _convertBackendFoodToFoodEntry(json))
            .toList();
      }
    } catch (e) {
      debugPrint('Error searching food database: $e');
    }

    // Fallback to local search
    return _searchLocalFoods(query);
  }

  /// Search local fallback foods
  static List<FoodEntry> _searchLocalFoods(String query) {
    // Use the existing _getFallbackFoods method which already filters by query
    final directMatches = _getFallbackFoods(query);

    // Also search by category
    final lowerQuery = query.toLowerCase();
    final categoryMatches =
        _fallbackFoods.where((food) {
          return food.category.displayName.toLowerCase().contains(lowerQuery);
        }).toList();

    // Combine and deduplicate results
    final allMatches = <String, FoodEntry>{};
    for (final food in [...directMatches, ...categoryMatches]) {
      allMatches[food.id] = food;
    }

    return allMatches.values.toList();
  }
}
