import 'package:flutter/foundation.dart';
import '../models/food_entry.dart';
import '../services/custom_meal_time_service.dart';

class CustomMealTimeProvider with ChangeNotifier {
  List<CustomMealTime> _customMealTimes = [];
  bool _isLoading = false;
  String? _error;

  // Getters
  List<CustomMealTime> get customMealTimes => _customMealTimes;
  bool get isLoading => _isLoading;
  String? get error => _error;

  /// Get active custom meal times only
  List<CustomMealTime> get activeMealTimes =>
      _customMealTimes.where((mealTime) => mealTime.isActive).toList();

  /// Load custom meal times from the server
  Future<void> loadCustomMealTimes() async {
    try {
      _setLoading(true);
      _clearError();

      _customMealTimes = await CustomMealTimeService.getCustomMealTimes();
      
      debugPrint('Loaded ${_customMealTimes.length} custom meal times');
    } catch (e) {
      _setError('Failed to load custom meal times: $e');
      debugPrint('Error loading custom meal times: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Create a new custom meal time
  Future<void> createCustomMealTime({
    required String name,
    required String displayName,
    required String icon,
    required String color,
    required String defaultTime,
    required MealTimeRange timeRange,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final newMealTime = await CustomMealTimeService.createCustomMealTime(
        name: name,
        displayName: displayName,
        icon: icon,
        color: color,
        defaultTime: defaultTime,
        timeRange: timeRange,
      );

      _customMealTimes.add(newMealTime);
      notifyListeners();

      debugPrint('Created custom meal time: ${newMealTime.displayName}');
    } catch (e) {
      _setError('Failed to create custom meal time: $e');
      debugPrint('Error creating custom meal time: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update an existing custom meal time
  Future<void> updateCustomMealTime({
    required String id,
    String? name,
    String? displayName,
    String? icon,
    String? color,
    String? defaultTime,
    MealTimeRange? timeRange,
    bool? isActive,
  }) async {
    try {
      _setLoading(true);
      _clearError();

      final updatedMealTime = await CustomMealTimeService.updateCustomMealTime(
        id: id,
        name: name,
        displayName: displayName,
        icon: icon,
        color: color,
        defaultTime: defaultTime,
        timeRange: timeRange,
        isActive: isActive,
      );

      final index = _customMealTimes.indexWhere((mealTime) => mealTime.id == id);
      if (index != -1) {
        _customMealTimes[index] = updatedMealTime;
        notifyListeners();
      }

      debugPrint('Updated custom meal time: ${updatedMealTime.displayName}');
    } catch (e) {
      _setError('Failed to update custom meal time: $e');
      debugPrint('Error updating custom meal time: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a custom meal time
  Future<void> deleteCustomMealTime(String id) async {
    try {
      _setLoading(true);
      _clearError();

      await CustomMealTimeService.deleteCustomMealTime(id);

      _customMealTimes.removeWhere((mealTime) => mealTime.id == id);
      notifyListeners();

      debugPrint('Deleted custom meal time with id: $id');
    } catch (e) {
      _setError('Failed to delete custom meal time: $e');
      debugPrint('Error deleting custom meal time: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Initialize default meal times for the user
  Future<void> initializeDefaultMealTimes() async {
    try {
      _setLoading(true);
      _clearError();

      final defaultMealTimes = await CustomMealTimeService.initializeDefaultMealTimes();
      _customMealTimes = defaultMealTimes;
      notifyListeners();

      debugPrint('Initialized ${defaultMealTimes.length} default meal times');
    } catch (e) {
      _setError('Failed to initialize default meal times: $e');
      debugPrint('Error initializing default meal times: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Find custom meal time by name
  CustomMealTime? findMealTimeByName(String name) {
    try {
      return _customMealTimes.firstWhere(
        (mealTime) => mealTime.name.toLowerCase() == name.toLowerCase(),
      );
    } catch (e) {
      return null;
    }
  }

  /// Find custom meal time by ID
  CustomMealTime? findMealTimeById(String id) {
    try {
      return _customMealTimes.firstWhere((mealTime) => mealTime.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Get meal time suggestions based on current time
  MealType suggestMealTypeForCurrentTime() {
    return CustomMealTimeService.suggestMealTypeForCurrentTime();
  }

  /// Check if a meal time name is available
  bool isMealTimeNameAvailable(String name, {String? excludeId}) {
    return !_customMealTimes.any((mealTime) =>
        mealTime.name.toLowerCase() == name.toLowerCase() &&
        mealTime.id != excludeId);
  }

  /// Get meal times that overlap with a given time range
  List<CustomMealTime> getOverlappingMealTimes(MealTimeRange timeRange) {
    return _customMealTimes.where((mealTime) {
      // Check if time ranges overlap
      final startMinutes = CustomMealTimeService.timeToMinutes(timeRange.start);
      final endMinutes = CustomMealTimeService.timeToMinutes(timeRange.end);
      final mealStartMinutes = CustomMealTimeService.timeToMinutes(mealTime.timeRange.start);
      final mealEndMinutes = CustomMealTimeService.timeToMinutes(mealTime.timeRange.end);

      // Handle overnight ranges
      if (endMinutes < startMinutes || mealEndMinutes < mealStartMinutes) {
        // Complex overlap logic for overnight ranges
        return true; // Simplified for now
      }

      return (startMinutes < mealEndMinutes && endMinutes > mealStartMinutes);
    }).toList();
  }

  /// Refresh data
  Future<void> refresh() async {
    await loadCustomMealTimes();
  }

  /// Clear all data
  void clear() {
    _customMealTimes.clear();
    _error = null;
    _isLoading = false;
    notifyListeners();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }
}
