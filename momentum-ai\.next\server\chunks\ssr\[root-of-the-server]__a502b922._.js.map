{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Sentebale/momentum-ai/src/components/Providers.tsx"], "sourcesContent": ["'use client'\n\nimport { SessionProvider } from 'next-auth/react'\n\nexport function Providers({ children }: { children: React.ReactNode }) {\n  return (\n    <SessionProvider>\n      {children}\n    </SessionProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIO,SAAS,UAAU,EAAE,QAAQ,EAAiC;IACnE,qBACE,8OAAC,8IAAA,CAAA,kBAAe;kBACb;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Sentebale/momentum-ai/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx'\nimport { twMerge } from 'tailwind-merge'\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Sentebale/momentum-ai/src/components/ui/Button.tsx"], "sourcesContent": ["import { ButtonHTMLAttributes, forwardRef } from 'react'\nimport { cn } from '@/lib/utils'\n\nexport interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link'\n  size?: 'default' | 'sm' | 'lg' | 'icon'\n}\n\nconst Button = forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant = 'default', size = 'default', ...props }, ref) => {\n    return (\n      <button\n        className={cn(\n          'inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50',\n          {\n            'bg-primary text-primary-foreground hover:bg-primary/90': variant === 'default',\n            'bg-destructive text-destructive-foreground hover:bg-destructive/90': variant === 'destructive',\n            'border border-input bg-background hover:bg-accent hover:text-accent-foreground': variant === 'outline',\n            'bg-secondary text-secondary-foreground hover:bg-secondary/80': variant === 'secondary',\n            'hover:bg-accent hover:text-accent-foreground': variant === 'ghost',\n            'text-primary underline-offset-4 hover:underline': variant === 'link',\n          },\n          {\n            'h-10 px-4 py-2': size === 'default',\n            'h-9 rounded-md px-3': size === 'sm',\n            'h-11 rounded-md px-8': size === 'lg',\n            'h-10 w-10': size === 'icon',\n          },\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = 'Button'\n\nexport { Button }\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EACtB,CAAC,EAAE,SAAS,EAAE,UAAU,SAAS,EAAE,OAAO,SAAS,EAAE,GAAG,OAAO,EAAE;IAC/D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0RACA;YACE,0DAA0D,YAAY;YACtE,sEAAsE,YAAY;YAClF,kFAAkF,YAAY;YAC9F,gEAAgE,YAAY;YAC5E,gDAAgD,YAAY;YAC5D,mDAAmD,YAAY;QACjE,GACA;YACE,kBAAkB,SAAS;YAC3B,uBAAuB,SAAS;YAChC,wBAAwB,SAAS;YACjC,aAAa,SAAS;QACxB,GACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Sentebale/momentum-ai/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signIn, signOut } from 'next-auth/react'\nimport Link from 'next/link'\nimport { Button } from '@/components/ui/Button'\n\nexport function Header() {\n  const { data: session, status } = useSession()\n\n  return (\n    <header className=\"bg-white shadow-sm border-b\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <div className=\"flex items-center\">\n            <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n              Momentum AI\n            </Link>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"hidden md:flex space-x-8\">\n            {session && (\n              <>\n                <Link\n                  href=\"/dashboard\"\n                  className=\"text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Dashboard\n                </Link>\n                <Link\n                  href=\"/habits\"\n                  className=\"text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Habits\n                </Link>\n                <Link\n                  href=\"/analytics\"\n                  className=\"text-gray-500 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Analytics\n                </Link>\n              </>\n            )}\n          </nav>\n\n          {/* Auth */}\n          <div className=\"flex items-center space-x-4\">\n            {status === 'loading' ? (\n              <div className=\"animate-pulse bg-gray-200 h-8 w-20 rounded\"></div>\n            ) : session ? (\n              <div className=\"flex items-center space-x-4\">\n                <span className=\"text-sm text-gray-700\">\n                  {session.user?.name}\n                </span>\n                <Button\n                  onClick={() => signOut()}\n                  variant=\"outline\"\n                  size=\"sm\"\n                >\n                  Sign Out\n                </Button>\n              </div>\n            ) : (\n              <Button\n                onClick={() => signIn('google')}\n                size=\"sm\"\n              >\n                Sign In\n              </Button>\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMO,SAAS;IACd,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAE3C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAkC;;;;;;;;;;;kCAM7D,8OAAC;wBAAI,WAAU;kCACZ,yBACC;;8CACE,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACZ,WAAW,0BACV,8OAAC;4BAAI,WAAU;;;;;mCACb,wBACF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CACb,QAAQ,IAAI,EAAE;;;;;;8CAEjB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD;oCACrB,SAAQ;oCACR,MAAK;8CACN;;;;;;;;;;;iDAKH,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;4BACtB,MAAK;sCACN;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}