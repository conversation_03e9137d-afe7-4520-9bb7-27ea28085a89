import 'package:flutter/foundation.dart';
import 'food_entry.dart';

enum RecipeDifficulty {
  easy,
  medium,
  hard;

  String get displayName {
    switch (this) {
      case RecipeDifficulty.easy:
        return 'Easy';
      case RecipeDifficulty.medium:
        return 'Medium';
      case RecipeDifficulty.hard:
        return 'Hard';
    }
  }
}

@immutable
class RecipeIngredient {
  final String name;
  final double amount;
  final String unit;
  final double calories;
  final double carbohydrates;
  final double protein;
  final double fat;
  final double fiber;
  final String? notes;

  const RecipeIngredient({
    required this.name,
    required this.amount,
    required this.unit,
    required this.calories,
    required this.carbohydrates,
    required this.protein,
    required this.fat,
    required this.fiber,
    this.notes,
  });

  factory RecipeIngredient.fromJson(Map<String, dynamic> json) {
    return RecipeIngredient(
      name: json['name'] ?? '',
      amount: (json['amount'] ?? 0).toDouble(),
      unit: json['unit'] ?? 'g',
      calories: (json['calories'] ?? 0).toDouble(),
      carbohydrates: (json['carbohydrates'] ?? 0).toDouble(),
      protein: (json['protein'] ?? 0).toDouble(),
      fat: (json['fat'] ?? 0).toDouble(),
      fiber: (json['fiber'] ?? 0).toDouble(),
      notes: json['notes'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'amount': amount,
      'unit': unit,
      'calories': calories,
      'carbohydrates': carbohydrates,
      'protein': protein,
      'fat': fat,
      'fiber': fiber,
      'notes': notes,
    };
  }

  RecipeIngredient copyWith({
    String? name,
    double? amount,
    String? unit,
    double? calories,
    double? carbohydrates,
    double? protein,
    double? fat,
    double? fiber,
    String? notes,
  }) {
    return RecipeIngredient(
      name: name ?? this.name,
      amount: amount ?? this.amount,
      unit: unit ?? this.unit,
      calories: calories ?? this.calories,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      protein: protein ?? this.protein,
      fat: fat ?? this.fat,
      fiber: fiber ?? this.fiber,
      notes: notes ?? this.notes,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RecipeIngredient &&
        other.name == name &&
        other.amount == amount &&
        other.unit == unit;
  }

  @override
  int get hashCode => Object.hash(name, amount, unit);
}

@immutable
class Recipe {
  final String id;
  final String name;
  final String? description;
  final List<RecipeIngredient> ingredients;
  final List<String> instructions;
  final FoodCategory category;
  final int servings;
  final int prepTime; // in minutes
  final int cookTime; // in minutes
  final RecipeDifficulty difficulty;
  final List<String> tags;
  final GlycemicIndex estimatedGlycemicIndex;
  final double totalWeight; // in grams
  final String? imageUrl;
  final bool isPublic;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Recipe({
    required this.id,
    required this.name,
    this.description,
    required this.ingredients,
    required this.instructions,
    required this.category,
    required this.servings,
    required this.prepTime,
    required this.cookTime,
    required this.difficulty,
    required this.tags,
    required this.estimatedGlycemicIndex,
    required this.totalWeight,
    this.imageUrl,
    this.isPublic = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Recipe.fromJson(Map<String, dynamic> json) {
    return Recipe(
      id: json['id'] ?? json['_id'] ?? '',
      name: json['name'] ?? '',
      description: json['description'],
      ingredients:
          (json['ingredients'] as List<dynamic>?)
              ?.map((i) => RecipeIngredient.fromJson(i))
              .toList() ??
          [],
      instructions: List<String>.from(json['instructions'] ?? []),
      category: FoodCategory.values.firstWhere(
        (c) => c.name == json['category'],
        orElse: () => FoodCategory.sweets,
      ),
      servings: json['servings'] ?? 1,
      prepTime: json['prepTime'] ?? 0,
      cookTime: json['cookTime'] ?? 0,
      difficulty: RecipeDifficulty.values.firstWhere(
        (d) => d.name == json['difficulty'],
        orElse: () => RecipeDifficulty.easy,
      ),
      tags: List<String>.from(json['tags'] ?? []),
      estimatedGlycemicIndex: GlycemicIndex.values.firstWhere(
        (g) => g.name == json['estimatedGlycemicIndex'],
        orElse: () => GlycemicIndex.medium,
      ),
      totalWeight: (json['totalWeight'] ?? 0).toDouble(),
      imageUrl: json['imageUrl'],
      isPublic: json['isPublic'] ?? false,
      createdAt: DateTime.parse(
        json['createdAt'] ?? DateTime.now().toIso8601String(),
      ),
      updatedAt: DateTime.parse(
        json['updatedAt'] ?? DateTime.now().toIso8601String(),
      ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'ingredients': ingredients.map((i) => i.toJson()).toList(),
      'instructions': instructions,
      'category': category.name,
      'servings': servings,
      'prepTime': prepTime,
      'cookTime': cookTime,
      'difficulty': difficulty.name,
      'tags': tags,
      'estimatedGlycemicIndex': estimatedGlycemicIndex.name,
      'totalWeight': totalWeight,
      'imageUrl': imageUrl,
      'isPublic': isPublic,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  Recipe copyWith({
    String? id,
    String? name,
    String? description,
    List<RecipeIngredient>? ingredients,
    List<String>? instructions,
    FoodCategory? category,
    int? servings,
    int? prepTime,
    int? cookTime,
    RecipeDifficulty? difficulty,
    List<String>? tags,
    GlycemicIndex? estimatedGlycemicIndex,
    double? totalWeight,
    String? imageUrl,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Recipe(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      ingredients: ingredients ?? this.ingredients,
      instructions: instructions ?? this.instructions,
      category: category ?? this.category,
      servings: servings ?? this.servings,
      prepTime: prepTime ?? this.prepTime,
      cookTime: cookTime ?? this.cookTime,
      difficulty: difficulty ?? this.difficulty,
      tags: tags ?? this.tags,
      estimatedGlycemicIndex:
          estimatedGlycemicIndex ?? this.estimatedGlycemicIndex,
      totalWeight: totalWeight ?? this.totalWeight,
      imageUrl: imageUrl ?? this.imageUrl,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Calculated properties
  int get totalTime => prepTime + cookTime;

  double get totalCalories {
    return ingredients.fold(0.0, (sum, ingredient) {
      final multiplier = ingredient.amount / 100;
      return sum + (ingredient.calories * multiplier);
    });
  }

  double get totalCarbohydrates {
    return ingredients.fold(0.0, (sum, ingredient) {
      final multiplier = ingredient.amount / 100;
      return sum + (ingredient.carbohydrates * multiplier);
    });
  }

  double get totalProtein {
    return ingredients.fold(0.0, (sum, ingredient) {
      final multiplier = ingredient.amount / 100;
      return sum + (ingredient.protein * multiplier);
    });
  }

  double get totalFat {
    return ingredients.fold(0.0, (sum, ingredient) {
      final multiplier = ingredient.amount / 100;
      return sum + (ingredient.fat * multiplier);
    });
  }

  double get caloriesPerServing => totalCalories / servings;
  double get carbsPerServing => totalCarbohydrates / servings;
  double get proteinPerServing => totalProtein / servings;
  double get fatPerServing => totalFat / servings;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Recipe && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
