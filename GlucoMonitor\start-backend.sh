#!/bin/bash

# GlucoMonitor Backend Startup Script
# This script helps start the backend server for development

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -d "backend" ]; then
    print_error "Backend directory not found. Please run this script from the project root."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js and try again."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed. Please install npm and try again."
    exit 1
fi

print_status "Starting GlucoMonitor Backend Server..."

# Navigate to backend directory
cd backend

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found. Installing dependencies..."
    npm install
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_warning ".env file not found."
    if [ -f ".env.example" ]; then
        print_status "Creating .env from .env.example..."
        cp .env.example .env
        print_warning "Please edit .env file with your configuration before running the server."
        exit 1
    else
        print_error ".env.example not found. Please create environment configuration."
        exit 1
    fi
fi

# Check if TypeScript is compiled
if [ ! -d "dist" ]; then
    print_status "Compiling TypeScript..."
    npm run build
fi

# Start the server
print_status "Starting backend server in development mode..."
print_status "Server will be available at: http://localhost:5000"
print_status "Health check endpoint: http://localhost:5000/api/health"
print_status "Press Ctrl+C to stop the server"
print_success "Backend server starting..."

# Start the development server
npm run dev
