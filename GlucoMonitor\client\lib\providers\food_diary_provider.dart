import 'package:flutter/foundation.dart';
import '../models/food_entry.dart';
import '../models/daily_nutrition.dart';
import '../services/food_diary_service.dart';

class FoodDiaryProvider extends ChangeNotifier {
  DateTime _selectedDate = DateTime.now();
  DailyNutrition? _dailyNutrition;
  bool _isLoading = false;
  String? _error;
  final Map<MealType, bool> _expandedMeals = {
    MealType.breakfast: true,
    MealType.morningSnack: false,
    MealType.lunch: true,
    MealType.afternoonSnack: false,
    MealType.dinner: true,
    MealType.eveningSnack: false,
    MealType.lateNight: false,
    MealType.custom: false,
  };

  // Getters
  DateTime get selectedDate => _selectedDate;
  DailyNutrition? get dailyNutrition => _dailyNutrition;
  bool get isLoading => _isLoading;
  String? get error => _error;
  Map<MealType, bool> get expandedMeals => _expandedMeals;

  // Get food entries for current selected date
  List<FoodEntry> get todaysFoodEntries => _dailyNutrition?.foodEntries ?? [];

  // Get entries grouped by meal type
  Map<MealType, List<FoodEntry>> get entriesByMeal =>
      _dailyNutrition?.entriesByMeal ?? {};

  // Get nutrition goals
  NutritionGoals get nutritionGoals =>
      _dailyNutrition?.goals ?? NutritionGoals.diabeticDefault();

  // Get daily totals
  double get totalCarbs => _dailyNutrition?.totalCarbohydrates ?? 0;
  double get totalCalories => _dailyNutrition?.totalCalories ?? 0;
  double get totalProtein => _dailyNutrition?.totalProtein ?? 0;
  double get totalFat => _dailyNutrition?.totalFat ?? 0;
  double get totalFiber => _dailyNutrition?.totalFiber ?? 0;

  // Get progress percentages
  double get carbProgress => _dailyNutrition?.carbProgress ?? 0;
  double get calorieProgress => _dailyNutrition?.calorieProgress ?? 0;
  double get proteinProgress => _dailyNutrition?.proteinProgress ?? 0;

  // Get blood sugar change
  double? get averageBloodSugarChange =>
      _dailyNutrition?.averageBloodSugarChange;

  // Get daily insights
  DailyInsights get dailyInsights {
    if (_dailyNutrition == null) {
      return DailyInsights(
        date: _selectedDate,
        recommendations: [],
        achievements: [],
        warnings: [],
        overallScore: 50,
      );
    }
    return DailyInsights.fromDailyNutrition(_dailyNutrition!);
  }

  /// Initialize the provider
  Future<void> initialize() async {
    await loadDailyNutrition();
  }

  /// Load daily nutrition data for selected date
  Future<void> loadDailyNutrition() async {
    try {
      _setLoading(true);
      _clearError();

      _dailyNutrition = await FoodDiaryService.getDailyNutrition(_selectedDate);

      debugPrint(
        'Loaded ${_dailyNutrition?.foodEntries.length ?? 0} food entries for ${_selectedDate.toIso8601String().split('T')[0]}',
      );
    } catch (e) {
      _setError('Failed to load daily nutrition: $e');
      debugPrint('Error loading daily nutrition: $e');
    } finally {
      _setLoading(false);
    }
  }

  /// Change selected date
  Future<void> changeDate(DateTime newDate) async {
    if (_selectedDate.day == newDate.day &&
        _selectedDate.month == newDate.month &&
        _selectedDate.year == newDate.year) {
      return; // Same date, no need to reload
    }

    _selectedDate = newDate;
    await loadDailyNutrition();
  }

  /// Go to previous day
  Future<void> goToPreviousDay() async {
    final previousDay = _selectedDate.subtract(const Duration(days: 1));
    await changeDate(previousDay);
  }

  /// Go to next day
  Future<void> goToNextDay() async {
    final nextDay = _selectedDate.add(const Duration(days: 1));
    await changeDate(nextDay);
  }

  /// Go to today
  Future<void> goToToday() async {
    await changeDate(DateTime.now());
  }

  /// Add a food entry
  Future<void> addFoodEntry(FoodEntry entry) async {
    try {
      _setLoading(true);
      _clearError();

      await FoodDiaryService.saveFoodEntry(entry);
      await loadDailyNutrition(); // Reload to get updated data

      debugPrint('Added food entry: ${entry.name}');
    } catch (e) {
      _setError('Failed to add food entry: $e');
      debugPrint('Error adding food entry: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Update a food entry
  Future<void> updateFoodEntry(FoodEntry entry) async {
    try {
      _setLoading(true);
      _clearError();

      await FoodDiaryService.saveFoodEntry(entry);
      await loadDailyNutrition(); // Reload to get updated data

      debugPrint('Updated food entry: ${entry.name}');
    } catch (e) {
      _setError('Failed to update food entry: $e');
      debugPrint('Error updating food entry: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Delete a food entry
  Future<void> deleteFoodEntry(String entryId) async {
    try {
      _setLoading(true);
      _clearError();

      await FoodDiaryService.deleteFoodEntry(entryId);
      await loadDailyNutrition(); // Reload to get updated data

      debugPrint('Deleted food entry: $entryId');
    } catch (e) {
      _setError('Failed to delete food entry: $e');
      debugPrint('Error deleting food entry: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Toggle meal section expansion
  void toggleMealExpansion(MealType mealType) {
    _expandedMeals[mealType] = !(_expandedMeals[mealType] ?? false);
    notifyListeners();
  }

  /// Check if meal section is expanded
  bool isMealExpanded(MealType mealType) {
    return _expandedMeals[mealType] ?? false;
  }

  /// Get meal nutrition for specific meal type
  MealNutrition getMealNutrition(MealType mealType) {
    return _dailyNutrition?.getMealNutrition(mealType) ??
        MealNutrition(
          mealType: mealType,
          entries: [],
          totalCarbs: 0,
          totalCalories: 0,
          totalProtein: 0,
          totalFat: 0,
        );
  }

  /// Search foods in database
  Future<List<FoodEntry>> searchFoods(String query) async {
    try {
      return await FoodDiaryService.searchFoods(query);
    } catch (e) {
      debugPrint('Error searching foods: $e');
      return [];
    }
  }

  /// Get food suggestions for meal type
  Future<List<FoodEntry>> getFoodSuggestions(MealType mealType) async {
    try {
      return await FoodDiaryService.getFoodSuggestions(mealType);
    } catch (e) {
      debugPrint('Error getting food suggestions: $e');
      return [];
    }
  }

  /// Get foods by category
  Future<List<FoodEntry>> getFoodsByCategory(FoodCategory category) async {
    try {
      return await FoodDiaryService.getFoodsByCategory(category);
    } catch (e) {
      debugPrint('Error getting foods by category: $e');
      return [];
    }
  }

  /// Get diabetes-friendly foods
  Future<List<FoodEntry>> getDiabetesFriendlyFoods() async {
    try {
      return await FoodDiaryService.getDiabetesFriendlyFoods();
    } catch (e) {
      debugPrint('Error getting diabetes-friendly foods: $e');
      return [];
    }
  }

  /// Get traditional South African foods
  Future<List<FoodEntry>> getTraditionalSAFoods() async {
    try {
      return await FoodDiaryService.getTraditionalSAFoods();
    } catch (e) {
      debugPrint('Error getting traditional SA foods: $e');
      return [];
    }
  }

  /// Test API connectivity
  Future<bool> testApiConnectivity() async {
    try {
      return await FoodDiaryService.testApiConnectivity();
    } catch (e) {
      debugPrint('Error testing API connectivity: $e');
      return false;
    }
  }

  /// Get API status
  Future<Map<String, dynamic>> getApiStatus() async {
    try {
      return await FoodDiaryService.getApiStatus();
    } catch (e) {
      debugPrint('Error getting API status: $e');
      return {
        'connected': false,
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }

  /// Update nutrition goals
  Future<void> updateNutritionGoals(NutritionGoals goals) async {
    try {
      _setLoading(true);
      _clearError();

      await FoodDiaryService.saveNutritionGoals(goals);
      await loadDailyNutrition(); // Reload to get updated data with new goals

      debugPrint('Updated nutrition goals');
    } catch (e) {
      _setError('Failed to update nutrition goals: $e');
      debugPrint('Error updating nutrition goals: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  /// Add blood sugar reading
  Future<void> addBloodSugarReading(BloodSugarReading reading) async {
    try {
      await FoodDiaryService.saveBloodSugarReading(reading);
      await loadDailyNutrition(); // Reload to get updated data

      debugPrint('Added blood sugar reading: ${reading.value} mg/dL');
    } catch (e) {
      _setError('Failed to add blood sugar reading: $e');
      debugPrint('Error adding blood sugar reading: $e');
      rethrow;
    }
  }

  /// Refresh data
  Future<void> refresh() async {
    await loadDailyNutrition();
  }

  /// Get formatted date string
  String get formattedSelectedDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selected = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
    );

    if (selected.isAtSameMomentAs(today)) {
      return 'Today';
    } else if (selected.isAtSameMomentAs(
      today.subtract(const Duration(days: 1)),
    )) {
      return 'Yesterday';
    } else if (selected.isAtSameMomentAs(today.add(const Duration(days: 1)))) {
      return 'Tomorrow';
    } else {
      final months = [
        'Jan',
        'Feb',
        'Mar',
        'Apr',
        'May',
        'Jun',
        'Jul',
        'Aug',
        'Sep',
        'Oct',
        'Nov',
        'Dec',
      ];
      return '${months[_selectedDate.month - 1]} ${_selectedDate.day}';
    }
  }

  /// Check if selected date is today
  bool get isToday {
    final now = DateTime.now();
    return _selectedDate.year == now.year &&
        _selectedDate.month == now.month &&
        _selectedDate.day == now.day;
  }

  /// Check if selected date is in the future
  bool get isFutureDate {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final selected = DateTime(
      _selectedDate.year,
      _selectedDate.month,
      _selectedDate.day,
    );
    return selected.isAfter(today);
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }
}
