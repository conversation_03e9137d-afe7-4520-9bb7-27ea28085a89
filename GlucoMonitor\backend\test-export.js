const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Test configuration
const BASE_URL = 'http://localhost:5000';
const TEST_EMAIL = '<EMAIL>'; // Using your configured email

// Test user credentials (you'll need to replace with actual JWT token)
let authToken = '';

// Helper function to make authenticated requests
const makeRequest = async (method, endpoint, data = null, params = {}) => {
    try {
        const config = {
            method,
            url: `${BASE_URL}${endpoint}`,
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        };

        if (data) config.data = data;
        if (Object.keys(params).length > 0) config.params = params;

        const response = await axios(config);
        return { success: true, data: response.data, status: response.status };
    } catch (error) {
        return { 
            success: false, 
            error: error.response?.data || error.message,
            status: error.response?.status 
        };
    }
};

// Test functions
const testExportFormats = async () => {
    console.log('\n🧪 Testing Export Formats...\n');

    const formats = ['json', 'csv', 'pdf', 'excel'];
    const chartTypes = ['line', 'bar', 'area', 'scatter'];

    for (const format of formats) {
        for (const chartType of chartTypes) {
            console.log(`📊 Testing ${format.toUpperCase()} export with ${chartType} chart...`);
            
            const result = await makeRequest('GET', '/api/glucose/export/chart', null, {
                format,
                chartType,
                destination: 'download'
            });

            if (result.success) {
                console.log(`✅ ${format.toUpperCase()} ${chartType} export: SUCCESS`);
            } else {
                console.log(`❌ ${format.toUpperCase()} ${chartType} export: FAILED`);
                console.log(`   Error: ${result.error?.message || result.error}`);
            }
        }
    }
};

const testEmailExport = async () => {
    console.log('\n📧 Testing Email Export...\n');

    const result = await makeRequest('GET', '/api/glucose/export/chart', null, {
        format: 'pdf',
        chartType: 'line',
        destination: 'email',
        email: TEST_EMAIL
    });

    if (result.success) {
        console.log('✅ Email export: SUCCESS');
        console.log(`   Email sent to: ${TEST_EMAIL}`);
    } else {
        console.log('❌ Email export: FAILED');
        console.log(`   Error: ${result.error?.message || result.error}`);
    }
};

const testCloudExports = async () => {
    console.log('\n☁️ Testing Cloud Storage Exports...\n');

    // Test Google Drive
    console.log('📁 Testing Google Drive export...');
    const driveResult = await makeRequest('GET', '/api/glucose/export/chart', null, {
        format: 'excel',
        chartType: 'line',
        destination: 'googledrive'
    });

    if (driveResult.success) {
        console.log('✅ Google Drive export: SUCCESS');
        console.log(`   Link: ${driveResult.data?.data?.link || 'No link provided'}`);
    } else {
        console.log('❌ Google Drive export: FAILED');
        console.log(`   Error: ${driveResult.error?.message || driveResult.error}`);
    }

    // Test Dropbox
    console.log('\n📦 Testing Dropbox export...');
    const dropboxResult = await makeRequest('GET', '/api/glucose/export/chart', null, {
        format: 'pdf',
        chartType: 'bar',
        destination: 'dropbox'
    });

    if (dropboxResult.success) {
        console.log('✅ Dropbox export: SUCCESS');
        console.log(`   Link: ${dropboxResult.data?.data?.link || 'No link provided'}`);
    } else {
        console.log('❌ Dropbox export: FAILED');
        console.log(`   Error: ${dropboxResult.error?.message || dropboxResult.error}`);
    }
};

const testBulkExport = async () => {
    console.log('\n📦 Testing Bulk Export...\n');

    const result = await makeRequest('POST', '/api/glucose/export/bulk', {
        chartType: 'line',
        formats: ['json', 'csv', 'pdf'],
        destinations: ['download'],
        includeAllFormats: false
    });

    if (result.success) {
        console.log('✅ Bulk export: SUCCESS');
        console.log(`   Total exports: ${result.data?.data?.totalExports || 0}`);
        console.log(`   Successful: ${result.data?.data?.successful || 0}`);
        console.log(`   Failed: ${result.data?.data?.failed || 0}`);
    } else {
        console.log('❌ Bulk export: FAILED');
        console.log(`   Error: ${result.error?.message || result.error}`);
    }
};

const testLocalExport = async () => {
    console.log('\n💾 Testing Local Export...\n');

    const result = await makeRequest('GET', '/api/glucose/export/chart', null, {
        format: 'excel',
        chartType: 'line',
        destination: 'local'
    });

    if (result.success) {
        console.log('✅ Local export: SUCCESS');
        console.log(`   File path: ${result.data?.data?.path || 'No path provided'}`);
    } else {
        console.log('❌ Local export: FAILED');
        console.log(`   Error: ${result.error?.message || result.error}`);
    }
};

// Main test runner
const runTests = async () => {
    console.log('🚀 GlucoMonitor Export Functionality Test Suite');
    console.log('================================================\n');

    // Check if auth token is provided
    if (!authToken) {
        console.log('❌ No authentication token provided!');
        console.log('Please set the authToken variable with a valid JWT token.');
        console.log('\nTo get a token:');
        console.log('1. Start the backend server');
        console.log('2. Login through the API or app');
        console.log('3. Copy the JWT token');
        console.log('4. Set authToken = "your_jwt_token_here" in this script\n');
        return;
    }

    try {
        // Test basic export formats
        await testExportFormats();

        // Test email functionality
        await testEmailExport();

        // Test cloud storage
        await testCloudExports();

        // Test bulk export
        await testBulkExport();

        // Test local export
        await testLocalExport();

        console.log('\n🎉 Test suite completed!');
        console.log('Check the results above for any failures.');

    } catch (error) {
        console.error('\n💥 Test suite failed with error:', error.message);
    }
};

// Instructions for running the test
console.log('📋 INSTRUCTIONS:');
console.log('1. Make sure your backend server is running on port 5000');
console.log('2. Set your JWT token in the authToken variable below');
console.log('3. Run: node test-export.js\n');

// TODO: Replace with your actual JWT token
authToken = 'YOUR_JWT_TOKEN_HERE';

// Run tests if token is provided
if (authToken && authToken !== 'YOUR_JWT_TOKEN_HERE') {
    runTests();
} else {
    console.log('⚠️  Please set your JWT token in the authToken variable and run again.');
}
