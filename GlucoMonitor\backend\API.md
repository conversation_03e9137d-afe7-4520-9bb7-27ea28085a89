# GlucoMonitor Backend API Documentation

This document provides comprehensive documentation for the GlucoMonitor TypeScript backend API endpoints.

## Base URL

- **Development**: `http://localhost:5000`
- **Production**: `https://your-domain.com`

## Authentication

The API uses JW<PERSON> (JSON Web Tokens) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow this standard format:

```json
{
  "success": true|false,
  "message": "Response message",
  "data": {}, // Optional data object
  "token": "jwt-token", // Only for auth endpoints
  "user": {} // Only for auth endpoints
}
```

## Error Handling

Error responses include appropriate HTTP status codes and descriptive messages:

```json
{
  "success": false,
  "message": "Error description"
}
```

## Rate Limiting

- **Development**: 20 requests per hour per phone number
- **Production**: 5 requests per hour per phone number
- Rate limiting applies to OTP-related endpoints
- Uses Redis for tracking (falls back to in-memory storage)

## Endpoints

### Authentication Endpoints

#### Register User
**POST** `/api/auth/register`

Register a new user with email, password, and phone verification.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+27123456789",
  "name": "John Doe"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully. Please verify your phone number.",
  "userId": "64a7b8c9d1e2f3g4h5i6j7k8"
}
```

**Notes:**
- Phone number must be in South African format (+27...)
- Password must be at least 6 characters
- Sends OTP to provided phone number
- In development mode, OTP is fixed as "123456"

---

#### Verify OTP
**POST** `/api/auth/verify-otp`

Verify phone number with OTP received via SMS.

**Request Body:**
```json
{
  "phoneNumber": "+27123456789",
  "otp": "123456"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "name": "John Doe",
    "diabetesType": null,
    "language": "en"
  }
}
```

**Notes:**
- OTP expires after 10 minutes
- In development mode, use OTP "123456"
- Returns JWT token for authenticated requests

---

#### Login
**POST** `/api/auth/login`

Authenticate user with email and password.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Response:**
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "64a7b8c9d1e2f3g4h5i6j7k8",
    "email": "<EMAIL>",
    "name": "John Doe",
    "phoneNumber": "+27123456789",
    "diabetesType": null,
    "language": "en"
  }
}
```

**Notes:**
- Requires verified phone number
- Returns JWT token and user profile

---

#### Get Current User
**GET** `/api/auth/me`

Get current authenticated user's profile.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Response:**
```json
{
  "success": true,
  "user": {
    "_id": "64a7b8c9d1e2f3g4h5i6j7k8",
    "email": "<EMAIL>",
    "phoneNumber": "+27123456789",
    "isPhoneVerified": true,
    "name": "John Doe",
    "diabetesType": null,
    "language": "en",
    "popiaConsent": null,
    "createdAt": "2025-07-03T09:12:13.761Z",
    "updatedAt": "2025-07-03T09:12:22.228Z"
  }
}
```

---

#### Update Profile
**PUT** `/api/auth/profile`

Update current user's profile information.

**Headers:**
```
Authorization: Bearer <jwt-token>
```

**Request Body:**
```json
{
  "name": "John Smith",
  "diabetesType": "type1",
  "language": "af",
  "popiaConsent": true
}
```

**Response:**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "user": {
    "id": "64a7b8c9d1e2f3g4h5i6j7k8",
    "name": "John Smith",
    "diabetesType": "type1",
    "language": "af"
  }
}
```

---

#### Forgot Password
**POST** `/api/auth/forgot-password`

Request password reset token via SMS.

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset code sent to your registered phone number"
}
```

**Notes:**
- Sends reset token to user's verified phone number
- Token expires after 10 minutes
- User must have verified phone number

---

#### Reset Password
**POST** `/api/auth/reset-password`

Reset password using token received via SMS.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "resetToken": "91dca895c43f0b41f66139556a2103463b525533",
  "newPassword": "newpassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Password reset successful. You can now login with your new password."
}
```

**Notes:**
- Reset token is sent via SMS in forgot password flow
- Token expires after 10 minutes
- New password must be at least 6 characters

---

### Development Endpoints

#### Clear Rate Limit
**POST** `/api/auth/clear-rate-limit`

Clear rate limiting for a specific phone number (development only).

**Request Body:**
```json
{
  "phoneNumber": "+27123456789"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Rate limit cleared for +27123456789"
}
```

**Notes:**
- Only available in development mode
- Useful for testing OTP flows

---

## TypeScript Types

The backend uses comprehensive TypeScript types for all request/response objects:

```typescript
// User registration request
interface RegisterRequest {
  email: string;
  password: string;
  phoneNumber: string;
  name: string;
}

// Login request
interface LoginRequest {
  email: string;
  password: string;
}

// OTP verification request
interface VerifyOTPRequest {
  phoneNumber: string;
  otp: string;
}

// Auth response with token
interface AuthResponse {
  success: boolean;
  token: string;
  user: {
    id: string;
    email: string;
    name: string;
    phoneNumber: string;
    diabetesType: string | null;
    language: string;
  };
}
```

## Error Codes

| Status Code | Description |
|-------------|-------------|
| 200 | Success |
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or missing token |
| 403 | Forbidden - Access denied |
| 404 | Not Found - Resource not found |
| 429 | Too Many Requests - Rate limit exceeded |
| 500 | Internal Server Error - Server error |

## Development Notes

### Fixed OTP in Development
In development mode (`NODE_ENV=development`), all OTP verifications accept the fixed code "123456" for easier testing.

### Redis Fallback
When Redis is unavailable, the system automatically falls back to in-memory storage for:
- OTP storage and verification
- Rate limiting

### SMS Integration
The backend integrates with SMSPortal for SMS delivery. In development mode, SMS sending is simulated with console logging.

### Phone Number Format
All phone numbers must be in South African format starting with "+27".

## Testing Examples

See the main [README.md](./README.md) for comprehensive testing examples using curl commands.
