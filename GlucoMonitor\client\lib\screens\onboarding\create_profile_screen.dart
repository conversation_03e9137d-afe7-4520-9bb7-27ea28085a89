import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/profile_picture_picker.dart';
import '../../widgets/guardian_contact_form.dart';
import '../main/main_screen.dart';

class CreateProfileScreen extends StatefulWidget {
  final bool isOfflineMode;

  const CreateProfileScreen({super.key, this.isOfflineMode = false});

  @override
  State<CreateProfileScreen> createState() => _CreateProfileScreenState();
}

class _CreateProfileScreenState extends State<CreateProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _guardianNameController = TextEditingController();
  final _guardianPhoneController = TextEditingController();
  final _guardianEmailController = TextEditingController();
  String? _selectedAgeGroup;
  String? _selectedDiabetesType;
  String? _profilePicturePath;
  bool _consentGiven = false;
  bool _loading = false;
  bool _showGuardianForm = false;

  final _ageGroups = ['Under 18', '18-30', '31-45', '46-60', 'Over 60'];

  final _diabetesTypes = [
    'Type 1',
    'Type 2',
    'Gestational',
    'Pre-diabetes',
    'Not sure',
  ];

  @override
  void initState() {
    super.initState();
    _loadProfileDraft();

    // Add listeners for auto-save
    _nameController.addListener(_saveDraft);
    _guardianNameController.addListener(_saveDraft);
    _guardianPhoneController.addListener(_saveDraft);
    _guardianEmailController.addListener(_saveDraft);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _guardianNameController.dispose();
    _guardianPhoneController.dispose();
    _guardianEmailController.dispose();
    super.dispose();
  }

  Future<void> _loadProfileDraft() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    final draft = await authProvider.loadProfileDraft();

    if (draft != null) {
      setState(() {
        if (draft['name'] != null) _nameController.text = draft['name'];
        if (draft['ageGroup'] != null) {
          _selectedAgeGroup = draft['ageGroup'];
          _showGuardianForm = _selectedAgeGroup == 'Under 18';
        }
        if (draft['diabetesType'] != null) {
          _selectedDiabetesType = draft['diabetesType'];
        }
        if (draft['profilePicturePath'] != null) {
          _profilePicturePath = draft['profilePicturePath'];
        }
        if (draft['guardianName'] != null) {
          _guardianNameController.text = draft['guardianName'];
        }
        if (draft['guardianPhone'] != null) {
          _guardianPhoneController.text = draft['guardianPhone'];
        }
        if (draft['guardianEmail'] != null) {
          _guardianEmailController.text = draft['guardianEmail'];
        }
      });
    }
  }

  Future<void> _saveDraft() async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);
    await authProvider.saveProfileDraft(
      name: _nameController.text.isNotEmpty ? _nameController.text : null,
      ageGroup: _selectedAgeGroup,
      diabetesType: _selectedDiabetesType,
      profilePicturePath: _profilePicturePath,
      guardianName:
          _guardianNameController.text.isNotEmpty
              ? _guardianNameController.text
              : null,
      guardianPhone:
          _guardianPhoneController.text.isNotEmpty
              ? _guardianPhoneController.text
              : null,
      guardianEmail:
          _guardianEmailController.text.isNotEmpty
              ? _guardianEmailController.text
              : null,
    );
  }

  Future<void> _saveProfile() async {
    if (_formKey.currentState?.validate() ?? false) {
      if (!_consentGiven && !widget.isOfflineMode) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Please accept the privacy policy to continue'),
          ),
        );
        return;
      }

      setState(() => _loading = true);

      try {
        final authProvider = Provider.of<AuthProvider>(context, listen: false);
        final success = await authProvider.updateProfile(
          name: _nameController.text,
          ageGroup: _selectedAgeGroup!,
          diabetesType: _selectedDiabetesType,
          givePopiaConsent: _consentGiven,
          isOfflineMode: widget.isOfflineMode,
          profilePicturePath: _profilePicturePath,
          guardianName:
              _guardianNameController.text.isNotEmpty
                  ? _guardianNameController.text
                  : null,
          guardianPhone:
              _guardianPhoneController.text.isNotEmpty
                  ? _guardianPhoneController.text
                  : null,
          guardianEmail:
              _guardianEmailController.text.isNotEmpty
                  ? _guardianEmailController.text
                  : null,
        );

        if (!mounted) return;

        if (success) {
          // Clear the draft since profile was saved successfully
          await authProvider.clearProfileDraft();

          if (!mounted) return;

          // Navigate to main screen
          Navigator.of(context).pushAndRemoveUntil(
            MaterialPageRoute(builder: (_) => const MainScreen()),
            (route) => false,
          );
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Failed to save profile')),
          );
        }
      } catch (e) {
        if (!mounted) return;
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(e.toString())));
      } finally {
        if (mounted) {
          setState(() => _loading = false);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Create Profile'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onBackground,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                // Profile Picture Picker
                Center(
                  child: ProfilePicturePicker(
                    initialImagePath: _profilePicturePath,
                    onImageSelected: (path) {
                      setState(() {
                        _profilePicturePath = path;
                      });
                      _saveDraft(); // Auto-save when image is selected
                    },
                  ),
                ),
                const SizedBox(height: 24),
                TextFormField(
                  controller: _nameController,
                  style: const TextStyle(color: AppColors.onBackground),
                  decoration: const InputDecoration(
                    labelText: 'Full Name',
                    labelStyle: TextStyle(color: AppColors.onBackground),
                    border: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.onBackground),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.onBackground),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderSide: BorderSide(color: AppColors.onBackground),
                    ),
                  ),
                  textCapitalization: TextCapitalization.words,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                DropdownButtonFormField<String>(
                  value: _selectedAgeGroup,
                  decoration: const InputDecoration(
                    labelText: 'Age Group',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      _ageGroups.map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedAgeGroup = value;
                      _showGuardianForm = value == 'Under 18';
                    });
                    _saveDraft(); // Auto-save when age group changes
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select your age group';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                DropdownButtonFormField<String>(
                  value: _selectedDiabetesType,
                  decoration: const InputDecoration(
                    labelText: 'Type of Diabetes',
                    border: OutlineInputBorder(),
                  ),
                  items:
                      _diabetesTypes.map((String value) {
                        return DropdownMenuItem<String>(
                          value: value,
                          child: Text(value),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDiabetesType = value;
                    });
                    _saveDraft(); // Auto-save when diabetes type changes
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'Please select your diabetes type';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 24),
                // Guardian Contact Form (shown for users under 18)
                if (_showGuardianForm) ...[
                  GuardianContactForm(
                    nameController: _guardianNameController,
                    phoneController: _guardianPhoneController,
                    emailController: _guardianEmailController,
                    isRequired: true,
                  ),
                  const SizedBox(height: 24),
                ],
                if (!widget.isOfflineMode) ...[
                  Row(
                    children: [
                      Checkbox(
                        value: _consentGiven,
                        onChanged: (value) {
                          setState(() {
                            _consentGiven = value ?? false;
                          });
                        },
                      ),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _consentGiven = !_consentGiven;
                            });
                          },
                          child: const Text(
                            'I consent to the collection and processing of my personal information in accordance with POPIA.',
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                ],
                ElevatedButton(
                  onPressed: _loading ? null : _saveProfile,
                  style: ElevatedButton.styleFrom(
                    minimumSize: const Size(double.infinity, 50),
                  ),
                  child:
                      _loading
                          ? const CircularProgressIndicator()
                          : const Text('Complete Profile'),
                ),
                if (widget.isOfflineMode) ...[
                  const SizedBox(height: 16),
                  OutlinedButton(
                    onPressed: () async {
                      final navigator = Navigator.of(context);
                      final scaffoldMessenger = ScaffoldMessenger.of(context);
                      await _saveDraft();
                      if (mounted) {
                        scaffoldMessenger.showSnackBar(
                          const SnackBar(
                            content: Text(
                              'Progress saved! You can continue later.',
                            ),
                          ),
                        );
                        navigator.pop();
                      }
                    },
                    child: const Text(
                      'Save Progress & Continue Later',
                      style: TextStyle(color: Colors.white),
                    ),
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'Note: Your profile will be stored locally and can be synced later when you go online. Use "Save Progress" to continue filling this form later.',
                    style: TextStyle(color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }
}
