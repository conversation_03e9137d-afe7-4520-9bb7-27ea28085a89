import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../models/medication.dart';
import '../../providers/medication_provider.dart';
import '../../services/medication_service.dart';

class AddMedicationModal extends StatefulWidget {
  final Medication? editMedication;

  const AddMedicationModal({super.key, this.editMedication});

  @override
  State<AddMedicationModal> createState() => _AddMedicationModalState();
}

class _AddMedicationModalState extends State<AddMedicationModal> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _brandNameController = TextEditingController();
  final _dosageController = TextEditingController();
  final _dosageUnitController = TextEditingController();
  final _notesController = TextEditingController();
  final _prescribedByController = TextEditingController();

  MedicationType _selectedType = MedicationType.tablet;
  MedicationFrequency _selectedFrequency = MedicationFrequency.onceDaily;
  List<MedicationTime> _medicationTimes = [];
  DateTime _startDate = DateTime.now();
  DateTime? _endDate;
  bool _reminderEnabled = true;
  int _reminderMinutesBefore = 15;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    if (widget.editMedication != null) {
      _populateFields(widget.editMedication!);
    } else {
      _setDefaultTimes();
    }
  }

  void _populateFields(Medication medication) {
    _nameController.text = medication.name;
    _brandNameController.text = medication.brandName ?? '';
    _dosageController.text = medication.dosage.toString();
    _dosageUnitController.text = medication.dosageUnit;
    _notesController.text = medication.notes ?? '';
    _prescribedByController.text = medication.prescribedBy ?? '';
    _selectedType = medication.type;
    _selectedFrequency = medication.frequency;
    _medicationTimes = List.from(medication.times);
    _startDate = medication.startDate;
    _endDate = medication.endDate;
    _reminderEnabled = medication.reminderEnabled;
    _reminderMinutesBefore = medication.reminderMinutesBefore;
  }

  void _setDefaultTimes() {
    switch (_selectedFrequency) {
      case MedicationFrequency.onceDaily:
        _medicationTimes = [
          const MedicationTime(
            time: TimeOfDay(hour: 8, minute: 0),
            label: 'Morning',
          ),
        ];
        break;
      case MedicationFrequency.twiceDaily:
        _medicationTimes = [
          const MedicationTime(
            time: TimeOfDay(hour: 8, minute: 0),
            label: 'Morning',
          ),
          const MedicationTime(
            time: TimeOfDay(hour: 20, minute: 0),
            label: 'Evening',
          ),
        ];
        break;
      case MedicationFrequency.threeTimesDaily:
        _medicationTimes = [
          const MedicationTime(
            time: TimeOfDay(hour: 8, minute: 0),
            label: 'Morning',
          ),
          const MedicationTime(
            time: TimeOfDay(hour: 14, minute: 0),
            label: 'Afternoon',
          ),
          const MedicationTime(
            time: TimeOfDay(hour: 20, minute: 0),
            label: 'Evening',
          ),
        ];
        break;
      case MedicationFrequency.fourTimesDaily:
        _medicationTimes = [
          const MedicationTime(
            time: TimeOfDay(hour: 8, minute: 0),
            label: 'Morning',
          ),
          const MedicationTime(
            time: TimeOfDay(hour: 12, minute: 0),
            label: 'Noon',
          ),
          const MedicationTime(
            time: TimeOfDay(hour: 16, minute: 0),
            label: 'Afternoon',
          ),
          const MedicationTime(
            time: TimeOfDay(hour: 20, minute: 0),
            label: 'Evening',
          ),
        ];
        break;
      default:
        _medicationTimes = [
          const MedicationTime(
            time: TimeOfDay(hour: 8, minute: 0),
            label: 'Morning',
          ),
        ];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        expand: false,
        builder: (context, scrollController) {
          return Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: AppColors.textSecondary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        widget.editMedication != null
                            ? 'Edit Medication'
                            : 'Add Medication',
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppColors.onSurface,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Navigator.of(context).pop(),
                      icon: const Icon(Icons.close, color: AppColors.onSurface),
                    ),
                  ],
                ),
              ),

              const Divider(color: AppColors.santasGray),

              // Form
              Expanded(
                child: Form(
                  key: _formKey,
                  child: ListView(
                    controller: scrollController,
                    padding: const EdgeInsets.all(16),
                    children: [
                      _buildBasicInfoSection(),
                      const SizedBox(height: 24),
                      _buildDosageSection(),
                      const SizedBox(height: 24),
                      _buildScheduleSection(),
                      const SizedBox(height: 24),
                      _buildReminderSection(),
                      const SizedBox(height: 24),
                      _buildAdditionalInfoSection(),
                      const SizedBox(height: 32),
                      _buildActionButtons(),
                      const SizedBox(height: 16),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Basic Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _nameController,
          decoration: const InputDecoration(
            labelText: 'Medication Name *',
            hintText: 'e.g., Metformin',
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'Please enter medication name';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _brandNameController,
          decoration: const InputDecoration(
            labelText: 'Brand Name (Optional)',
            hintText: 'e.g., Glucophage',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<MedicationType>(
          value: _selectedType,
          decoration: const InputDecoration(
            labelText: 'Medication Type',
            border: OutlineInputBorder(),
          ),
          items:
              MedicationType.values.map((type) {
                return DropdownMenuItem(
                  value: type,
                  child: Text(type.name.toUpperCase()),
                );
              }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedType = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildDosageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Dosage Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              flex: 2,
              child: TextFormField(
                controller: _dosageController,
                keyboardType: TextInputType.number,
                decoration: const InputDecoration(
                  labelText: 'Dosage *',
                  hintText: '500',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter dosage';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                controller: _dosageUnitController,
                decoration: const InputDecoration(
                  labelText: 'Unit *',
                  hintText: 'mg',
                  border: OutlineInputBorder(),
                ),
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return 'Please enter unit';
                  }
                  return null;
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildScheduleSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Schedule',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        DropdownButtonFormField<MedicationFrequency>(
          value: _selectedFrequency,
          decoration: const InputDecoration(
            labelText: 'Frequency',
            border: OutlineInputBorder(),
          ),
          items:
              MedicationFrequency.values.map((frequency) {
                return DropdownMenuItem(
                  value: frequency,
                  child: Text(_getFrequencyDisplay(frequency)),
                );
              }).toList(),
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _selectedFrequency = value;
                _setDefaultTimes();
              });
            }
          },
        ),
        const SizedBox(height: 16),
        const Text(
          'Times',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 8),
        ..._medicationTimes.asMap().entries.map((entry) {
          final index = entry.key;
          final medicationTime = entry.value;
          return _buildTimeItem(index, medicationTime);
        }),
        if (_selectedFrequency == MedicationFrequency.custom)
          TextButton.icon(
            onPressed: _addCustomTime,
            icon: const Icon(Icons.add),
            label: const Text('Add Time'),
          ),
      ],
    );
  }

  Widget _buildTimeItem(int index, MedicationTime medicationTime) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: const Icon(Icons.schedule),
        title: Text(medicationTime.time.format(context)),
        subtitle:
            medicationTime.label != null ? Text(medicationTime.label!) : null,
        trailing:
            _selectedFrequency == MedicationFrequency.custom
                ? IconButton(
                  onPressed: () => _removeCustomTime(index),
                  icon: const Icon(Icons.delete),
                )
                : null,
        onTap: () => _editTime(index),
      ),
    );
  }

  Widget _buildReminderSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Reminders',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        SwitchListTile(
          title: const Text('Enable Reminders'),
          subtitle: const Text(
            'Get notified when it\'s time to take your medication',
          ),
          value: _reminderEnabled,
          onChanged: (value) {
            setState(() {
              _reminderEnabled = value;
            });
          },
        ),
        if (_reminderEnabled) ...[
          const SizedBox(height: 16),
          DropdownButtonFormField<int>(
            value: _reminderMinutesBefore,
            decoration: const InputDecoration(
              labelText: 'Remind me before',
              border: OutlineInputBorder(),
            ),
            items: const [
              DropdownMenuItem(value: 0, child: Text('At the time')),
              DropdownMenuItem(value: 5, child: Text('5 minutes before')),
              DropdownMenuItem(value: 15, child: Text('15 minutes before')),
              DropdownMenuItem(value: 30, child: Text('30 minutes before')),
              DropdownMenuItem(value: 60, child: Text('1 hour before')),
            ],
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _reminderMinutesBefore = value;
                });
              }
            },
          ),
        ],
      ],
    );
  }

  Widget _buildAdditionalInfoSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Additional Information',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _prescribedByController,
          decoration: const InputDecoration(
            labelText: 'Prescribed By (Optional)',
            hintText: 'Dr. Smith',
            border: OutlineInputBorder(),
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: _notesController,
          maxLines: 3,
          decoration: const InputDecoration(
            labelText: 'Notes (Optional)',
            hintText: 'Take with food, avoid alcohol, etc.',
            border: OutlineInputBorder(),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton(
            onPressed: _isLoading ? null : () => Navigator.of(context).pop(),
            style: OutlinedButton.styleFrom(
              foregroundColor: Colors.white,
              side: const BorderSide(color: Colors.white),
            ),
            child: const Text('Cancel'),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _isLoading ? null : _saveMedication,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primary,
              foregroundColor: Colors.white,
            ),
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : Text(widget.editMedication != null ? 'Update' : 'Save'),
          ),
        ),
      ],
    );
  }

  String _getFrequencyDisplay(MedicationFrequency frequency) {
    switch (frequency) {
      case MedicationFrequency.onceDaily:
        return 'Once daily';
      case MedicationFrequency.twiceDaily:
        return 'Twice daily';
      case MedicationFrequency.threeTimesDaily:
        return 'Three times daily';
      case MedicationFrequency.fourTimesDaily:
        return 'Four times daily';
      case MedicationFrequency.everyOtherDay:
        return 'Every other day';
      case MedicationFrequency.weekly:
        return 'Weekly';
      case MedicationFrequency.asNeeded:
        return 'As needed';
      case MedicationFrequency.custom:
        return 'Custom schedule';
    }
  }

  void _addCustomTime() {
    showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 8, minute: 0),
    ).then((time) {
      if (time != null) {
        setState(() {
          _medicationTimes.add(MedicationTime(time: time));
        });
      }
    });
  }

  void _removeCustomTime(int index) {
    setState(() {
      _medicationTimes.removeAt(index);
    });
  }

  void _editTime(int index) {
    showTimePicker(
      context: context,
      initialTime: _medicationTimes[index].time,
    ).then((time) {
      if (time != null) {
        setState(() {
          _medicationTimes[index] = MedicationTime(
            time: time,
            label: _medicationTimes[index].label,
          );
        });
      }
    });
  }

  Future<void> _saveMedication() async {
    if (!_formKey.currentState!.validate()) return;
    if (_medicationTimes.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please add at least one time')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final now = DateTime.now();
      final medication = Medication(
        id: widget.editMedication?.id ?? MedicationService.generateId(),
        name: _nameController.text.trim(),
        brandName:
            _brandNameController.text.trim().isEmpty
                ? null
                : _brandNameController.text.trim(),
        type: _selectedType,
        dosage: double.parse(_dosageController.text),
        dosageUnit: _dosageUnitController.text.trim(),
        frequency: _selectedFrequency,
        times: _medicationTimes,
        startDate: _startDate,
        endDate: _endDate,
        notes:
            _notesController.text.trim().isEmpty
                ? null
                : _notesController.text.trim(),
        prescribedBy:
            _prescribedByController.text.trim().isEmpty
                ? null
                : _prescribedByController.text.trim(),
        reminderEnabled: _reminderEnabled,
        reminderMinutesBefore: _reminderMinutesBefore,
        createdAt: widget.editMedication?.createdAt ?? now,
        updatedAt: now,
      );

      await context.read<MedicationProvider>().saveMedication(medication);

      if (mounted) {
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              widget.editMedication != null
                  ? 'Medication updated successfully'
                  : 'Medication added successfully',
            ),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Error: $e')));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _brandNameController.dispose();
    _dosageController.dispose();
    _dosageUnitController.dispose();
    _notesController.dispose();
    _prescribedByController.dispose();
    super.dispose();
  }
}
