"use client";
import { Poppins } from 'next/font/google'
import Link from 'next/link'
import Image from 'next/image'
import logo from '@/assets/logo.png'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { usePathname } from 'next/navigation'
import NavbarSidebar from './navbar-sidebar'
import { useState } from 'react'
import { MenuIcon } from 'lucide-react'

const poppins = Poppins({
    subsets: ['latin'],
    weight: ['700'],
    //display: 'swap',
})

interface NavbarItemProps {
    href: string;
    children: React.ReactNode;
    isActive?: boolean;
};

const NavbarItem = ({
  href,
  children,
  isActive,
}: NavbarItemProps) => {
    return (
        <Button
        asChild
        variant="outline"
        className={cn(
            "bg-transparent hover:bg-transparent rounded-full hover:border-primary border-transparent px-3.5 text-lg",
            isActive && "bg-[var( --background)] text-white hover:bg-[var( --background)] text-white",
        )}
        > 
          <Link href={href}>
          {children}
          </Link>
        </Button>
    );
};

const navbarItems = [
    { href: "/", children: "Home" },
    { href: "/about", children: "About" },
    { href: "/features", children: "Features" },
    { href: "/pricing", children: "Pricing" },
    { href: "/contact", children: "Contact" },
];

export const Navbar = () => {
    const pathname = usePathname();
    const [isSidebarOpen, setIsSidebarOpen] = useState(false);

    return (
       <nav className="h-20 flex border justify-between font-medium bg-[var(--primary)]">
        <Link href="/" className="pl-6 flex items-center">
            <div className="relative w-55 h-45 flex items-center justify-center">
                <Image 
                    src={logo}
                    alt="Sadevtech Logo"
                    className="object-contain"
                    fill
                    priority
                />
            </div>
        </Link>

        <NavbarSidebar
        items={navbarItems}
         open={isSidebarOpen}
         onOpenChange={setIsSidebarOpen}
        />

        <div className="items-center gap-4 hidden lg:flex">
           {navbarItems.map((item) => (
            <NavbarItem 
             key={item.href}
             href={item.href}
             isActive={pathname === item.href}
             >
              {item.children}
            </NavbarItem>
           ))}
        </div>

        <div className="hidden lg:flex">
          <Button 
          asChild
          variant="secondary"
          className="border-l border-t-0 border-b-0 border-r-0 px-12 h-full rounded-none bg-[var(--accent)] hover:bg-[var(--primary)] transition-colors text-lg text-[var(--foreground)]"
          >
            <Link href="/sign-in">
            Log in
            </Link>
             
          </Button>
          <Button
          asChild
            className="border-l border-t-0 border-b-0 border-r-0 px-12 h-full rounded-none bg-[var(--primary)] hover:bg-[var(--accent)] transition-colors text-lg text-[var(--foreground)]"
          >
              <Link href="/sign-up">
              Start selling
              </Link>
          </Button>
        </div>

        <div className="flex lg:hidden items-center justify-center">
          <Button
            variant="ghost"
            className="size-12 border-transparent bg-[var(--background)]"
            onClick={() => setIsSidebarOpen(true)} 
          >
            <MenuIcon/>
          </Button>
        </div>
       </nav>
    )
}

