import { She<PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
     SheetTitle } 
     from "@/components/ui/sheet";
 import { ScrollArea } from "@/components/ui/scroll-area";    
import Link from "next/link";

interface NavbarItem{
    href: string;
    children: React.ReactNode;
   // isActive?: boolean;
}


interface Props {
    items: NavbarItem[];
    open: boolean;
    onOpenChange: (open: boolean) => void;
}

export default function NavbarSidebar({
    items,
    open,
    onOpenChange,
}:Props) {
  return(
    <Sheet open={open} onOpenChange={onOpenChange}>
         <SheetContent
           side="left"
           className="p-0 transition-none"
         >
         <SheetHeader className="p-4 border-b">
          
            <SheetTitle>
              Menu
            </SheetTitle>
          
         </SheetHeader>
         <ScrollArea className="flex flex-col overflow-y-auto h-full pb-2">
          {items.map ((item) => (
            <Link
            key={item.href}
            href={item.href}
            className="w-full text-left p-4 hover:bg-[var(--primary)] hover:text-[var(--background)] flex items-center text-base font-medium"
            onClick={() => onOpenChange(false)}
            >
             {item.children}
            </Link>
          ))}
          <div className="border-t">
           <Link 
           onClick={() => onOpenChange(false)} href="/sign-in" className="w-full text-left p-4 hover:bg-[var(--primary)] hover:text-[var(--background)] flex items-center text-base font-medium">
            Log in
           </Link>
           <Link 
           onClick={() => onOpenChange(false)} href="/sign-up" className="w-full text-left p-4 hover:bg-[var(--primary)] hover:text-[var(--background)] flex items-center text-base font-medium">
            Start selling
           </Link>
          </div>
         </ScrollArea>
         </SheetContent>
    </Sheet>
  )  
};
