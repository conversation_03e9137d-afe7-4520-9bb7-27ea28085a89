import mongoose, { Document, Schema } from 'mongoose';

// Enums matching Flutter frontend
export enum GlycemicIndex {
    LOW = 'low',
    MEDIUM = 'medium', 
    HIGH = 'high'
}

export enum FoodCategory {
    GRAINS_STARCHES = 'grains_starches',
    VEGETABLES = 'vegetables',
    FRUITS = 'fruits',
    PROTEINS = 'proteins',
    DAIRY = 'dairy',
    FATS_OILS = 'fats_oils',
    BEVERAGES = 'beverages',
    SNACKS_SWEETS = 'snacks_sweets',
    TRADITIONAL_SA = 'traditional_sa',
    FAST_FOOD = 'fast_food'
}

export enum MealType {
    BREAKFAST = 'breakfast',
    MORNING_SNACK = 'morning_snack',
    LUNCH = 'lunch',
    AFTERNOON_SNACK = 'afternoon_snack',
    DINNER = 'dinner',
    EVENING_SNACK = 'evening_snack',
    LATE_NIGHT = 'late_night',
    CUSTOM = 'custom'
}

// Interface for custom meal time configuration
export interface ICustomMealTime {
    id: string;
    name: string;
    displayName: string;
    icon: string;
    color: string;
    defaultTime: string; // HH:mm format
    timeRange: {
        start: string; // HH:mm format
        end: string;   // HH:mm format
    };
    isActive: boolean;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
}

// Default meal time configurations
export const DEFAULT_MEAL_TIMES = {
    [MealType.BREAKFAST]: {
        displayName: 'Breakfast',
        icon: 'wb_sunny',
        color: '#FF9800',
        defaultTime: '07:00',
        timeRange: { start: '05:00', end: '11:00' }
    },
    [MealType.MORNING_SNACK]: {
        displayName: 'Morning Snack',
        icon: 'coffee',
        color: '#8BC34A',
        defaultTime: '10:00',
        timeRange: { start: '09:00', end: '11:30' }
    },
    [MealType.LUNCH]: {
        displayName: 'Lunch',
        icon: 'restaurant',
        color: '#2196F3',
        defaultTime: '12:30',
        timeRange: { start: '11:00', end: '15:00' }
    },
    [MealType.AFTERNOON_SNACK]: {
        displayName: 'Afternoon Snack',
        icon: 'local_cafe',
        color: '#FF5722',
        defaultTime: '15:30',
        timeRange: { start: '14:00', end: '17:00' }
    },
    [MealType.DINNER]: {
        displayName: 'Dinner',
        icon: 'dinner_dining',
        color: '#3F51B5',
        defaultTime: '19:00',
        timeRange: { start: '17:00', end: '22:00' }
    },
    [MealType.EVENING_SNACK]: {
        displayName: 'Evening Snack',
        icon: 'cookie',
        color: '#9C27B0',
        defaultTime: '20:30',
        timeRange: { start: '19:30', end: '23:00' }
    },
    [MealType.LATE_NIGHT]: {
        displayName: 'Late Night',
        icon: 'nights_stay',
        color: '#607D8B',
        defaultTime: '23:00',
        timeRange: { start: '22:00', end: '02:00' }
    }
};

export enum BloodSugarImpact {
    LOW = 'low',
    MODERATE = 'moderate',
    HIGH = 'high'
}

// Food Entry interface
export interface IFoodEntry extends Document {
    userId: mongoose.Types.ObjectId;
    name: string;
    carbohydrates: number; // grams
    calories: number; // kcal
    protein: number; // grams
    fat: number; // grams
    fiber: number; // grams
    glycemicIndex: GlycemicIndex;
    category: FoodCategory;
    portion: string; // e.g., "1 cup", "100g", "1 slice"
    portionSize: number; // numerical value for calculations
    unit: string; // e.g., "g", "ml", "pieces"
    mealType: MealType;
    customMealTimeId?: string; // Reference to custom meal time if mealType is 'custom'
    timestamp: Date;
    notes?: string;
    brand?: string;
    isCustom: boolean; // true if user-added, false if from database
    createdAt: Date;
    updatedAt: Date;

    // Virtual properties
    carbsPer100g: number;
    caloriesPer100g: number;
    displayName: string;
    portionDisplay: string;
    isDiabetesFriendly: boolean;
    bloodSugarImpact: BloodSugarImpact;
}

const foodEntrySchema = new Schema<IFoodEntry>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
        index: true
    },
    name: {
        type: String,
        required: [true, 'Food name is required'],
        trim: true,
        maxlength: [100, 'Food name cannot exceed 100 characters']
    },
    carbohydrates: {
        type: Number,
        required: [true, 'Carbohydrates value is required'],
        min: [0, 'Carbohydrates cannot be negative']
    },
    calories: {
        type: Number,
        required: [true, 'Calories value is required'],
        min: [0, 'Calories cannot be negative']
    },
    protein: {
        type: Number,
        required: [true, 'Protein value is required'],
        min: [0, 'Protein cannot be negative']
    },
    fat: {
        type: Number,
        required: [true, 'Fat value is required'],
        min: [0, 'Fat cannot be negative']
    },
    fiber: {
        type: Number,
        required: [true, 'Fiber value is required'],
        min: [0, 'Fiber cannot be negative']
    },
    glycemicIndex: {
        type: String,
        enum: Object.values(GlycemicIndex),
        required: [true, 'Glycemic index is required']
    },
    category: {
        type: String,
        enum: Object.values(FoodCategory),
        required: [true, 'Food category is required']
    },
    portion: {
        type: String,
        required: [true, 'Portion description is required'],
        trim: true,
        maxlength: [50, 'Portion description cannot exceed 50 characters']
    },
    portionSize: {
        type: Number,
        required: [true, 'Portion size is required'],
        min: [0.1, 'Portion size must be greater than 0']
    },
    unit: {
        type: String,
        required: [true, 'Unit is required'],
        trim: true,
        maxlength: [20, 'Unit cannot exceed 20 characters']
    },
    mealType: {
        type: String,
        enum: Object.values(MealType),
        required: [true, 'Meal type is required']
    },
    customMealTimeId: {
        type: String,
        required: false // Only required if mealType is 'custom'
    },
    timestamp: {
        type: Date,
        required: [true, 'Timestamp is required'],
        default: Date.now,
        index: true
    },
    notes: {
        type: String,
        maxlength: [500, 'Notes cannot exceed 500 characters'],
        trim: true
    },
    brand: {
        type: String,
        trim: true,
        maxlength: [50, 'Brand name cannot exceed 50 characters']
    },
    isCustom: {
        type: Boolean,
        default: false
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for performance
foodEntrySchema.index({ userId: 1, timestamp: -1 });
foodEntrySchema.index({ userId: 1, mealType: 1, timestamp: -1 });
foodEntrySchema.index({ userId: 1, category: 1 });
foodEntrySchema.index({ name: 'text', brand: 'text' });

// Virtual properties
foodEntrySchema.virtual('carbsPer100g').get(function(this: IFoodEntry) {
    if (this.portionSize === 0) return 0;
    return (this.carbohydrates / this.portionSize) * 100;
});

foodEntrySchema.virtual('caloriesPer100g').get(function(this: IFoodEntry) {
    if (this.portionSize === 0) return 0;
    return (this.calories / this.portionSize) * 100;
});

foodEntrySchema.virtual('displayName').get(function(this: IFoodEntry) {
    if (this.brand && this.brand.length > 0) {
        return `${this.brand} ${this.name}`;
    }
    return this.name;
});

foodEntrySchema.virtual('portionDisplay').get(function(this: IFoodEntry) {
    return `${this.portionSize}${this.unit} (${this.portion})`;
});

foodEntrySchema.virtual('isDiabetesFriendly').get(function(this: IFoodEntry) {
    return this.carbohydrates <= 15 && this.glycemicIndex === GlycemicIndex.LOW;
});

foodEntrySchema.virtual('bloodSugarImpact').get(function(this: IFoodEntry) {
    if (this.carbohydrates <= 15 && this.glycemicIndex === GlycemicIndex.LOW) {
        return BloodSugarImpact.LOW;
    } else if (this.carbohydrates <= 30 && this.glycemicIndex !== GlycemicIndex.HIGH) {
        return BloodSugarImpact.MODERATE;
    } else {
        return BloodSugarImpact.HIGH;
    }
});

// Static methods
foodEntrySchema.statics.findByMealType = function(userId: string, mealType: MealType, date?: Date) {
    const startOfDay = date ? new Date(date) : new Date();
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(startOfDay);
    endOfDay.setHours(23, 59, 59, 999);
    
    return this.find({
        userId,
        mealType,
        timestamp: { $gte: startOfDay, $lte: endOfDay }
    }).sort({ timestamp: -1 });
};

foodEntrySchema.statics.findByDateRange = function(userId: string, startDate: Date, endDate: Date) {
    return this.find({
        userId,
        timestamp: { $gte: startDate, $lte: endDate }
    }).sort({ timestamp: -1 });
};

const FoodEntry = mongoose.model<IFoodEntry>('FoodEntry', foodEntrySchema);

export default FoodEntry;
