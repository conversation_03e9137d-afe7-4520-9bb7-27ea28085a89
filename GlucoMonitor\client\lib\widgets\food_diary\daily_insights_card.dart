import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/food_diary_provider.dart';

class DailyInsightsCard extends StatelessWidget {
  const DailyInsightsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<FoodDiaryProvider>(
      builder: (context, provider, child) {
        final insights = provider.dailyInsights;
        
        // Don't show insights if there's no data
        if (provider.todaysFoodEntries.isEmpty) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with overall score
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Daily Insights',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    decoration: BoxDecoration(
                      color: _getScoreColor(insights.overallScore).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          _getScoreIcon(insights.overallScore),
                          size: 16,
                          color: _getScoreColor(insights.overallScore),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${insights.overallScore.toStringAsFixed(0)}/100',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: _getScoreColor(insights.overallScore),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),

              // Achievements
              if (insights.achievements.isNotEmpty) ...[
                _buildInsightSection(
                  'Achievements',
                  insights.achievements,
                  Colors.green,
                  Icons.check_circle,
                ),
                const SizedBox(height: 16),
              ],

              // Recommendations
              if (insights.recommendations.isNotEmpty) ...[
                _buildInsightSection(
                  'Recommendations',
                  insights.recommendations,
                  Colors.blue,
                  Icons.lightbulb,
                ),
                const SizedBox(height: 16),
              ],

              // Warnings
              if (insights.warnings.isNotEmpty) ...[
                _buildInsightSection(
                  'Warnings',
                  insights.warnings,
                  Colors.orange,
                  Icons.warning,
                ),
              ],

              // Quick diabetes tips
              if (insights.recommendations.isEmpty && 
                  insights.warnings.isEmpty && 
                  insights.achievements.isEmpty) ...[
                _buildDiabetesTips(),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildInsightSection(
    String title,
    List<String> items,
    Color color,
    IconData icon,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(icon, color: color, size: 18),
            const SizedBox(width: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: color,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...items.map((item) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 4,
                height: 4,
                margin: const EdgeInsets.only(top: 8, right: 8),
                decoration: BoxDecoration(
                  color: color,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: Text(
                  item,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.onSurface,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildDiabetesTips() {
    final tips = [
      'Focus on low-GI foods like vegetables, lean proteins, and whole grains',
      'Monitor portion sizes to help manage blood sugar levels',
      'Include fiber-rich foods to slow glucose absorption',
      'Stay hydrated with water instead of sugary drinks',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.tips_and_updates, color: AppColors.primary, size: 18),
            const SizedBox(width: 8),
            Text(
              'Diabetes Management Tips',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppColors.primary,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        ...tips.map((tip) => Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 4,
                height: 4,
                margin: const EdgeInsets.only(top: 8, right: 8),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: Text(
                  tip,
                  style: const TextStyle(
                    fontSize: 14,
                    color: AppColors.onSurface,
                    height: 1.4,
                  ),
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Color _getScoreColor(double score) {
    if (score >= 80) return Colors.green;
    if (score >= 60) return Colors.orange;
    return Colors.red;
  }

  IconData _getScoreIcon(double score) {
    if (score >= 80) return Icons.sentiment_very_satisfied;
    if (score >= 60) return Icons.sentiment_neutral;
    return Icons.sentiment_dissatisfied;
  }
}
