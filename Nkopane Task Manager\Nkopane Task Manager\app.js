// Task Management
let tasks = JSON.parse(localStorage.getItem('tasks')) || [];

const taskForm = document.getElementById('taskForm');
const taskInput = document.getElementById('taskInput');
const taskList = document.getElementById('taskList');
const startDateInput = document.getElementById('startDate');
const dueDateInput = document.getElementById('dueDate');

// Format date for display
const formatDate = (dateString) => {
    const options = { year: 'numeric', month: 'short', day: 'numeric' };
    return new Date(dateString).toLocaleDateString(undefined, options);
};

// Save tasks to localStorage
const saveTasks = () => {
    localStorage.setItem('tasks', JSON.stringify(tasks));
};

// Render tasks
const renderTasks = () => {
    taskList.innerHTML = '';
    tasks.forEach((task, index) => {
        const taskElement = document.createElement('div');
        taskElement.className = 'flex items-center justify-between p-4 bg-gray-50 rounded-lg';
        
        // Calculate if task is overdue
        const isOverdue = new Date(task.dueDate) < new Date() && !task.completed;
        const dateColor = isOverdue ? 'text-red-600' : 'text-gray-600';
        
        taskElement.innerHTML = `
            <div class="flex items-center gap-4 flex-1">
                <input type="checkbox" ${task.completed ? 'checked' : ''} 
                    class="w-5 h-5 rounded border-gray-300 focus:ring-blue-500"
                    onchange="toggleTask(${index})">
                <div class="flex flex-col flex-1">
                    <span class="${task.completed ? 'line-through text-gray-500' : 'text-gray-800'}">
                        ${task.text}
                    </span>
                    <div class="text-sm ${dateColor}">
                        <span title="Start Date">
                            <i class="fas fa-calendar-plus"></i> ${formatDate(task.startDate)}
                        </span>
                        <span class="mx-2">→</span>
                        <span title="Due Date">
                            <i class="fas fa-calendar-check"></i> ${formatDate(task.dueDate)}
                        </span>
                        ${isOverdue ? '<span class="text-red-600 ml-2">(Overdue)</span>' : ''}
                    </div>
                </div>
            </div>
            <div class="flex gap-2">
                <button onclick="editTask(${index})" 
                    class="text-blue-500 hover:text-blue-600">
                    <i class="fas fa-edit"></i>
                </button>
                <button onclick="deleteTask(${index})" 
                    class="text-red-500 hover:text-red-600">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        taskList.appendChild(taskElement);
    });
};

// Add task
taskForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const text = taskInput.value.trim();
    const startDate = startDateInput.value;
    const dueDate = dueDateInput.value;
    
    if (text && startDate && dueDate) {
        if (new Date(startDate) > new Date(dueDate)) {
            alert('Start date cannot be after due date!');
            return;
        }
        
        tasks.push({ 
            text, 
            completed: false,
            startDate,
            dueDate
        });
        saveTasks();
        renderTasks();
        
        // Reset form
        taskInput.value = '';
        startDateInput.value = '';
        dueDateInput.value = '';
    }
});

// Toggle task completion
const toggleTask = (index) => {
    tasks[index].completed = !tasks[index].completed;
    saveTasks();
    renderTasks();
};

// Edit task
const editTask = (index) => {
    const task = tasks[index];
    const newText = prompt('Edit task:', task.text);
    const newStartDate = prompt('Edit start date (YYYY-MM-DD):', task.startDate);
    const newDueDate = prompt('Edit due date (YYYY-MM-DD):', task.dueDate);
    
    if (newText !== null && newStartDate !== null && newDueDate !== null) {
        if (newText.trim() !== '' && newStartDate.trim() !== '' && newDueDate.trim() !== '') {
            if (new Date(newStartDate) > new Date(newDueDate)) {
                alert('Start date cannot be after due date!');
                return;
            }
            
            tasks[index] = {
                ...task,
                text: newText.trim(),
                startDate: newStartDate.trim(),
                dueDate: newDueDate.trim()
            };
            saveTasks();
            renderTasks();
        }
    }
};

// Delete task
const deleteTask = (index) => {
    if (confirm('Are you sure you want to delete this task?')) {
        tasks.splice(index, 1);
        saveTasks();
        renderTasks();
    }
};

// Set min date for date inputs to today
const today = new Date().toISOString().split('T')[0];
startDateInput.min = today;
dueDateInput.min = today;

// Pomodoro Timer
const timerDisplay = document.getElementById('timer');
const startButton = document.getElementById('startTimer');
const pauseButton = document.getElementById('pauseTimer');
const resetButton = document.getElementById('resetTimer');
const timer25Button = document.getElementById('timer25');
const timer50Button = document.getElementById('timer50');
const timer75Button = document.getElementById('timer75');

let timeLeft = 25 * 60; // Default to 25 minutes in seconds
let timerId = null;
let isRunning = false;
let selectedTime = 25; // Default selected time in minutes

// Format time as MM:SS
const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// Update timer display
const updateDisplay = () => {
    timerDisplay.textContent = formatTime(timeLeft);
};

// Set active timer button
const setActiveTimer = (activeButton, minutes) => {
    // Remove active class from all buttons
    [timer25Button, timer50Button, timer75Button].forEach(btn => {
        btn.className = btn.className.replace('bg-blue-500 text-white', 'bg-blue-100 text-blue-800');
    });
    
    // Add active class to selected button
    activeButton.className = activeButton.className.replace('bg-blue-100 text-blue-800', 'bg-blue-500 text-white');
    
    // Update timer
    selectedTime = minutes;
    timeLeft = minutes * 60;
    updateDisplay();
    
    // Reset timer state
    if (isRunning) {
        clearInterval(timerId);
        isRunning = false;
    }
};

// Start timer
const startTimer = () => {
    if (!isRunning) {
        isRunning = true;
        timerId = setInterval(() => {
            timeLeft--;
            updateDisplay();
            if (timeLeft === 0) {
                clearInterval(timerId);
                isRunning = false;
                // Play notification sound
                const audio = new Audio('https://actions.google.com/sounds/v1/alarms/beep_short.ogg');
                audio.play();
                // Show notification
                alert('Pomodoro session completed!');
                timeLeft = selectedTime * 60;
                updateDisplay();
            }
        }, 1000);
    }
};

// Pause timer
const pauseTimer = () => {
    clearInterval(timerId);
    isRunning = false;
};

// Reset timer
const resetTimer = () => {
    clearInterval(timerId);
    isRunning = false;
    timeLeft = selectedTime * 60;
    updateDisplay();
};

// Event listeners for timer buttons
startButton.addEventListener('click', startTimer);
pauseButton.addEventListener('click', pauseTimer);
resetButton.addEventListener('click', resetTimer);

// Event listeners for timer options
timer25Button.addEventListener('click', () => setActiveTimer(timer25Button, 25));
timer50Button.addEventListener('click', () => setActiveTimer(timer50Button, 50));
timer75Button.addEventListener('click', () => setActiveTimer(timer75Button, 75));

// Initial render
renderTasks();
updateDisplay(); 