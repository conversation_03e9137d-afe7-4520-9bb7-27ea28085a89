// Debug script to run the backend with better error handling
process.on('uncaughtException', (error) => {
  console.error('❌ Uncaught Exception:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

console.log('🔍 Starting backend with enhanced error handling...');

try {
  console.log('📁 Loading backend from:', __dirname + '/backend/dist/index.js');

  // Load the backend
  require('./backend/dist/index.js');
} catch (error) {
  console.error('❌ Failed to start backend:', error);
  console.error('Stack:', error.stack);
  process.exit(1);
}
