import express from 'express';
const router = express.Router();
import { protect } from '../middleware/auth';
import {
    startPhoneVerification,
    verifyOTP,
    updateProfile,
    updateProfileLegacy,
    getMe,
    clearRateLimit,
    register,
    login,
    forgotPassword,
    resetPassword
} from '../controllers/auth';

// Development only route
if (process.env.NODE_ENV === 'development') {
    router.post('/clear-rate-limit', clearRateLimit);
}

// New email/password authentication routes
router.post('/register', register);
router.post('/login', login);

// Password reset routes
router.post('/forgot-password', forgotPassword);
router.post('/reset-password', resetPassword);

// Protected routes (require authentication)
router.put('/profile', protect, updateProfile);
router.get('/me', protect, getMe);

// Legacy phone-based authentication routes (still needed for phone verification)
router.post('/verify-phone', startPhoneVerification);
router.post('/verify-otp', verifyOTP);
router.post('/update-profile', updateProfileLegacy);

export default router;
