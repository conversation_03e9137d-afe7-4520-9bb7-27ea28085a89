import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';

/// Interactive tutorial for AI food recognition training
class AITrainingTutorialScreen extends StatefulWidget {
  const AITrainingTutorialScreen({super.key});

  @override
  State<AITrainingTutorialScreen> createState() =>
      _AITrainingTutorialScreenState();
}

class _AITrainingTutorialScreenState extends State<AITrainingTutorialScreen>
    with SingleTickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  int _currentPage = 0;
  final int _totalPages = 6;

  final List<TutorialStep> _tutorialSteps = [
    TutorialStep(
      title: 'Welcome to AI Food Training',
      description:
          'Learn how to train the AI to recognize your specific dishes and improve accuracy for your favorite foods.',
      icon: Icons.school,
      color: Colors.blue,
      content:
          'AI Food Training allows you to teach the recognition system about your homemade dishes, family recipes, and frequently eaten foods.',
    ),
    TutorialStep(
      title: 'Why Train Custom Foods?',
      description:
          'Custom training dramatically improves recognition accuracy for your specific dishes.',
      icon: Icons.trending_up,
      color: Colors.green,
      content:
          'Standard AI models may not recognize your grandmother\'s special recipe or your unique cooking style. Training helps the AI learn your specific foods.',
    ),
    TutorialStep(
      title: 'Taking Training Photos',
      description:
          'Learn the best practices for capturing effective training images.',
      icon: Icons.camera_alt,
      color: Colors.orange,
      content:
          'Take 3-5 photos from different angles, in good lighting, with the food clearly visible. Include different portions and presentations.',
    ),
    TutorialStep(
      title: 'Adding Nutrition Information',
      description: 'Provide accurate nutrition data for your custom foods.',
      icon: Icons.restaurant,
      color: Colors.purple,
      content:
          'Add calories, carbohydrates, protein, fat, and fiber content. This helps with accurate nutrition tracking when the food is recognized.',
    ),
    TutorialStep(
      title: 'AI Learning Process',
      description: 'Understand how the AI learns from your training examples.',
      icon: Icons.psychology,
      color: Colors.teal,
      content:
          'The AI analyzes color patterns, textures, and shapes from your photos to create a unique recognition model for your food.',
    ),
    TutorialStep(
      title: 'Improving Over Time',
      description:
          'Your custom models get better with more examples and feedback.',
      icon: Icons.auto_awesome,
      color: Colors.indigo,
      content:
          'Add more photos over time and provide feedback when the AI makes mistakes. This continuously improves recognition accuracy.',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('AI Training Tutorial'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _skipTutorial,
            child: const Text('Skip', style: TextStyle(color: Colors.white)),
          ),
        ],
      ),
      body: Column(
        children: [
          // Progress indicator
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: LinearProgressIndicator(
                    value: (_currentPage + 1) / _totalPages,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      AppColors.primary,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  '${_currentPage + 1} of $_totalPages',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          // Tutorial content
          Expanded(
            child: PageView.builder(
              controller: _pageController,
              onPageChanged: (index) {
                setState(() {
                  _currentPage = index;
                });
                _animationController.reset();
                _animationController.forward();
              },
              itemCount: _tutorialSteps.length,
              itemBuilder: (context, index) {
                return FadeTransition(
                  opacity: _fadeAnimation,
                  child: _buildTutorialPage(_tutorialSteps[index]),
                );
              },
            ),
          ),

          // Navigation buttons
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                if (_currentPage > 0)
                  Expanded(
                    child: OutlinedButton(
                      onPressed: _previousPage,
                      style: OutlinedButton.styleFrom(
                        side: const BorderSide(color: AppColors.primary),
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'Previous',
                        style: TextStyle(color: AppColors.primary),
                      ),
                    ),
                  ),
                if (_currentPage > 0) const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed:
                        _currentPage == _totalPages - 1
                            ? _completeTutorial
                            : _nextPage,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                    child: Text(
                      _currentPage == _totalPages - 1
                          ? 'Start Training'
                          : 'Next',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTutorialPage(TutorialStep step) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: step.color.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(step.icon, size: 60, color: step.color),
          ),

          const SizedBox(height: 32),

          // Title
          Text(
            step.title,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Description
          Text(
            step.description,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Content
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  _buildStepContent(step),
                  const SizedBox(height: 24),
                  _buildStepTips(step),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(TutorialStep step) {
    switch (_currentPage) {
      case 0:
        return _buildWelcomeContent();
      case 1:
        return _buildBenefitsContent();
      case 2:
        return _buildPhotoTipsContent();
      case 3:
        return _buildNutritionContent();
      case 4:
        return _buildLearningContent();
      case 5:
        return _buildImprovementContent();
      default:
        return Container();
    }
  }

  Widget _buildWelcomeContent() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        children: [
          const Icon(Icons.lightbulb, color: Colors.blue, size: 32),
          const SizedBox(height: 12),
          const Text(
            'Did you know?',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Custom trained foods have up to 95% recognition accuracy compared to 70% for general foods.',
            style: TextStyle(fontSize: 14, color: Colors.grey[700]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildBenefitsContent() {
    final benefits = [
      'Higher recognition accuracy',
      'Faster food logging',
      'Better nutrition tracking',
      'Personalized experience',
    ];

    return Column(
      children:
          benefits
              .map(
                (benefit) => Padding(
                  padding: const EdgeInsets.only(bottom: 12),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.check_circle,
                        color: Colors.green,
                        size: 20,
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          benefit,
                          style: const TextStyle(fontSize: 16),
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
    );
  }

  Widget _buildPhotoTipsContent() {
    return Column(
      children: [
        _buildPhotoTip(
          'Good Lighting',
          'Natural light works best',
          Icons.wb_sunny,
          Colors.orange,
        ),
        _buildPhotoTip(
          'Clear Focus',
          'Food should be sharp and visible',
          Icons.center_focus_strong,
          Colors.blue,
        ),
        _buildPhotoTip(
          'Multiple Angles',
          'Take photos from different perspectives',
          Icons.rotate_right,
          Colors.green,
        ),
        _buildPhotoTip(
          'Fill Frame',
          'Food should occupy most of the image',
          Icons.crop_free,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildPhotoTip(
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                Text(description, style: const TextStyle(fontSize: 12)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNutritionContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.purple.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.purple.withValues(alpha: 0.2)),
      ),
      child: const Column(
        children: [
          Text(
            'Nutrition Information Required:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.purple,
            ),
          ),
          SizedBox(height: 12),
          Text(
            '• Calories per 100g\n'
            '• Carbohydrates (g)\n'
            '• Protein (g)\n'
            '• Fat (g)\n'
            '• Fiber (g)\n'
            '• Glycemic Index (Low/Medium/High)',
            style: TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  Widget _buildLearningContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.teal.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.teal.withValues(alpha: 0.2)),
      ),
      child: const Column(
        children: [
          Icon(Icons.psychology, color: Colors.teal, size: 40),
          SizedBox(height: 12),
          Text(
            'AI Learning Process',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.teal,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'The AI analyzes your photos to learn:\n'
            '• Color patterns and variations\n'
            '• Texture characteristics\n'
            '• Shape and size features\n'
            '• Visual context clues',
            style: TextStyle(fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildImprovementContent() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.indigo.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.indigo.withValues(alpha: 0.2)),
      ),
      child: const Column(
        children: [
          Icon(Icons.auto_awesome, color: Colors.indigo, size: 40),
          SizedBox(height: 12),
          Text(
            'Continuous Improvement',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: Colors.indigo,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Your AI gets smarter over time:\n'
            '• Add more training photos\n'
            '• Provide feedback on recognition\n'
            '• Correct mistakes when they happen\n'
            '• Watch accuracy improve!',
            style: TextStyle(fontSize: 14),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildStepTips(TutorialStep step) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Icon(Icons.tips_and_updates, color: Colors.amber[700], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              step.content,
              style: const TextStyle(fontSize: 12, fontStyle: FontStyle.italic),
            ),
          ),
        ],
      ),
    );
  }

  void _nextPage() {
    if (_currentPage < _totalPages - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _skipTutorial() {
    Navigator.of(context).pop();
  }

  void _completeTutorial() {
    Navigator.of(context).pop();
    // Navigate to custom food training screen
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Ready to start training your first custom food!'),
        backgroundColor: Colors.green,
      ),
    );
  }
}

class TutorialStep {
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final String content;

  const TutorialStep({
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.content,
  });
}
