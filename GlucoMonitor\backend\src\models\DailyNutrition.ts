import mongoose, { Document, Schema } from 'mongoose';
import { MealType } from './FoodEntry';

// Nutrition Goals interface
export interface INutritionGoals {
    dailyCalories: number;
    dailyCarbohydrates: number; // grams
    dailyProtein: number; // grams
    dailyFat: number; // grams
    dailyFiber: number; // grams
    maxCarbsPerMeal: number; // grams
    targetBloodSugarRange: {
        min: number; // mg/dL
        max: number; // mg/dL
    };
}

// Blood Sugar Reading interface (simplified for correlation)
export interface IBloodSugarReading {
    value: number; // mg/dL
    timestamp: Date;
    mealTiming?: string;
    relatedMealType?: MealType;
}

// Meal Nutrition Summary interface
export interface IMealNutrition {
    mealType: MealType;
    totalCarbs: number;
    totalCalories: number;
    totalProtein: number;
    totalFat: number;
    totalFiber: number;
    entryCount: number;
    averageGlycemicIndex: number; // calculated weighted average
    bloodSugarImpactScore: number; // 1-10 scale
}

// Daily Nutrition interface
export interface IDailyNutrition extends Document {
    userId: mongoose.Types.ObjectId;
    date: Date;
    goals: INutritionGoals;
    totalCalories: number;
    totalCarbohydrates: number;
    totalProtein: number;
    totalFat: number;
    totalFiber: number;
    mealBreakdown: Map<MealType, IMealNutrition>;
    bloodSugarReadings: IBloodSugarReading[];
    averageBloodSugarChange?: number; // mg/dL
    adherenceScore: number; // 0-100 percentage
    notes?: string;
    createdAt: Date;
    updatedAt: Date;

    // Virtual properties
    caloriesProgress: number;
    carbsProgress: number;
    proteinProgress: number;
    fatProgress: number;
    fiberProgress: number;
    isWithinTargets: boolean;
    nutritionGrade: string; // A, B, C, D, F
}

const nutritionGoalsSchema = new Schema<INutritionGoals>({
    dailyCalories: {
        type: Number,
        required: true,
        min: [800, 'Daily calories must be at least 800'],
        max: [5000, 'Daily calories cannot exceed 5000']
    },
    dailyCarbohydrates: {
        type: Number,
        required: true,
        min: [50, 'Daily carbohydrates must be at least 50g'],
        max: [500, 'Daily carbohydrates cannot exceed 500g']
    },
    dailyProtein: {
        type: Number,
        required: true,
        min: [30, 'Daily protein must be at least 30g'],
        max: [300, 'Daily protein cannot exceed 300g']
    },
    dailyFat: {
        type: Number,
        required: true,
        min: [20, 'Daily fat must be at least 20g'],
        max: [200, 'Daily fat cannot exceed 200g']
    },
    dailyFiber: {
        type: Number,
        required: true,
        min: [15, 'Daily fiber must be at least 15g'],
        max: [100, 'Daily fiber cannot exceed 100g']
    },
    maxCarbsPerMeal: {
        type: Number,
        required: true,
        min: [15, 'Max carbs per meal must be at least 15g'],
        max: [100, 'Max carbs per meal cannot exceed 100g']
    },
    targetBloodSugarRange: {
        min: {
            type: Number,
            required: true,
            min: [70, 'Minimum blood sugar target must be at least 70 mg/dL'],
            max: [120, 'Minimum blood sugar target cannot exceed 120 mg/dL']
        },
        max: {
            type: Number,
            required: true,
            min: [120, 'Maximum blood sugar target must be at least 120 mg/dL'],
            max: [200, 'Maximum blood sugar target cannot exceed 200 mg/dL']
        }
    }
}, { _id: false });

const bloodSugarReadingSchema = new Schema<IBloodSugarReading>({
    value: {
        type: Number,
        required: true,
        min: [20, 'Blood sugar value must be at least 20 mg/dL'],
        max: [600, 'Blood sugar value cannot exceed 600 mg/dL']
    },
    timestamp: {
        type: Date,
        required: true
    },
    mealTiming: {
        type: String,
        enum: ['before_breakfast', 'after_breakfast', 'before_lunch', 'after_lunch', 'before_dinner', 'after_dinner', 'bedtime', 'other']
    },
    relatedMealType: {
        type: String,
        enum: Object.values(MealType)
    }
}, { _id: false });

const mealNutritionSchema = new Schema<IMealNutrition>({
    mealType: {
        type: String,
        enum: Object.values(MealType),
        required: true
    },
    totalCarbs: {
        type: Number,
        default: 0,
        min: 0
    },
    totalCalories: {
        type: Number,
        default: 0,
        min: 0
    },
    totalProtein: {
        type: Number,
        default: 0,
        min: 0
    },
    totalFat: {
        type: Number,
        default: 0,
        min: 0
    },
    totalFiber: {
        type: Number,
        default: 0,
        min: 0
    },
    entryCount: {
        type: Number,
        default: 0,
        min: 0
    },
    averageGlycemicIndex: {
        type: Number,
        default: 0,
        min: 0,
        max: 3
    },
    bloodSugarImpactScore: {
        type: Number,
        default: 0,
        min: 0,
        max: 10
    }
}, { _id: false });

const dailyNutritionSchema = new Schema<IDailyNutrition>({
    userId: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'User ID is required'],
        index: true
    },
    date: {
        type: Date,
        required: [true, 'Date is required'],
        index: true
    },
    goals: {
        type: nutritionGoalsSchema,
        required: true
    },
    totalCalories: {
        type: Number,
        default: 0,
        min: 0
    },
    totalCarbohydrates: {
        type: Number,
        default: 0,
        min: 0
    },
    totalProtein: {
        type: Number,
        default: 0,
        min: 0
    },
    totalFat: {
        type: Number,
        default: 0,
        min: 0
    },
    totalFiber: {
        type: Number,
        default: 0,
        min: 0
    },
    mealBreakdown: {
        type: Map,
        of: mealNutritionSchema,
        default: new Map()
    },
    bloodSugarReadings: [bloodSugarReadingSchema],
    averageBloodSugarChange: {
        type: Number
    },
    adherenceScore: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot exceed 1000 characters'],
        trim: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Compound indexes for performance
dailyNutritionSchema.index({ userId: 1, date: -1 }, { unique: true });
dailyNutritionSchema.index({ userId: 1, 'goals.targetBloodSugarRange': 1 });

// Virtual properties
dailyNutritionSchema.virtual('caloriesProgress').get(function(this: IDailyNutrition) {
    return Math.round((this.totalCalories / this.goals.dailyCalories) * 100);
});

dailyNutritionSchema.virtual('carbsProgress').get(function(this: IDailyNutrition) {
    return Math.round((this.totalCarbohydrates / this.goals.dailyCarbohydrates) * 100);
});

dailyNutritionSchema.virtual('proteinProgress').get(function(this: IDailyNutrition) {
    return Math.round((this.totalProtein / this.goals.dailyProtein) * 100);
});

dailyNutritionSchema.virtual('fatProgress').get(function(this: IDailyNutrition) {
    return Math.round((this.totalFat / this.goals.dailyFat) * 100);
});

dailyNutritionSchema.virtual('fiberProgress').get(function(this: IDailyNutrition) {
    return Math.round((this.totalFiber / this.goals.dailyFiber) * 100);
});

dailyNutritionSchema.virtual('isWithinTargets').get(function(this: IDailyNutrition) {
    const caloriesOk = this.totalCalories <= this.goals.dailyCalories * 1.1; // 10% tolerance
    const carbsOk = this.totalCarbohydrates <= this.goals.dailyCarbohydrates * 1.1;
    return caloriesOk && carbsOk;
});

dailyNutritionSchema.virtual('nutritionGrade').get(function(this: IDailyNutrition) {
    if (this.adherenceScore >= 90) return 'A';
    if (this.adherenceScore >= 80) return 'B';
    if (this.adherenceScore >= 70) return 'C';
    if (this.adherenceScore >= 60) return 'D';
    return 'F';
});

// Static methods
dailyNutritionSchema.statics.findByDateRange = function(userId: string, startDate: Date, endDate: Date) {
    return this.find({
        userId,
        date: { $gte: startDate, $lte: endDate }
    }).sort({ date: -1 });
};

dailyNutritionSchema.statics.getDefaultGoals = function(): INutritionGoals {
    return {
        dailyCalories: 2000,
        dailyCarbohydrates: 150,
        dailyProtein: 75,
        dailyFat: 65,
        dailyFiber: 25,
        maxCarbsPerMeal: 45,
        targetBloodSugarRange: {
            min: 80,
            max: 180
        }
    };
};

const DailyNutrition = mongoose.model<IDailyNutrition>('DailyNutrition', dailyNutritionSchema);

export default DailyNutrition;
