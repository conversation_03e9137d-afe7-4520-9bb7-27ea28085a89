import axios from 'axios';
import { createClient } from 'redis';

// Configure Redis client to connect to Docker container
const redisClient = createClient({
    url: process.env.REDIS_URL || 'redis://localhost:6379',
    socket: {
        reconnectStrategy: false // Disable automatic reconnection
    }
});

// Handle Redis connection events
redisClient.on('connect', () => console.log('Redis client connected'));
redisClient.on('error', (err: Error) => {
    console.warn('Redis not available:', err.message);
    isRedisConnected = false;
});

// Track Redis connection status
let isRedisConnected = false;

// In-memory fallback for OTP storage when Redis is not available
const inMemoryOTPStore: { [key: string]: { otp: string; expiry: number } } = {};

// In-memory fallback for rate limiting when Redis is not available
const inMemoryRateLimitStore: { [key: string]: { attempts: number; expiry: number } } = {};

// Try to connect to Redis, but don't fail if it's not available
redisClient.connect()
    .then(() => {
        isRedisConnected = true;
        console.log('Redis connected successfully');
    })
    .catch((err) => {
        console.warn('Redis not available, using in-memory fallback:', err.message);
        isRedisConnected = false;
    });

// Store OTP in Redis or in-memory fallback
const storeOTP = async (phoneNumber: string, otp: string): Promise<boolean> => {
    try {
        if (isRedisConnected) {
            // Store OTP with 10 minute expiry in Redis
            await redisClient.set(`otp_${phoneNumber}`, otp, {
                EX: 600 // 10 minutes in seconds
            });
            console.log(`OTP stored in Redis for ${phoneNumber}`);
        } else {
            // Use in-memory fallback
            const expiry = Date.now() + (10 * 60 * 1000); // 10 minutes from now
            inMemoryOTPStore[`otp_${phoneNumber}`] = { otp, expiry };
            console.log(`OTP stored in memory for ${phoneNumber}`);
        }
        return true;
    } catch (error) {
        console.error('OTP storage error:', error);
        return false;
    }
};

// Verify OTP from Redis or in-memory fallback
const verifyOTP = async (phoneNumber: string, enteredOtp: string): Promise<boolean> => {
    try {
        console.log(`Verifying OTP for ${phoneNumber}:`, enteredOtp);

        let storedOTP: string | null = null;

        if (isRedisConnected) {
            // Fetch from Redis
            const redisValue = await redisClient.get(`otp_${phoneNumber}`);
            storedOTP = redisValue ? redisValue.toString() : null;
            console.log('Stored OTP in Redis:', storedOTP);
        } else {
            // Fetch from in-memory store
            const otpData = inMemoryOTPStore[`otp_${phoneNumber}`];
            if (otpData && otpData.expiry > Date.now()) {
                storedOTP = otpData.otp;
                console.log('Stored OTP in memory:', storedOTP);
            } else if (otpData) {
                // OTP expired, remove it
                delete inMemoryOTPStore[`otp_${phoneNumber}`];
                console.log('OTP expired in memory');
            }
        }

        if (!storedOTP) {
            console.log('No OTP found or OTP expired');
            return false; // OTP expired or not found
        }

        // Normalize both OTPs by removing any whitespace and quotes
        const normalizedStoredOTP = storedOTP.replace(/["\s]/g, '');
        const normalizedEnteredOTP = enteredOtp.toString().replace(/["\s]/g, '');

        console.log('OTP Comparison:', {
            normalizedStoredOTP,
            normalizedEnteredOTP,
            match: normalizedStoredOTP === normalizedEnteredOTP
        });

        const isValid = normalizedStoredOTP === normalizedEnteredOTP;

        if (isValid) {
            // Delete OTP after successful verification
            if (isRedisConnected) {
                console.log('Valid OTP - deleting from Redis');
                await redisClient.del(`otp_${phoneNumber}`);
            } else {
                console.log('Valid OTP - deleting from memory');
                delete inMemoryOTPStore[`otp_${phoneNumber}`];
            }
        } else {
            console.log('Invalid OTP comparison');
        }

        return isValid;
    } catch (error) {
        console.error('OTP verification error:', error);
        return false;
    }
};

// Generate 6-digit OTP
const generateOTP = (): string => {
    return Math.floor(100000 + Math.random() * 900000).toString();
};

// Language type
type Language = 'en' | 'af' | 'zu';

// Send OTP via SMS using SMSPortal
const sendOTP = async (phoneNumber: string, otp: string, language: Language = 'en'): Promise<boolean> => {
    try {
        console.log('sendOTP called with:', { phoneNumber, language });
        console.log('Environment:', process.env.NODE_ENV);
        
        if (!process.env.SMSPORTAL_CLIENT_ID || !process.env.SMSPORTAL_API_SECRET || !process.env.SMSPORTAL_SENDER) {
            console.error('SMSPortal configuration missing:', {
                hasClientId: !!process.env.SMSPORTAL_CLIENT_ID,
                hasApiKey: !!process.env.SMSPORTAL_API_SECRET,
                hasSender: !!process.env.SMSPORTAL_SENDER
            });
            throw new Error('SMS service not properly configured');
        }

        // For development/testing, simulate SMS sending for all numbers
        if (process.env.NODE_ENV === 'development') {
            console.log('Development mode: Simulating successful SMS send for all numbers');
            console.log('Development code:', otp);
            // For OTP verification, use fixed code; for password reset, use the actual token
            const isPasswordReset = otp.length > 6; // Password reset tokens are longer
            if (isPasswordReset) {
                console.log('Password reset token - using actual token:', otp);
                await storeOTP(phoneNumber, otp);
            } else {
                // Regular OTP - use fixed code for easier testing
                const devOTP = '123456';
                console.log('Development Fixed OTP for testing:', devOTP);
                await storeOTP(phoneNumber, devOTP);
            }
            return true;
        }

        console.log(`Attempting to send OTP to ${phoneNumber}`);
        
        // Determine if this is a password reset token (longer than typical OTP)
        const isPasswordReset = otp.length > 6;

        // Get message template based on language and message type
        const messages = isPasswordReset ? {
            en: `Your GlucoMonitor password reset code is: ${otp}. Valid for 10 minutes.`,
            af: `Jou GlucoMonitor wagwoord herstel kode is: ${otp}. Geldig vir 10 minute.`,
            zu: `Ikhodi yakho yokubuyisela iphasiwedi ye-GlucoMonitor ithi: ${otp}. Isebenza imizuzu engu-10.`,
        } : {
            en: `Your GlucoMonitor verification code is: ${otp}. Valid for 10 minutes.`,
            af: `Jou GlucoMonitor verifikasiekode is: ${otp}. Geldig vir 10 minute.`,
            zu: `Ikhodi yakho yokuqinisekisa ye-GlucoMonitor ithi: ${otp}. Isebenza imizuzu engu-10.`,
        };

        const message = messages[language] || messages.en;
        console.log(`Sending ${isPasswordReset ? 'password reset' : 'verification'} message via SMSPortal:`, {
            to: phoneNumber,
            messageLength: message.length
        });

        try {
            // Create auth token for SMSPortal
            const authToken = Buffer.from(`${process.env.SMSPORTAL_CLIENT_ID}:${process.env.SMSPORTAL_API_SECRET}`).toString('base64');
            
            const response = await axios.post('https://rest.smsportal.com/v1/bulkmessages', {
                messages: [{
                    content: message,
                    destination: phoneNumber.replace('+', ''), // Remove + for SMSPortal format
                    sender: process.env.SMSPORTAL_SENDER
                }]
            }, {
                headers: {
                    'Authorization': `Basic ${authToken}`,
                    'Content-Type': 'application/json'
                }
            });
            
            console.log('SMSPortal response:', response.data);
            
            // In development mode, even if we get a trial mode response, store the OTP
            if (process.env.NODE_ENV === 'development' && 
                (response.data?.message?.includes('Trial Mode') || response.data?.error?.includes('Trial Mode'))) {
                console.log('Trial mode response detected, storing OTP for testing');
                console.log('OTP for testing:', otp);
                await storeOTP(phoneNumber, otp);
                return true;
            }
            
            if (response.data.status === 'ok') {
                await storeOTP(phoneNumber, otp);
                return true;
            } else {
                throw new Error(response.data.message || 'Failed to send SMS');
            }
        } catch (apiError: any) {
            console.error('SMSPortal Error:', {
                status: apiError.response?.status,
                data: apiError.response?.data,
                message: apiError.message
            });
            // In development, if we get any error, still store the OTP for testing
            if (process.env.NODE_ENV === 'development') {
                console.log('Development mode: Storing OTP despite API error');
                console.log('OTP for testing:', otp);
                await storeOTP(phoneNumber, otp);
                return true;
            }
            throw apiError;
        }
    } catch (error: any) {
        console.error('SMS Error Details:', {
            name: error.name,
            message: error.message,
            stack: error.stack
        });
        return false;
    }
};

// Rate limiter config
const checkPhoneVerificationLimit = async (phoneNumber: string): Promise<boolean> => {
    try {
        const key = `otp_limit_${phoneNumber}`;
        // More lenient limit in development mode
        const limit = process.env.NODE_ENV === 'development' ? 20 : 5;

        let attempts = 0;

        if (isRedisConnected) {
            // Use Redis for rate limiting
            const value = await redisClient.get(key);

            if (!value) {
                await redisClient.set(key, '1', {
                    EX: 3600 // 1 hour expiry
                });
                console.log(`New rate limit started for ${phoneNumber}: 1/${limit}`);
                return true;
            }

            attempts = parseInt(value.toString());
            if (attempts >= limit) {
                console.log(`Rate limit exceeded for ${phoneNumber}: ${attempts}/${limit}`);
                // In development, allow resetting the limit for testing
                if (process.env.NODE_ENV === 'development' &&
                    (phoneNumber === '+27123456789' ||
                    phoneNumber === '+27987654321' ||
                    phoneNumber.startsWith('+2711'))) {
                    await redisClient.del(key);
                    console.log(`Rate limit reset for test number ${phoneNumber}`);
                    return true;
                }
                return false;
            }

            // Increment attempt counter
            await redisClient.incr(key);
            console.log(`Rate limit incremented for ${phoneNumber}: ${attempts + 1}/${limit}`);
            return true;
        } else {
            // Use in-memory fallback for rate limiting
            const rateLimitData = inMemoryRateLimitStore[key];
            const now = Date.now();

            if (!rateLimitData || rateLimitData.expiry < now) {
                // Create new rate limit entry
                inMemoryRateLimitStore[key] = {
                    attempts: 1,
                    expiry: now + (3600 * 1000) // 1 hour from now
                };
                console.log(`New rate limit started for ${phoneNumber}: 1/${limit} (memory)`);
                return true;
            }

            attempts = rateLimitData.attempts;
            if (attempts >= limit) {
                console.log(`Rate limit exceeded for ${phoneNumber}: ${attempts}/${limit} (memory)`);
                // In development, allow resetting the limit for testing
                if (process.env.NODE_ENV === 'development' &&
                    (phoneNumber === '+27123456789' ||
                    phoneNumber === '+27987654321' ||
                    phoneNumber.startsWith('+2711'))) {
                    delete inMemoryRateLimitStore[key];
                    console.log(`Rate limit reset for test number ${phoneNumber} (memory)`);
                    return true;
                }
                return false;
            }

            // Increment attempt counter
            rateLimitData.attempts++;
            console.log(`Rate limit incremented for ${phoneNumber}: ${rateLimitData.attempts}/${limit} (memory)`);
            return true;
        }
    } catch (error) {
        console.error('Rate limit check error:', error);
        // Allow the request if there's an error checking rate limit
        return process.env.NODE_ENV === 'development';
    }
};

// Clear rate limit (for development/testing)
const clearRateLimit = async (phoneNumber: string): Promise<boolean> => {
    if (process.env.NODE_ENV !== 'development') {
        return false;
    }
    try {
        const key = `otp_limit_${phoneNumber}`;
        await redisClient.del(key);
        console.log(`Rate limit cleared for ${phoneNumber}`);
        return true;
    } catch (error) {
        console.error('Error clearing rate limit:', error);
        return false;
    }
};

// Send SMS function for general messaging
const sendSMS = async (phoneNumber: string, message: string): Promise<{ success: boolean; messageId?: string; error?: string }> => {
    try {
        console.log(`Sending SMS to ${phoneNumber}: ${message}`);

        // In development mode, just log the message
        if (process.env.NODE_ENV === 'development') {
            console.log(`📱 SMS (DEV MODE) to ${phoneNumber}:`);
            console.log(`📝 Message: ${message}`);
            return {
                success: true,
                messageId: `dev_${Date.now()}`
            };
        }

        // Production SMS sending logic would go here
        // For now, return success for development
        return {
            success: true,
            messageId: `msg_${Date.now()}`
        };
    } catch (error) {
        console.error('Error sending SMS:', error);
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error'
        };
    }
};

export {
    generateOTP,
    sendOTP,
    sendSMS,
    checkPhoneVerificationLimit,
    verifyOTP,
    clearRateLimit
};
