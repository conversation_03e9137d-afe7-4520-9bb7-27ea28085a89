import 'food_entry.dart';

/// Model for daily nutrition summary and goals
class DailyNutrition {
  final DateTime date;
  final List<FoodEntry> foodEntries;
  final NutritionGoals goals;
  final double? averageBloodSugarChange; // mg/dL
  final List<BloodSugarReading> bloodSugarReadings;

  const DailyNutrition({
    required this.date,
    required this.foodEntries,
    required this.goals,
    this.averageBloodSugarChange,
    this.bloodSugarReadings = const [],
  });

  /// Calculate total carbohydrates for the day
  double get totalCarbohydrates {
    return foodEntries.fold(0.0, (sum, entry) => sum + entry.carbohydrates);
  }

  /// Calculate total calories for the day
  double get totalCalories {
    return foodEntries.fold(0.0, (sum, entry) => sum + entry.calories);
  }

  /// Calculate total protein for the day
  double get totalProtein {
    return foodEntries.fold(0.0, (sum, entry) => sum + entry.protein);
  }

  /// Calculate total fat for the day
  double get totalFat {
    return foodEntries.fold(0.0, (sum, entry) => sum + entry.fat);
  }

  /// Calculate total fiber for the day
  double get totalFiber {
    return foodEntries.fold(0.0, (sum, entry) => sum + entry.fiber);
  }

  /// Get carbohydrate progress as percentage
  double get carbProgress {
    if (goals.carbohydrates == 0) return 0;
    return (totalCarbohydrates / goals.carbohydrates).clamp(0.0, 1.0);
  }

  /// Get calorie progress as percentage
  double get calorieProgress {
    if (goals.calories == 0) return 0;
    return (totalCalories / goals.calories).clamp(0.0, 1.0);
  }

  /// Get protein progress as percentage
  double get proteinProgress {
    if (goals.protein == 0) return 0;
    return (totalProtein / goals.protein).clamp(0.0, 1.0);
  }

  /// Get food entries grouped by meal type
  Map<MealType, List<FoodEntry>> get entriesByMeal {
    final Map<MealType, List<FoodEntry>> grouped = {};
    
    for (final mealType in MealType.values) {
      grouped[mealType] = foodEntries
          .where((entry) => entry.mealType == mealType)
          .toList()
        ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
    }
    
    return grouped;
  }

  /// Get nutrition summary for a specific meal
  MealNutrition getMealNutrition(MealType mealType) {
    final mealEntries = foodEntries
        .where((entry) => entry.mealType == mealType)
        .toList();
    
    return MealNutrition(
      mealType: mealType,
      entries: mealEntries,
      totalCarbs: mealEntries.fold(0.0, (sum, entry) => sum + entry.carbohydrates),
      totalCalories: mealEntries.fold(0.0, (sum, entry) => sum + entry.calories),
      totalProtein: mealEntries.fold(0.0, (sum, entry) => sum + entry.protein),
      totalFat: mealEntries.fold(0.0, (sum, entry) => sum + entry.fat),
    );
  }

  /// Check if daily goals are met
  bool get goalsAchieved {
    return carbProgress >= 0.8 && carbProgress <= 1.2 &&
           calorieProgress >= 0.8 && calorieProgress <= 1.2;
  }

  /// Get diabetes-friendly food percentage
  double get diabetesFriendlyPercentage {
    if (foodEntries.isEmpty) return 0;
    final friendlyCount = foodEntries.where((entry) => entry.isDiabetesFriendly).length;
    return friendlyCount / foodEntries.length;
  }

  /// Get average glycemic index for the day
  double get averageGlycemicIndex {
    if (foodEntries.isEmpty) return 0;
    final totalGI = foodEntries.fold(0.0, (sum, entry) => sum + entry.glycemicIndex.value);
    return totalGI / foodEntries.length;
  }

  DailyNutrition copyWith({
    DateTime? date,
    List<FoodEntry>? foodEntries,
    NutritionGoals? goals,
    double? averageBloodSugarChange,
    List<BloodSugarReading>? bloodSugarReadings,
  }) {
    return DailyNutrition(
      date: date ?? this.date,
      foodEntries: foodEntries ?? this.foodEntries,
      goals: goals ?? this.goals,
      averageBloodSugarChange: averageBloodSugarChange ?? this.averageBloodSugarChange,
      bloodSugarReadings: bloodSugarReadings ?? this.bloodSugarReadings,
    );
  }
}

/// Model for nutrition goals
class NutritionGoals {
  final double carbohydrates; // grams per day
  final double calories; // kcal per day
  final double protein; // grams per day
  final double fat; // grams per day
  final double fiber; // grams per day

  const NutritionGoals({
    required this.carbohydrates,
    required this.calories,
    required this.protein,
    required this.fat,
    required this.fiber,
  });

  /// Default goals for diabetic patients
  factory NutritionGoals.diabeticDefault() {
    return const NutritionGoals(
      carbohydrates: 150, // 45-65% of calories, assuming 1800 cal diet
      calories: 1800,
      protein: 90, // 15-20% of calories
      fat: 60, // 20-35% of calories
      fiber: 25, // recommended daily fiber
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'carbohydrates': carbohydrates,
      'calories': calories,
      'protein': protein,
      'fat': fat,
      'fiber': fiber,
    };
  }

  factory NutritionGoals.fromJson(Map<String, dynamic> json) {
    return NutritionGoals(
      carbohydrates: (json['carbohydrates'] as num).toDouble(),
      calories: (json['calories'] as num).toDouble(),
      protein: (json['protein'] as num).toDouble(),
      fat: (json['fat'] as num).toDouble(),
      fiber: (json['fiber'] as num).toDouble(),
    );
  }
}

/// Model for meal-specific nutrition summary
class MealNutrition {
  final MealType mealType;
  final List<FoodEntry> entries;
  final double totalCarbs;
  final double totalCalories;
  final double totalProtein;
  final double totalFat;

  const MealNutrition({
    required this.mealType,
    required this.entries,
    required this.totalCarbs,
    required this.totalCalories,
    required this.totalProtein,
    required this.totalFat,
  });

  /// Check if meal is empty
  bool get isEmpty => entries.isEmpty;

  /// Get meal timing (earliest entry timestamp)
  DateTime? get mealTime {
    if (entries.isEmpty) return null;
    return entries.map((e) => e.timestamp).reduce((a, b) => a.isBefore(b) ? a : b);
  }

  /// Get formatted meal time
  String get formattedMealTime {
    final time = mealTime;
    if (time == null) return '';
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  /// Get dominant food category in this meal
  FoodCategory? get dominantCategory {
    if (entries.isEmpty) return null;
    
    final categoryCount = <FoodCategory, int>{};
    for (final entry in entries) {
      categoryCount[entry.category] = (categoryCount[entry.category] ?? 0) + 1;
    }
    
    return categoryCount.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// Get blood sugar impact level for the meal
  BloodSugarImpact get mealBloodSugarImpact {
    if (entries.isEmpty) return BloodSugarImpact.low;
    
    final highImpactCount = entries.where((e) => e.bloodSugarImpact == BloodSugarImpact.high).length;
    final moderateImpactCount = entries.where((e) => e.bloodSugarImpact == BloodSugarImpact.moderate).length;
    
    if (highImpactCount > 0) return BloodSugarImpact.high;
    if (moderateImpactCount > 0) return BloodSugarImpact.moderate;
    return BloodSugarImpact.low;
  }
}

/// Model for blood sugar readings linked to meals
class BloodSugarReading {
  final String id;
  final double value; // mg/dL
  final DateTime timestamp;
  final String? mealId; // linked to food entry
  final bool isBeforeMeal;
  final String? notes;

  const BloodSugarReading({
    required this.id,
    required this.value,
    required this.timestamp,
    this.mealId,
    this.isBeforeMeal = true,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'mealId': mealId,
      'isBeforeMeal': isBeforeMeal,
      'notes': notes,
    };
  }

  factory BloodSugarReading.fromJson(Map<String, dynamic> json) {
    return BloodSugarReading(
      id: json['id'] as String,
      value: (json['value'] as num).toDouble(),
      timestamp: DateTime.parse(json['timestamp'] as String),
      mealId: json['mealId'] as String?,
      isBeforeMeal: json['isBeforeMeal'] as bool? ?? true,
      notes: json['notes'] as String?,
    );
  }
}

/// Model for daily insights and recommendations
class DailyInsights {
  final DateTime date;
  final List<String> recommendations;
  final List<String> achievements;
  final List<String> warnings;
  final double overallScore; // 0-100

  const DailyInsights({
    required this.date,
    required this.recommendations,
    required this.achievements,
    required this.warnings,
    required this.overallScore,
  });

  /// Generate insights based on daily nutrition
  factory DailyInsights.fromDailyNutrition(DailyNutrition nutrition) {
    final recommendations = <String>[];
    final achievements = <String>[];
    final warnings = <String>[];
    double score = 50; // base score

    // Analyze carbohydrate intake
    if (nutrition.carbProgress > 1.2) {
      warnings.add('Carbohydrate intake is above recommended levels');
      score -= 15;
    } else if (nutrition.carbProgress >= 0.8) {
      achievements.add('Good carbohydrate management');
      score += 10;
    }

    // Analyze diabetes-friendly food choices
    if (nutrition.diabetesFriendlyPercentage >= 0.7) {
      achievements.add('Excellent diabetes-friendly food choices');
      score += 15;
    } else if (nutrition.diabetesFriendlyPercentage < 0.3) {
      recommendations.add('Try to include more low-GI, low-carb foods');
      score -= 10;
    }

    // Analyze glycemic index
    if (nutrition.averageGlycemicIndex <= 55) {
      achievements.add('Great job keeping glycemic index low');
      score += 10;
    } else if (nutrition.averageGlycemicIndex > 70) {
      warnings.add('High glycemic index foods may spike blood sugar');
      score -= 10;
    }

    // Fiber intake
    if (nutrition.totalFiber >= 25) {
      achievements.add('Excellent fiber intake for blood sugar control');
      score += 5;
    } else if (nutrition.totalFiber < 15) {
      recommendations.add('Increase fiber intake with vegetables and whole grains');
    }

    return DailyInsights(
      date: nutrition.date,
      recommendations: recommendations,
      achievements: achievements,
      warnings: warnings,
      overallScore: score.clamp(0, 100),
    );
  }
}
