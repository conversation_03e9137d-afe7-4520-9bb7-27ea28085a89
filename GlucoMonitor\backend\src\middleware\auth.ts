import jwt from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import User, { IUser } from '../models/User';

// Extend Request interface to include user
export interface AuthRequest extends Request {
    user?: IUser;
}

interface JWTPayload {
    id: string;
    iat: number;
    exp: number;
}

export const protect = async (req: AuthRequest, res: Response, next: NextFunction): Promise<void> => {
    let token: string | undefined;

    if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
        token = req.headers.authorization.split(' ')[1];
    }

    if (!token) {
        res.status(401).json({
            success: false,
            message: 'Not authorized to access this route'
        });
        return;
    }

    try {
        // Verify JWT token
        const decoded = jwt.verify(token, process.env.JWT_SECRET as string) as JWTPayload;

        // Find user by ID
        const user = await User.findById(decoded.id);

        if (!user) {
            res.status(401).json({
                success: false,
                message: 'User not found'
            });
            return;
        }

        req.user = user;
        next();
    } catch (err) {
        console.error('Auth Error:', err);
        res.status(401).json({
            success: false,
            message: 'Not authorized to access this route'
        });
    }
};

// Authorization middleware for role-based access
export const authorize = (...roles: string[]) => {
    return (req: AuthRequest, res: Response, next: NextFunction): void => {
        if (!req.user) {
            res.status(401).json({
                success: false,
                message: 'Not authorized to access this route'
            });
            return;
        }

        if (!roles.includes(req.user.role || 'user')) {
            res.status(403).json({
                success: false,
                message: 'User role is not authorized to access this route'
            });
            return;
        }

        next();
    };
};
