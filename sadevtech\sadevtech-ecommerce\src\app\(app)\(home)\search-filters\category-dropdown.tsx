"use client";

import { useRef, useState } from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { Category } from "@/payload-types";
import { se } from "date-fns/locale";


interface Props {
    category: Category;
    isActive?: boolean;
    isNavigationHovered?: boolean;
}

export const CategoryDropdown = ({
    category,
    isActive,
    isNavigationHovered,
}: Props) => {
   const [isOpren, setIsOpen] = useState(false);
   const dropdownRef = useRef<HTMLDivElement>(null);

   const onMouseEnter = () => {
     if (category.subcategories) {
        console.log("Hello")
        setIsOpen(true);
     }
   };

   const onMouseLeave = () => setIsOpen(false);

  return (
      <div 
      className="relative"
      ref={dropdownRef}
      onMouseEnter={onMouseEnter}
      onMouseLeave={onMouseLeave}
      >
       <div className="relative">
         <Button 
      variant="elevated"
      className={cn(
       "h-11 px-4 bg-transparent border-transparent rounded-full hover:bg-[var(--primary)] hover:border-[var(--primary)] text-[var(--foreground)]", 
        isActive && !isNavigationHovered && "bg-[var(--primary)] border-[var(--primary)] ",
      )}
      >
        {category.name}
      </Button>
      {category.subcategories && category.subcategories.length > 0 && (
        <div
          className={cn(
            "opacity-0 absolute -bottom-3 w-0 h-0 border-l-[10px] border-r-[10px] border-b-[10px] border-l-transparent border-r-transparent border-[var(--primary)] left-1/2 -translate-x-1/2",
            isOpren && "opacity-100 ",
          )}
         />
      )}
       </div>
      </div>  
    )
}