import 'dart:typed_data';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../models/food_entry.dart';
import '../constants/south_african_foods.dart';
import '../services/learning_algorithm_service.dart';
import '../services/custom_food_training_service.dart';

/// Enhanced offline food recognition service with improved pattern matching
class EnhancedOfflineRecognitionService {
  /// Enhanced food patterns with more detailed characteristics
  static const Map<String, FoodPattern> _enhancedFoodPatterns = {
    'pap': FoodPattern(
      name: 'Pap',
      colorRanges: [
        ColorRange(
          minR: 200,
          maxR: 255,
          minG: 200,
          maxG: 255,
          minB: 180,
          maxB: 255,
        ),
        ColorRange(
          minR: 240,
          maxR: 255,
          minG: 240,
          maxG: 255,
          minB: 220,
          maxB: 255,
        ),
      ],
      textureFeatures: TextureFeatures(
        smoothness: 0.8,
        uniformity: 0.9,
        graininess: 0.2,
      ),
      shapeFeatures: ShapeFeatures(
        roundness: 0.3,
        elongation: 0.4,
        compactness: 0.7,
      ),
      contextClues: ['plate', 'bowl', 'spoon', 'traditional'],
      confidence: 0.85,
    ),
    'morogo': FoodPattern(
      name: 'Morogo',
      colorRanges: [
        ColorRange(
          minR: 20,
          maxR: 100,
          minG: 80,
          maxG: 180,
          minB: 20,
          maxB: 100,
        ),
        ColorRange(
          minR: 40,
          maxR: 120,
          minG: 100,
          maxG: 200,
          minB: 40,
          maxB: 120,
        ),
      ],
      textureFeatures: TextureFeatures(
        smoothness: 0.3,
        uniformity: 0.4,
        graininess: 0.8,
      ),
      shapeFeatures: ShapeFeatures(
        roundness: 0.2,
        elongation: 0.8,
        compactness: 0.3,
      ),
      contextClues: ['leafy', 'green', 'vegetable', 'traditional'],
      confidence: 0.80,
    ),
    'boerewors': FoodPattern(
      name: 'Boerewors',
      colorRanges: [
        ColorRange(
          minR: 120,
          maxR: 200,
          minG: 80,
          maxG: 150,
          minB: 60,
          maxB: 120,
        ),
        ColorRange(
          minR: 140,
          maxR: 220,
          minG: 100,
          maxG: 170,
          minB: 80,
          maxB: 140,
        ),
      ],
      textureFeatures: TextureFeatures(
        smoothness: 0.6,
        uniformity: 0.5,
        graininess: 0.7,
      ),
      shapeFeatures: ShapeFeatures(
        roundness: 0.9,
        elongation: 0.8,
        compactness: 0.6,
      ),
      contextClues: ['sausage', 'meat', 'braai', 'coiled'],
      confidence: 0.82,
    ),
    'samp_and_beans': FoodPattern(
      name: 'Samp and Beans',
      colorRanges: [
        ColorRange(
          minR: 180,
          maxR: 240,
          minG: 160,
          maxG: 220,
          minB: 100,
          maxB: 160,
        ),
        ColorRange(
          minR: 200,
          maxR: 255,
          minG: 180,
          maxG: 240,
          minB: 120,
          maxB: 180,
        ),
      ],
      textureFeatures: TextureFeatures(
        smoothness: 0.4,
        uniformity: 0.6,
        graininess: 0.9,
      ),
      shapeFeatures: ShapeFeatures(
        roundness: 0.5,
        elongation: 0.3,
        compactness: 0.8,
      ),
      contextClues: ['beans', 'corn', 'mixed', 'traditional'],
      confidence: 0.78,
    ),
    'biltong': FoodPattern(
      name: 'Biltong',
      colorRanges: [
        ColorRange(
          minR: 80,
          maxR: 140,
          minG: 40,
          maxG: 100,
          minB: 20,
          maxB: 80,
        ),
        ColorRange(
          minR: 100,
          maxR: 160,
          minG: 60,
          maxG: 120,
          minB: 40,
          maxB: 100,
        ),
      ],
      textureFeatures: TextureFeatures(
        smoothness: 0.2,
        uniformity: 0.3,
        graininess: 0.9,
      ),
      shapeFeatures: ShapeFeatures(
        roundness: 0.1,
        elongation: 0.9,
        compactness: 0.2,
      ),
      contextClues: ['dried', 'meat', 'strips', 'jerky'],
      confidence: 0.85,
    ),
  };

  /// Recognize food using enhanced offline patterns
  static Future<FoodEntry?> recognizeFood(
    Uint8List imageBytes, {
    MealType mealType = MealType.breakfast,
    bool useEnhancedPatterns = true,
  }) async {
    try {
      debugPrint('🔍 Starting enhanced offline recognition');

      // Extract comprehensive image features
      final imageFeatures = await _extractComprehensiveFeatures(imageBytes);

      // Check for learning algorithm improvements first
      final learnedResult = await _checkLearnedPatterns(
        imageFeatures,
        mealType,
      );
      if (learnedResult != null) {
        debugPrint('🧠 Using learned pattern');
        return learnedResult;
      }

      // Use enhanced pattern matching
      if (useEnhancedPatterns) {
        final enhancedResult = await _matchEnhancedPatterns(
          imageFeatures,
          mealType,
        );
        if (enhancedResult != null) {
          debugPrint('✅ Enhanced pattern match found');
          return enhancedResult;
        }
      }

      // Fallback to basic pattern matching
      final basicResult = await _matchBasicPatterns(imageFeatures, mealType);
      if (basicResult != null) {
        debugPrint('✅ Basic pattern match found');
        return basicResult;
      }

      debugPrint('❌ No offline pattern match found');
      return null;
    } catch (e) {
      debugPrint('💥 Enhanced offline recognition error: $e');
      return null;
    }
  }

  /// Extract comprehensive image features
  static Future<ComprehensiveImageFeatures> _extractComprehensiveFeatures(
    Uint8List imageBytes,
  ) async {
    // Color analysis
    final colorFeatures = _analyzeColorFeatures(imageBytes);

    // Texture analysis (simplified)
    final textureFeatures = _analyzeTextureFeatures(imageBytes);

    // Shape analysis (simplified)
    final shapeFeatures = _analyzeShapeFeatures(imageBytes);

    // Size analysis
    final sizeFeatures = _analyzeSizeFeatures(imageBytes);

    return ComprehensiveImageFeatures(
      colorFeatures: colorFeatures,
      textureFeatures: textureFeatures,
      shapeFeatures: shapeFeatures,
      sizeFeatures: sizeFeatures,
    );
  }

  /// Analyze color features
  static ColorFeatures _analyzeColorFeatures(Uint8List imageBytes) {
    int redSum = 0, greenSum = 0, blueSum = 0;
    int pixelCount = imageBytes.length ~/ 3;

    final redValues = <int>[];
    final greenValues = <int>[];
    final blueValues = <int>[];

    for (int i = 0; i < imageBytes.length - 2; i += 3) {
      final r = imageBytes[i];
      final g = imageBytes[i + 1];
      final b = imageBytes[i + 2];

      redSum += r;
      greenSum += g;
      blueSum += b;

      redValues.add(r);
      greenValues.add(g);
      blueValues.add(b);
    }

    // Calculate statistics
    final avgRed = redSum / pixelCount;
    final avgGreen = greenSum / pixelCount;
    final avgBlue = blueSum / pixelCount;

    // Calculate standard deviations
    final redVariance =
        redValues.fold<double>(
          0,
          (sum, val) => sum + math.pow(val - avgRed, 2),
        ) /
        pixelCount;
    final greenVariance =
        greenValues.fold<double>(
          0,
          (sum, val) => sum + math.pow(val - avgGreen, 2),
        ) /
        pixelCount;
    final blueVariance =
        blueValues.fold<double>(
          0,
          (sum, val) => sum + math.pow(val - avgBlue, 2),
        ) /
        pixelCount;

    return ColorFeatures(
      avgRed: avgRed,
      avgGreen: avgGreen,
      avgBlue: avgBlue,
      redStdDev: math.sqrt(redVariance),
      greenStdDev: math.sqrt(greenVariance),
      blueStdDev: math.sqrt(blueVariance),
      dominantColor: _getDominantColor(avgRed, avgGreen, avgBlue),
      brightness: (avgRed + avgGreen + avgBlue) / 3,
      saturation: _calculateSaturation(avgRed, avgGreen, avgBlue),
    );
  }

  /// Analyze texture features (simplified)
  static TextureFeatures _analyzeTextureFeatures(Uint8List imageBytes) {
    // Simplified texture analysis
    // In a real implementation, this would use proper image processing

    final pixelCount = imageBytes.length ~/ 3;
    double variance = 0;
    double edgeCount = 0;

    // Calculate pixel variance for smoothness
    for (int i = 0; i < imageBytes.length - 5; i += 3) {
      final current =
          (imageBytes[i] + imageBytes[i + 1] + imageBytes[i + 2]) / 3;
      final next =
          (imageBytes[i + 3] + imageBytes[i + 4] + imageBytes[i + 5]) / 3;
      variance += math.pow(current - next, 2);

      if ((current - next).abs() > 30) {
        edgeCount++;
      }
    }

    final smoothness = 1.0 - (variance / pixelCount).clamp(0.0, 1.0);
    final uniformity = 1.0 - (edgeCount / pixelCount).clamp(0.0, 1.0);
    final graininess = (edgeCount / pixelCount).clamp(0.0, 1.0);

    return TextureFeatures(
      smoothness: smoothness,
      uniformity: uniformity,
      graininess: graininess,
    );
  }

  /// Analyze shape features (simplified)
  static ShapeFeatures _analyzeShapeFeatures(Uint8List imageBytes) {
    // Simplified shape analysis
    // In a real implementation, this would use computer vision

    return const ShapeFeatures(
      roundness: 0.5,
      elongation: 0.5,
      compactness: 0.5,
    );
  }

  /// Analyze size features
  static SizeFeatures _analyzeSizeFeatures(Uint8List imageBytes) {
    return SizeFeatures(
      totalPixels: imageBytes.length ~/ 3,
      estimatedArea: (imageBytes.length ~/ 3) * 0.1, // Simplified
    );
  }

  /// Check learned patterns from user corrections
  static Future<FoodEntry?> _checkLearnedPatterns(
    ComprehensiveImageFeatures features,
    MealType mealType,
  ) async {
    try {
      // Check custom trained foods first
      final customResult = await CustomFoodTrainingService.recognizeCustomFood(
        features,
        mealType,
      );

      if (customResult != null) {
        return customResult;
      }

      // Then check learning algorithm improvements
      final suggestions = await LearningAlgorithmService.getUserFoodSuggestions(
        mealType,
      );

      // Use suggestions to boost confidence for likely foods
      for (final suggestion in suggestions.take(3)) {
        for (final entry in _enhancedFoodPatterns.entries) {
          if (entry.value.name.toLowerCase().contains(
            suggestion.toLowerCase(),
          )) {
            final score = _calculatePatternScore(features, entry.value);
            if (score > 0.6) {
              // Lower threshold for learned foods
              return _createFoodEntryFromPattern(
                entry.value,
                mealType,
                score + 0.1,
              );
            }
          }
        }
      }

      return null;
    } catch (e) {
      debugPrint('💥 Error checking learned patterns: $e');
      return null;
    }
  }

  /// Match against enhanced food patterns
  static Future<FoodEntry?> _matchEnhancedPatterns(
    ComprehensiveImageFeatures features,
    MealType mealType,
  ) async {
    double bestScore = 0.0;
    String? bestMatch;

    for (final entry in _enhancedFoodPatterns.entries) {
      final pattern = entry.value;
      final score = _calculatePatternScore(features, pattern);

      if (score > bestScore && score > 0.7) {
        bestScore = score;
        bestMatch = entry.key;
      }
    }

    if (bestMatch != null) {
      final pattern = _enhancedFoodPatterns[bestMatch]!;
      return _createFoodEntryFromPattern(pattern, mealType, bestScore);
    }

    return null;
  }

  /// Match against basic patterns (fallback)
  static Future<FoodEntry?> _matchBasicPatterns(
    ComprehensiveImageFeatures features,
    MealType mealType,
  ) async {
    // Use the existing basic pattern matching as fallback
    final colorAnalysis = {
      'avgRed': features.colorFeatures.avgRed,
      'avgGreen': features.colorFeatures.avgGreen,
      'avgBlue': features.colorFeatures.avgBlue,
      'dominantColor': features.colorFeatures.dominantColor,
    };

    // Simple pattern matching for common SA foods
    if (colorAnalysis['dominantColor'] == 'white' &&
        features.colorFeatures.avgRed > 200 &&
        features.colorFeatures.avgGreen > 200) {
      return _createBasicFoodEntry('Pap', mealType);
    } else if (colorAnalysis['dominantColor'] == 'green' &&
        features.colorFeatures.avgGreen > 150) {
      return _createBasicFoodEntry('Morogo', mealType);
    } else if (colorAnalysis['dominantColor'] == 'brown' &&
        features.colorFeatures.avgRed > 100) {
      return _createBasicFoodEntry('Boerewors', mealType);
    }

    return null;
  }

  /// Calculate pattern matching score
  static double _calculatePatternScore(
    ComprehensiveImageFeatures features,
    FoodPattern pattern,
  ) {
    double colorScore = 0.0;
    double textureScore = 0.0;
    double shapeScore = 0.0;

    // Color matching
    for (final colorRange in pattern.colorRanges) {
      if (_isColorInRange(features.colorFeatures, colorRange)) {
        colorScore = math.max(colorScore, 0.9);
        break;
      }
    }

    // Texture matching
    textureScore = _calculateTextureScore(
      features.textureFeatures,
      pattern.textureFeatures,
    );

    // Shape matching
    shapeScore = _calculateShapeScore(
      features.shapeFeatures,
      pattern.shapeFeatures,
    );

    // Weighted average
    final totalScore =
        (colorScore * 0.5) + (textureScore * 0.3) + (shapeScore * 0.2);

    return totalScore * pattern.confidence;
  }

  /// Check if color is in range
  static bool _isColorInRange(ColorFeatures color, ColorRange range) {
    return color.avgRed >= range.minR &&
        color.avgRed <= range.maxR &&
        color.avgGreen >= range.minG &&
        color.avgGreen <= range.maxG &&
        color.avgBlue >= range.minB &&
        color.avgBlue <= range.maxB;
  }

  /// Calculate texture similarity score
  static double _calculateTextureScore(
    TextureFeatures actual,
    TextureFeatures expected,
  ) {
    final smoothnessDiff = (actual.smoothness - expected.smoothness).abs();
    final uniformityDiff = (actual.uniformity - expected.uniformity).abs();
    final graininessDiff = (actual.graininess - expected.graininess).abs();

    return 1.0 - ((smoothnessDiff + uniformityDiff + graininessDiff) / 3.0);
  }

  /// Calculate shape similarity score
  static double _calculateShapeScore(
    ShapeFeatures actual,
    ShapeFeatures expected,
  ) {
    final roundnessDiff = (actual.roundness - expected.roundness).abs();
    final elongationDiff = (actual.elongation - expected.elongation).abs();
    final compactnessDiff = (actual.compactness - expected.compactness).abs();

    return 1.0 - ((roundnessDiff + elongationDiff + compactnessDiff) / 3.0);
  }

  /// Create food entry from pattern
  static FoodEntry _createFoodEntryFromPattern(
    FoodPattern pattern,
    MealType mealType,
    double confidence,
  ) {
    // Find corresponding SA food
    final saFood = SouthAfricanFoods.allFoods.firstWhere(
      (food) => food.name.toLowerCase().contains(pattern.name.toLowerCase()),
      orElse: () => SouthAfricanFoods.pap, // Default fallback
    );

    return FoodEntry(
      id: 'enhanced_${DateTime.now().millisecondsSinceEpoch}',
      name: pattern.name,
      carbohydrates: saFood.carbsPer100g,
      calories: saFood.caloriesPer100g,
      protein: saFood.proteinPer100g,
      fat: saFood.fatPer100g,
      fiber: saFood.fiberPer100g,
      glycemicIndex: saFood.glycemicIndex,
      category: saFood.category,
      portion: '100g (AI estimated)',
      portionSize: 100.0,
      unit: 'g',
      mealType: mealType,
      timestamp: DateTime.now(),
      notes:
          'Enhanced offline recognition - confidence: ${(confidence * 100).toInt()}%',
      isCustom: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Create basic food entry
  static FoodEntry _createBasicFoodEntry(String foodName, MealType mealType) {
    final saFood = SouthAfricanFoods.allFoods.firstWhere(
      (food) => food.name.toLowerCase().contains(foodName.toLowerCase()),
      orElse: () => SouthAfricanFoods.pap,
    );

    return FoodEntry(
      id: 'basic_${DateTime.now().millisecondsSinceEpoch}',
      name: foodName,
      carbohydrates: saFood.carbsPer100g,
      calories: saFood.caloriesPer100g,
      protein: saFood.proteinPer100g,
      fat: saFood.fatPer100g,
      fiber: saFood.fiberPer100g,
      glycemicIndex: saFood.glycemicIndex,
      category: saFood.category,
      portion: '100g (estimated)',
      portionSize: 100.0,
      unit: 'g',
      mealType: mealType,
      timestamp: DateTime.now(),
      notes: 'Basic offline recognition',
      isCustom: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Get dominant color
  static String _getDominantColor(double r, double g, double b) {
    if (r > g && r > b) return 'red';
    if (g > r && g > b) return 'green';
    if (b > r && b > g) return 'blue';
    if (r > 200 && g > 200 && b > 200) return 'white';
    if (r < 50 && g < 50 && b < 50) return 'black';
    if (r > 150 && g > 100 && b < 100) return 'brown';
    if (r > 200 && g > 200 && b < 150) return 'yellow';
    return 'mixed';
  }

  /// Calculate color saturation
  static double _calculateSaturation(double r, double g, double b) {
    final max = math.max(r, math.max(g, b));
    final min = math.min(r, math.min(g, b));
    return max > 0 ? (max - min) / max : 0.0;
  }
}

// Data classes for enhanced pattern matching
class FoodPattern {
  final String name;
  final List<ColorRange> colorRanges;
  final TextureFeatures textureFeatures;
  final ShapeFeatures shapeFeatures;
  final List<String> contextClues;
  final double confidence;

  const FoodPattern({
    required this.name,
    required this.colorRanges,
    required this.textureFeatures,
    required this.shapeFeatures,
    required this.contextClues,
    required this.confidence,
  });
}

class ColorRange {
  final double minR, maxR, minG, maxG, minB, maxB;

  const ColorRange({
    required this.minR,
    required this.maxR,
    required this.minG,
    required this.maxG,
    required this.minB,
    required this.maxB,
  });
}

class ComprehensiveImageFeatures {
  final ColorFeatures colorFeatures;
  final TextureFeatures textureFeatures;
  final ShapeFeatures shapeFeatures;
  final SizeFeatures sizeFeatures;

  const ComprehensiveImageFeatures({
    required this.colorFeatures,
    required this.textureFeatures,
    required this.shapeFeatures,
    required this.sizeFeatures,
  });
}

class ColorFeatures {
  final double avgRed, avgGreen, avgBlue;
  final double redStdDev, greenStdDev, blueStdDev;
  final String dominantColor;
  final double brightness;
  final double saturation;

  const ColorFeatures({
    required this.avgRed,
    required this.avgGreen,
    required this.avgBlue,
    required this.redStdDev,
    required this.greenStdDev,
    required this.blueStdDev,
    required this.dominantColor,
    required this.brightness,
    required this.saturation,
  });
}

class TextureFeatures {
  final double smoothness;
  final double uniformity;
  final double graininess;

  const TextureFeatures({
    required this.smoothness,
    required this.uniformity,
    required this.graininess,
  });
}

class ShapeFeatures {
  final double roundness;
  final double elongation;
  final double compactness;

  const ShapeFeatures({
    required this.roundness,
    required this.elongation,
    required this.compactness,
  });
}

class SizeFeatures {
  final int totalPixels;
  final double estimatedArea;

  const SizeFeatures({required this.totalPixels, required this.estimatedArea});
}
