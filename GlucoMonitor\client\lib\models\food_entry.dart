import 'package:flutter/material.dart';

/// Class for meal time range
class MealTimeRange {
  final String start;
  final String end;

  const MealTimeRange({required this.start, required this.end});

  /// Check if a time falls within this range
  bool isTimeInRange(String time) {
    final timeMinutes = _timeToMinutes(time);
    final startMinutes = _timeToMinutes(start);
    final endMinutes = _timeToMinutes(end);

    // Handle overnight ranges (e.g., 22:00 - 02:00)
    if (endMinutes < startMinutes) {
      return timeMinutes >= startMinutes || timeMinutes <= endMinutes;
    }

    return timeMinutes >= startMinutes && timeMinutes <= endMinutes;
  }

  int _timeToMinutes(String time) {
    final parts = time.split(':');
    return int.parse(parts[0]) * 60 + int.parse(parts[1]);
  }

  @override
  String toString() => '$start - $end';
}

/// Custom meal time configuration
class CustomMealTime {
  final String id;
  final String name;
  final String displayName;
  final IconData icon;
  final Color color;
  final String defaultTime;
  final MealTimeRange timeRange;
  final bool isActive;

  const CustomMealTime({
    required this.id,
    required this.name,
    required this.displayName,
    required this.icon,
    required this.color,
    required this.defaultTime,
    required this.timeRange,
    this.isActive = true,
  });

  factory CustomMealTime.fromJson(Map<String, dynamic> json) {
    return CustomMealTime(
      id: json['_id'] ?? json['id'] ?? '',
      name: json['name'] ?? '',
      displayName: json['displayName'] ?? '',
      icon: _iconFromString(json['icon'] ?? 'schedule'),
      color: Color(int.parse(json['color'].replaceFirst('#', '0xFF'))),
      defaultTime: json['defaultTime'] ?? '12:00',
      timeRange: MealTimeRange(
        start: json['timeRange']['start'] ?? '00:00',
        end: json['timeRange']['end'] ?? '23:59',
      ),
      isActive: json['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'icon': _iconToString(icon),
      'color':
          '#${color.toARGB32().toRadixString(16).substring(2).toUpperCase()}',
      'defaultTime': defaultTime,
      'timeRange': {'start': timeRange.start, 'end': timeRange.end},
      'isActive': isActive,
    };
  }

  static IconData _iconFromString(String iconName) {
    switch (iconName) {
      case 'wb_sunny':
        return Icons.wb_sunny;
      case 'coffee':
        return Icons.coffee;
      case 'restaurant':
        return Icons.restaurant;
      case 'local_cafe':
        return Icons.local_cafe;
      case 'dinner_dining':
        return Icons.dinner_dining;
      case 'cookie':
        return Icons.cookie;
      case 'nights_stay':
        return Icons.nights_stay;
      case 'schedule':
        return Icons.schedule;
      default:
        return Icons.restaurant;
    }
  }

  static String _iconToString(IconData icon) {
    if (icon == Icons.wb_sunny) return 'wb_sunny';
    if (icon == Icons.coffee) return 'coffee';
    if (icon == Icons.restaurant) return 'restaurant';
    if (icon == Icons.local_cafe) return 'local_cafe';
    if (icon == Icons.dinner_dining) return 'dinner_dining';
    if (icon == Icons.cookie) return 'cookie';
    if (icon == Icons.nights_stay) return 'nights_stay';
    if (icon == Icons.schedule) return 'schedule';
    return 'restaurant';
  }
}

/// Enum for different meal types
enum MealType {
  breakfast,
  morningSnack,
  lunch,
  afternoonSnack,
  dinner,
  eveningSnack,
  lateNight,
  custom;

  String get displayName {
    switch (this) {
      case MealType.breakfast:
        return 'Breakfast';
      case MealType.morningSnack:
        return 'Morning Snack';
      case MealType.lunch:
        return 'Lunch';
      case MealType.afternoonSnack:
        return 'Afternoon Snack';
      case MealType.dinner:
        return 'Dinner';
      case MealType.eveningSnack:
        return 'Evening Snack';
      case MealType.lateNight:
        return 'Late Night';
      case MealType.custom:
        return 'Custom';
    }
  }

  IconData get icon {
    switch (this) {
      case MealType.breakfast:
        return Icons.wb_sunny;
      case MealType.morningSnack:
        return Icons.coffee;
      case MealType.lunch:
        return Icons.restaurant;
      case MealType.afternoonSnack:
        return Icons.local_cafe;
      case MealType.dinner:
        return Icons.dinner_dining;
      case MealType.eveningSnack:
        return Icons.cookie;
      case MealType.lateNight:
        return Icons.nights_stay;
      case MealType.custom:
        return Icons.schedule;
    }
  }

  Color get color {
    switch (this) {
      case MealType.breakfast:
        return const Color(0xFFFF9800); // Orange
      case MealType.morningSnack:
        return const Color(0xFF8BC34A); // Light Green
      case MealType.lunch:
        return const Color(0xFF2196F3); // Blue
      case MealType.afternoonSnack:
        return const Color(0xFFFF5722); // Deep Orange
      case MealType.dinner:
        return const Color(0xFF3F51B5); // Indigo
      case MealType.eveningSnack:
        return const Color(0xFF9C27B0); // Purple
      case MealType.lateNight:
        return const Color(0xFF607D8B); // Blue Grey
      case MealType.custom:
        return const Color(0xFF795548); // Brown
    }
  }

  /// Default time for this meal type (24-hour format)
  String get defaultTime {
    switch (this) {
      case MealType.breakfast:
        return '07:00';
      case MealType.morningSnack:
        return '10:00';
      case MealType.lunch:
        return '12:30';
      case MealType.afternoonSnack:
        return '15:30';
      case MealType.dinner:
        return '19:00';
      case MealType.eveningSnack:
        return '20:30';
      case MealType.lateNight:
        return '23:00';
      case MealType.custom:
        return '12:00';
    }
  }

  /// Time range for this meal type
  MealTimeRange get timeRange {
    switch (this) {
      case MealType.breakfast:
        return MealTimeRange(start: '05:00', end: '11:00');
      case MealType.morningSnack:
        return MealTimeRange(start: '09:00', end: '11:30');
      case MealType.lunch:
        return MealTimeRange(start: '11:00', end: '15:00');
      case MealType.afternoonSnack:
        return MealTimeRange(start: '14:00', end: '17:00');
      case MealType.dinner:
        return MealTimeRange(start: '17:00', end: '22:00');
      case MealType.eveningSnack:
        return MealTimeRange(start: '19:30', end: '23:00');
      case MealType.lateNight:
        return MealTimeRange(start: '22:00', end: '02:00');
      case MealType.custom:
        return MealTimeRange(start: '00:00', end: '23:59');
    }
  }

  /// Convert to backend string format
  String get backendValue {
    switch (this) {
      case MealType.breakfast:
        return 'breakfast';
      case MealType.morningSnack:
        return 'morning_snack';
      case MealType.lunch:
        return 'lunch';
      case MealType.afternoonSnack:
        return 'afternoon_snack';
      case MealType.dinner:
        return 'dinner';
      case MealType.eveningSnack:
        return 'evening_snack';
      case MealType.lateNight:
        return 'late_night';
      case MealType.custom:
        return 'custom';
    }
  }

  /// Create MealType from backend string
  static MealType fromBackendValue(String value) {
    switch (value) {
      case 'breakfast':
        return MealType.breakfast;
      case 'morning_snack':
        return MealType.morningSnack;
      case 'lunch':
        return MealType.lunch;
      case 'afternoon_snack':
        return MealType.afternoonSnack;
      case 'dinner':
        return MealType.dinner;
      case 'evening_snack':
        return MealType.eveningSnack;
      case 'late_night':
        return MealType.lateNight;
      case 'custom':
        return MealType.custom;
      default:
        return MealType.breakfast;
    }
  }
}

/// Enum for Glycemic Index levels
enum GlycemicIndex {
  low,
  medium,
  high;

  String get displayName {
    switch (this) {
      case GlycemicIndex.low:
        return 'Low GI';
      case GlycemicIndex.medium:
        return 'Medium GI';
      case GlycemicIndex.high:
        return 'High GI';
    }
  }

  Color get color {
    switch (this) {
      case GlycemicIndex.low:
        return Colors.green;
      case GlycemicIndex.medium:
        return Colors.orange;
      case GlycemicIndex.high:
        return Colors.red;
    }
  }

  int get value {
    switch (this) {
      case GlycemicIndex.low:
        return 55;
      case GlycemicIndex.medium:
        return 70;
      case GlycemicIndex.high:
        return 100;
    }
  }

  static GlycemicIndex fromValue(int value) {
    if (value <= 55) return GlycemicIndex.low;
    if (value <= 70) return GlycemicIndex.medium;
    return GlycemicIndex.high;
  }
}

/// Enum for food categories
enum FoodCategory {
  grains,
  vegetables,
  fruits,
  proteins,
  dairy,
  fats,
  beverages,
  sweets,
  other;

  String get displayName {
    switch (this) {
      case FoodCategory.grains:
        return 'Grains & Starches';
      case FoodCategory.vegetables:
        return 'Vegetables';
      case FoodCategory.fruits:
        return 'Fruits';
      case FoodCategory.proteins:
        return 'Proteins';
      case FoodCategory.dairy:
        return 'Dairy';
      case FoodCategory.fats:
        return 'Fats & Oils';
      case FoodCategory.beverages:
        return 'Beverages';
      case FoodCategory.sweets:
        return 'Sweets & Desserts';
      case FoodCategory.other:
        return 'Other';
    }
  }

  IconData get icon {
    switch (this) {
      case FoodCategory.grains:
        return Icons.grain;
      case FoodCategory.vegetables:
        return Icons.eco;
      case FoodCategory.fruits:
        return Icons.apple;
      case FoodCategory.proteins:
        return Icons.set_meal;
      case FoodCategory.dairy:
        return Icons.local_drink;
      case FoodCategory.fats:
        return Icons.opacity;
      case FoodCategory.beverages:
        return Icons.local_cafe;
      case FoodCategory.sweets:
        return Icons.cake;
      case FoodCategory.other:
        return Icons.fastfood;
    }
  }

  Color get color {
    switch (this) {
      case FoodCategory.grains:
        return Colors.brown;
      case FoodCategory.vegetables:
        return Colors.green;
      case FoodCategory.fruits:
        return Colors.red;
      case FoodCategory.proteins:
        return Colors.purple;
      case FoodCategory.dairy:
        return Colors.blue;
      case FoodCategory.fats:
        return Colors.yellow;
      case FoodCategory.beverages:
        return Colors.cyan;
      case FoodCategory.sweets:
        return Colors.pink;
      case FoodCategory.other:
        return Colors.grey;
    }
  }
}

/// Model for individual food entries
class FoodEntry {
  final String id;
  final String name;
  final double carbohydrates; // grams
  final double calories; // kcal
  final double protein; // grams
  final double fat; // grams
  final double fiber; // grams
  final GlycemicIndex glycemicIndex;
  final FoodCategory category;
  final String portion; // e.g., "1 cup", "100g", "1 slice"
  final double portionSize; // numerical value for calculations
  final String unit; // e.g., "g", "ml", "pieces"
  final MealType mealType;
  final DateTime timestamp;
  final String? notes;
  final String? brand;
  final String? barcode; // barcode for scanned products
  final bool isCustom; // true if user-added, false if from database
  final DateTime createdAt;
  final DateTime updatedAt;

  const FoodEntry({
    required this.id,
    required this.name,
    required this.carbohydrates,
    required this.calories,
    required this.protein,
    required this.fat,
    required this.fiber,
    required this.glycemicIndex,
    required this.category,
    required this.portion,
    required this.portionSize,
    required this.unit,
    required this.mealType,
    required this.timestamp,
    this.notes,
    this.brand,
    this.barcode,
    this.isCustom = false,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Calculate carbs per 100g for standardization
  double get carbsPer100g {
    if (portionSize == 0) return 0;
    return (carbohydrates / portionSize) * 100;
  }

  /// Calculate calories per 100g for standardization
  double get caloriesPer100g {
    if (portionSize == 0) return 0;
    return (calories / portionSize) * 100;
  }

  /// Get display name with brand if available
  String get displayName {
    if (brand != null && brand!.isNotEmpty) {
      return '$brand $name';
    }
    return name;
  }

  /// Get formatted portion display
  String get portionDisplay {
    return '$portionSize$unit ($portion)';
  }

  /// Check if this is a diabetes-friendly food (low carbs and low GI)
  bool get isDiabetesFriendly {
    return carbohydrates <= 15 && glycemicIndex == GlycemicIndex.low;
  }

  /// Get blood sugar impact level based on carbs and GI
  BloodSugarImpact get bloodSugarImpact {
    if (carbohydrates <= 15 && glycemicIndex == GlycemicIndex.low) {
      return BloodSugarImpact.low;
    } else if (carbohydrates <= 30 && glycemicIndex != GlycemicIndex.high) {
      return BloodSugarImpact.moderate;
    } else {
      return BloodSugarImpact.high;
    }
  }

  FoodEntry copyWith({
    String? id,
    String? name,
    double? carbohydrates,
    double? calories,
    double? protein,
    double? fat,
    double? fiber,
    GlycemicIndex? glycemicIndex,
    FoodCategory? category,
    String? portion,
    double? portionSize,
    String? unit,
    MealType? mealType,
    DateTime? timestamp,
    String? notes,
    String? brand,
    String? barcode,
    bool? isCustom,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return FoodEntry(
      id: id ?? this.id,
      name: name ?? this.name,
      carbohydrates: carbohydrates ?? this.carbohydrates,
      calories: calories ?? this.calories,
      protein: protein ?? this.protein,
      fat: fat ?? this.fat,
      fiber: fiber ?? this.fiber,
      glycemicIndex: glycemicIndex ?? this.glycemicIndex,
      category: category ?? this.category,
      portion: portion ?? this.portion,
      portionSize: portionSize ?? this.portionSize,
      unit: unit ?? this.unit,
      mealType: mealType ?? this.mealType,
      timestamp: timestamp ?? this.timestamp,
      notes: notes ?? this.notes,
      brand: brand ?? this.brand,
      barcode: barcode ?? this.barcode,
      isCustom: isCustom ?? this.isCustom,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'carbohydrates': carbohydrates,
      'calories': calories,
      'protein': protein,
      'fat': fat,
      'fiber': fiber,
      'glycemicIndex': glycemicIndex.name,
      'category': category.name,
      'portion': portion,
      'portionSize': portionSize,
      'unit': unit,
      'mealType': mealType.name,
      'timestamp': timestamp.toIso8601String(),
      'notes': notes,
      'brand': brand,
      'barcode': barcode,
      'isCustom': isCustom,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory FoodEntry.fromJson(Map<String, dynamic> json) {
    return FoodEntry(
      id: json['id'] as String,
      name: json['name'] as String,
      carbohydrates: (json['carbohydrates'] as num).toDouble(),
      calories: (json['calories'] as num).toDouble(),
      protein: (json['protein'] as num).toDouble(),
      fat: (json['fat'] as num).toDouble(),
      fiber: (json['fiber'] as num).toDouble(),
      glycemicIndex: GlycemicIndex.values.firstWhere(
        (e) => e.name == json['glycemicIndex'],
        orElse: () => GlycemicIndex.medium,
      ),
      category: FoodCategory.values.firstWhere(
        (e) => e.name == json['category'],
        orElse: () => FoodCategory.grains,
      ),
      portion: json['portion'] as String,
      portionSize: (json['portionSize'] as num).toDouble(),
      unit: json['unit'] as String,
      mealType: MealType.values.firstWhere(
        (e) => e.name == json['mealType'],
        orElse: () => MealType.breakfast,
      ),
      timestamp: DateTime.parse(json['timestamp'] as String),
      notes: json['notes'] as String?,
      brand: json['brand'] as String?,
      barcode: json['barcode'] as String?,
      isCustom: json['isCustom'] as bool? ?? false,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }
}

/// Enum for blood sugar impact levels
enum BloodSugarImpact {
  low,
  moderate,
  high;

  String get displayName {
    switch (this) {
      case BloodSugarImpact.low:
        return 'Low Impact';
      case BloodSugarImpact.moderate:
        return 'Moderate Impact';
      case BloodSugarImpact.high:
        return 'High Impact';
    }
  }

  Color get color {
    switch (this) {
      case BloodSugarImpact.low:
        return Colors.green;
      case BloodSugarImpact.moderate:
        return Colors.orange;
      case BloodSugarImpact.high:
        return Colors.red;
    }
  }

  IconData get icon {
    switch (this) {
      case BloodSugarImpact.low:
        return Icons.trending_flat;
      case BloodSugarImpact.moderate:
        return Icons.trending_up;
      case BloodSugarImpact.high:
        return Icons.trending_up;
    }
  }
}
