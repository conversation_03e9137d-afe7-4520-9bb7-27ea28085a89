// Load env vars FIRST before any other imports
import dotenv from 'dotenv';
dotenv.config();

import express, { Request, Response } from 'express';
import cors from 'cors';
import connectDB from './config/db';
import authRoutes from './routes/auth';
import glucoseRoutes from './routes/glucose';
import medicationRoutes from './routes/medication';
import foodDiaryRoutes from './routes/foodDiary';
import customMealTimeRoutes from './routes/customMealTime';
import healthRoutes from './routes/health';
import debugRoutes from './routes/debug';
import { env } from './config/env';
import { errorHandler, notFoundHandler, gracefulShutdown } from './utils/errorHandler';
import { startMedicationNotificationService, stopMedicationNotificationService } from './services/medicationNotification';
import logger from './utils/logger';
import requestIdMiddleware from './middleware/requestId';
import { httpLoggerMiddleware, securityLoggerMiddleware, errorResponseLogger } from './middleware/httpLogger';
import { debugMiddleware } from './utils/debugger';
import { initializeDevelopmentConfig, DevUtils } from './config/development';

// Initialize development configuration
initializeDevelopmentConfig();

// Connect to database
connectDB();

const app = express();

// Trust proxy for accurate IP addresses
app.set('trust proxy', 1);

// Request ID middleware (must be first)
app.use(requestIdMiddleware);

// HTTP logging middleware
app.use(httpLoggerMiddleware);

// Security logging middleware
app.use(securityLoggerMiddleware);

// Error response logging middleware
app.use(errorResponseLogger);

// Debug middleware (development only)
if (DevUtils.isDevelopment()) {
    app.use(debugMiddleware);
}

// Body parser with size limits
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Enable CORS with enhanced configuration
app.use(cors({
    origin: env.NODE_ENV === 'production'
        ? ['https://yourdomain.com'] // Replace with actual production domains
        : true, // Allow all origins in development
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Request-ID']
}));

// Root endpoint
app.get('/', (_req: Request, res: Response) => {
    logger.info('Root endpoint accessed');
    res.status(200).json({
        success: true,
        message: 'GlucoMonitor Backend API',
        version: process.env.npm_package_version || '1.0.0',
        environment: env.NODE_ENV,
        documentation: '/api/docs',
        health: '/api/health',
        timestamp: new Date().toISOString()
    });
});

// Mount API routes
logger.info('Registering API routes...');

app.use('/api/auth', authRoutes);
app.use('/api/glucose', glucoseRoutes);
app.use('/api/medication', medicationRoutes);
app.use('/api/food-diary', foodDiaryRoutes);
app.use('/api/custom-meal-times', customMealTimeRoutes);
app.use('/api/health', healthRoutes);
app.use('/api/debug', debugRoutes);

logger.info('API routes registered successfully');

// 404 handler for unmatched routes
app.use(notFoundHandler);

// Global error handler (must be last)
app.use(errorHandler);

// Start server
const server = app.listen(env.PORT, '0.0.0.0', () => {
    logger.info(`Server started successfully`, {
        port: env.PORT,
        environment: env.NODE_ENV,
        host: '0.0.0.0',
        pid: process.pid,
        nodeVersion: process.version
    });

    // Start medication notification service
    try {
        startMedicationNotificationService();
        logger.info('Medication notification service started');
    } catch (error) {
        logger.error('Failed to start medication notification service', {
            error: (error as Error).message
        });
    }
});

// Enhanced graceful shutdown handling
const shutdown = gracefulShutdown(server);
process.on('SIGTERM', () => {
    logger.info('SIGTERM received, initiating graceful shutdown...');
    stopMedicationNotificationService();
    shutdown('SIGTERM');
});

process.on('SIGINT', () => {
    logger.info('SIGINT received, initiating graceful shutdown...');
    stopMedicationNotificationService();
    shutdown('SIGINT');
});

// Handle server errors
server.on('error', (error: any) => {
    if (error.code === 'EADDRINUSE') {
        logger.error(`Port ${env.PORT} is already in use`);
        process.exit(1);
    } else {
        logger.error('Server error', { error: error.message });
        process.exit(1);
    }
});

// Log successful startup
logger.info('GlucoMonitor Backend API initialized successfully', {
    version: process.env.npm_package_version || '1.0.0',
    environment: env.NODE_ENV,
    features: [
        'Enhanced MongoDB connection with retry logic',
        'Comprehensive error handling and logging',
        'Health monitoring endpoints',
        'Request tracking and correlation',
        'Security logging',
        'Graceful shutdown handling'
    ]
});

// Export app for testing
export default app;
