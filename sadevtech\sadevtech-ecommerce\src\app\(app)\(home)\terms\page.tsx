const Page = () => {
    return (
      <div className="max-w-3xl mx-auto py-12 px-4 text-[var(--primary-foreground)] bg-[var(--background)]">
        <h1 className="text-3xl font-bold mb-6">Terms and Conditions</h1>
        <p className="mb-4">
          Welcome to sadevtech.co.za. By accessing or using our platform, you agree to be bound by the terms and conditions outlined below. Please read them carefully.
        </p>
  
        <h2 className="text-xl font-semibold mt-6 mb-2">1. Acceptance of Terms</h2>
        <p className="mb-4">
          By creating an account, listing products, or purchasing from vendors on our platform, you agree to comply with and be legally bound by these Terms of Service.
        </p>
  
        <h2 className="text-xl font-semibold mt-6 mb-2">2. Vendor Responsibilities</h2>
        <p className="mb-4">
          Vendors are responsible for the accuracy of their listings, fulfillment of orders, and compliance with local business laws including POPIA and tax obligations.
        </p>
  
        <h2 className="text-xl font-semibold mt-6 mb-2">3. Platform Fees</h2>
        <p className="mb-4">
          sadevtech may collect a platform commission per transaction. All fees are outlined during the vendor onboarding process.
        </p>
  
        <h2 className="text-xl font-semibold mt-6 mb-2">4. Account Termination</h2>
        <p className="mb-4">
          We reserve the right to suspend or terminate accounts that violate these terms or engage in fraudulent activity.
        </p>
  
        <h2 className="text-xl font-semibold mt-6 mb-2">5. Changes to Terms</h2>
        <p className="mb-4">
          We may update these Terms occasionally. Users will be notified of changes through the platform or email.
        </p>
  
        <p className="mt-8 italic">Last updated: June 2025</p>
      </div>
    );
  };
  
  export default Page;
  