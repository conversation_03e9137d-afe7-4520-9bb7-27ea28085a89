# Development Dockerfile for GlucoMonitor Backend
FROM node:20-alpine

# Install security updates and development tools
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl git && \
    rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S glucomonitor -u 1001

WORKDIR /app

# Copy package files for better layer caching
COPY package*.json ./
COPY tsconfig.json ./

# Install all dependencies (including dev dependencies)
RUN npm ci --include=dev && npm cache clean --force

# Create necessary directories
RUN mkdir -p /app/logs /app/uploads /app/config && \
    chown -R glucomonitor:nodejs /app

# Change ownership to non-root user
RUN chown -R glucomonitor:nodejs /app
USER glucomonitor

# Expose the port the app runs on
EXPOSE 5000

# Expose debug port for Node.js debugging
EXPOSE 9229

# Health check for development
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:5000/api/health/live || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Use nodemon for development with hot reloading
CMD ["npm", "run", "dev"]
