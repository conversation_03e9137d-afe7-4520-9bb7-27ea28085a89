# Google Drive Integration Setup Guide

## 📋 Overview
This guide explains how to set up Google Drive integration for the GlucoMonitor export functionality.

## 🔧 Step 1: Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID

## 🔑 Step 2: Enable Google Drive API

1. In Google Cloud Console, go to **APIs & Services > Library**
2. Search for "Google Drive API"
3. Click on it and press **Enable**

## 👤 Step 3: Create Service Account

1. Go to **APIs & Services > Credentials**
2. Click **Create Credentials > Service Account**
3. Fill in the details:
   - **Service account name**: `glucomonitor-export`
   - **Service account ID**: `glucomonitor-export`
   - **Description**: `Service account for GlucoMonitor export functionality`
4. Click **Create and Continue**
5. Skip role assignment (click **Continue**)
6. Click **Done**

## 🔐 Step 4: Generate Service Account Key

1. In the **Credentials** page, find your service account
2. Click on the service account email
3. Go to the **Keys** tab
4. Click **Add Key > Create New Key**
5. Select **JSON** format
6. Click **Create**
7. The JSON key file will be downloaded automatically

## 📁 Step 5: Create Google Drive Folder

1. Go to [Google Drive](https://drive.google.com/)
2. Create a new folder called "GlucoMonitor Exports"
3. Right-click the folder and select **Share**
4. Add the service account email (from step 3) with **Editor** permissions
5. Copy the folder ID from the URL (the long string after `/folders/`)

## 🐳 Step 6: Configure for Docker (Recommended)

### Option A: Mount JSON File (Most Secure)

1. **Create secure directory:**
   ```bash
   mkdir -p ./config/google
   chmod 700 ./config/google
   ```

2. **Copy your JSON key:**
   ```bash
   cp ~/Downloads/your-service-account-key.json ./config/google/service-account-key.json
   chmod 600 ./config/google/service-account-key.json
   ```

3. **Update `.env.docker`:**
   ```env
   GOOGLE_SERVICE_ACCOUNT_KEY=/app/config/google/service-account-key.json
   GOOGLE_DRIVE_FOLDER_ID=your_folder_id_from_step_5
   ```

4. **Update `docker-compose.yml`:**
   ```yaml
   services:
     backend:
       volumes:
         - ./config/google:/app/config/google:ro
   ```

### Option B: Environment Variable

1. **Convert JSON to single line:**
   ```bash
   cat your-service-account-key.json | jq -c .
   ```

2. **Update `.env.docker`:**
   ```env
   # Comment out the file path
   # GOOGLE_SERVICE_ACCOUNT_KEY=/app/config/google/service-account-key.json
   
   # Use JSON content instead
   GOOGLE_SERVICE_ACCOUNT_JSON={"type":"service_account","project_id":"your-project",...}
   GOOGLE_DRIVE_FOLDER_ID=your_folder_id_from_step_5
   ```

## 💻 Step 7: Configure for Local Development

1. **Place JSON file in backend directory:**
   ```bash
   cp ~/Downloads/your-service-account-key.json ./backend/config/google-service-account.json
   ```

2. **Update `backend/.env`:**
   ```env
   GOOGLE_SERVICE_ACCOUNT_KEY=./config/google-service-account.json
   GOOGLE_DRIVE_FOLDER_ID=your_folder_id_from_step_5
   ```

3. **Add to `.gitignore`:**
   ```gitignore
   # Google Service Account Keys
   backend/config/google-service-account.json
   config/google/
   ```

## 🔒 Security Best Practices

### ✅ DO:
- Use file mounting in Docker (Option A)
- Set restrictive file permissions (600)
- Add JSON files to `.gitignore`
- Use separate service accounts for different environments
- Regularly rotate service account keys

### ❌ DON'T:
- Commit JSON keys to version control
- Use the same key for multiple environments
- Give unnecessary permissions to service accounts
- Store keys in public repositories

## 🧪 Step 8: Test the Integration

1. **Start your backend:**
   ```bash
   docker-compose up backend
   ```

2. **Test Google Drive export:**
   ```bash
   curl -X GET "http://localhost:5000/api/glucose/export/chart?format=pdf&destination=googledrive" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

3. **Check your Google Drive folder** for the exported file

## 🚨 Troubleshooting

### Common Issues:

1. **"Credentials not configured" error:**
   - Verify environment variables are set correctly
   - Check file path exists and is readable
   - Ensure JSON format is valid

2. **"Permission denied" error:**
   - Verify service account has access to the Drive folder
   - Check folder ID is correct
   - Ensure Drive API is enabled

3. **"File not found" error:**
   - Check the JSON key file path
   - Verify file permissions (should be readable by the app)
   - Ensure the file is mounted correctly in Docker

### Debug Commands:

```bash
# Check if file exists in Docker container
docker exec -it glucomonitor-backend ls -la /app/config/google/

# Check environment variables
docker exec -it glucomonitor-backend env | grep GOOGLE

# View backend logs
docker logs glucomonitor-backend
```

## 📝 Example JSON Key Structure

Your service account JSON should look like this:
```json
******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
```

## ✅ Verification Checklist

- [ ] Google Cloud project created
- [ ] Google Drive API enabled
- [ ] Service account created
- [ ] JSON key downloaded
- [ ] Google Drive folder created and shared
- [ ] Folder ID copied
- [ ] JSON key placed securely
- [ ] Environment variables configured
- [ ] Docker volumes mounted (if using Docker)
- [ ] Integration tested successfully
