# GlucoMonitor

A comprehensive glucose monitoring application with Flutter frontend and TypeScript backend.

## 🚀 Recent Updates

**✅ Backend TypeScript Migration Complete** - The entire backend has been successfully migrated from JavaScript to TypeScript with enhanced type safety, better developer experience, and improved maintainability.

## 📋 Project Structure

```
GlucoMonitor/
├── backend/                 # TypeScript Node.js API server
│   ├── src/                # TypeScript source code
│   ├── dist/               # Compiled JavaScript (production)
│   ├── Dockerfile          # Production Docker configuration
│   ├── docker-compose.yml  # Backend-specific Docker setup
│   ├── DOCKER.md          # Comprehensive Docker documentation
│   ├── README.md          # Backend documentation
│   └── package.json       # Dependencies and scripts
├── client/                 # Flutter mobile application
│   ├── lib/               # Dart source code
│   ├── assets/            # Images, fonts, etc.
│   ├── README.md          # Client documentation
│   └── pubspec.yaml       # Flutter dependencies
├── docker-compose.yml     # Main project Docker configuration
└── README.md             # This file
```

## 🛠 Tech Stack

### Backend (TypeScript)
- **Runtime**: Node.js 18+
- **Language**: TypeScript 5.x
- **Framework**: Express.js
- **Database**: MongoDB with Mongoose
- **Authentication**: JWT tokens
- **Caching**: Redis (with in-memory fallback)
- **SMS Service**: SMSPortal
- **Build**: TypeScript Compiler

### Frontend (Flutter)
- **Framework**: Flutter
- **Language**: Dart
- **State Management**: Provider/Riverpod
- **HTTP Client**: Dio
- **Local Storage**: SharedPreferences
- **UI**: Material Design

## 🚀 Quick Start

### Prerequisites
- **Backend**: Node.js 18+, MongoDB, Redis (optional)
- **Frontend**: Flutter SDK, Dart SDK
- **Docker**: Docker and Docker Compose (optional)

### Option 1: Docker (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd GlucoMonitor

# Set up environment variables
cp .env.example .env.docker
# Edit .env.docker with your values

# Start all services
docker-compose up --build

# Backend will be available at http://localhost:5000
```

### Option 2: Manual Setup

#### Backend Setup
```bash
cd backend

# Install dependencies
npm install

# Set up environment
cp .env.example .env
# Edit .env with your values

# Development mode (TypeScript with hot reload)
npm run dev

# Production mode (compiled JavaScript)
npm run build
npm start
```

#### Frontend Setup
```bash
cd client

# Install dependencies
flutter pub get

# Run on connected device/emulator
flutter run

# Build for production
flutter build apk  # Android
flutter build ios  # iOS
```

## 🔧 Development

### Backend Development
The backend now uses TypeScript for enhanced development experience:

```bash
cd backend

# Development with hot reload and TypeScript compilation
npm run dev

# Build TypeScript to JavaScript
npm run build

# Run compiled JavaScript
npm start

# Clean build directory
npm run clean
```

**Development Features:**
- Hot reload with nodemon
- TypeScript compilation with ts-node
- Fixed OTP "123456" for testing
- In-memory fallbacks for Redis/SMS
- Enhanced error logging

### Frontend Development
```bash
cd client

# Run in development mode
flutter run

# Hot reload is automatic in Flutter
# Press 'r' to hot reload, 'R' to hot restart
```

## 🐳 Docker Configuration

### Main Project (docker-compose.yml)
- **Backend**: TypeScript-based API server
- **Redis**: Caching and rate limiting
- **Networks**: Isolated container networking
- **Volumes**: Persistent Redis data

### Backend-Specific (backend/docker-compose.yml)
- **Production**: Compiled JavaScript
- **Development**: TypeScript with hot reload
- **Redis Integration**: Full Redis support
- **Environment Profiles**: Separate dev/prod configs

See [backend/DOCKER.md](./backend/DOCKER.md) for comprehensive Docker documentation.

## 📱 Features

### Authentication System
- **Email/Password Login** with JWT tokens
- **Phone Verification** via SMS (SMSPortal)
- **Password Reset** with secure tokens
- **Rate Limiting** to prevent abuse

### User Management
- **Profile Management** with diabetes type tracking
- **Language Preferences** (English/Afrikaans)
- **POPIA Compliance** consent tracking
- **Phone Number Verification** required

### Technical Features
- **TypeScript Backend** for type safety
- **Graceful Fallbacks** for external services
- **Redis Caching** with in-memory backup
- **MongoDB Integration** with proper schemas
- **Flutter UI** with Material Design

## 🔐 Environment Configuration

### Backend (.env)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/glucomonitor

# JWT
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=30d

# SMS Service (SMSPortal)
SMSPORTAL_CLIENT_ID=your-client-id
SMSPORTAL_API_SECRET=your-api-secret
SMSPORTAL_SENDER=your-sender-name

# Redis (optional)
REDIS_URL=redis://localhost:6379

# Server
PORT=5000
NODE_ENV=development
```

### Docker (.env.docker)
Similar to backend .env but with Docker-specific configurations:
```env
REDIS_URL=redis://redis:6379
MONGODB_URI=mongodb://your-cloud-mongodb-uri
```

## 🧪 Testing

### Backend API Testing
```bash
# Health check
curl http://localhost:5000/

# Register user (use South African phone format)
curl -X POST http://localhost:5000/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123","phoneNumber":"+27123456789","name":"Test User"}'

# Verify OTP (development uses fixed OTP: 123456)
curl -X POST http://localhost:5000/api/auth/verify-otp \
  -H "Content-Type: application/json" \
  -d '{"phoneNumber":"+27123456789","otp":"123456"}'

# Login
curl -X POST http://localhost:5000/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

### Frontend Testing
```bash
cd client
flutter test
```

## 📝 TypeScript Migration Details

### What Was Migrated
- ✅ **All JavaScript files** converted to TypeScript
- ✅ **Type annotations** added throughout codebase
- ✅ **Express.js types** properly implemented
- ✅ **Mongoose schemas** with TypeScript types
- ✅ **Redis client** updated to v5.1.1
- ✅ **Build system** configured for TypeScript
- ✅ **Docker configuration** updated

### Benefits Achieved
- **Type Safety**: Compile-time error detection
- **Better IDE Support**: Enhanced autocomplete and refactoring
- **Improved Maintainability**: Self-documenting code
- **Enhanced Developer Experience**: Better debugging and tooling
- **Future-Proof**: Modern JavaScript/TypeScript ecosystem

### Zero Breaking Changes
- All API endpoints remain the same
- Frontend integration unaffected
- Database schemas unchanged
- Authentication flow preserved

## 📚 Documentation

- [Backend README](./backend/README.md) - Detailed backend documentation
- [Backend Docker Guide](./backend/DOCKER.md) - Comprehensive Docker setup
- [Client README](./client/README.md) - Flutter app documentation

## 🤝 Contributing

### Backend (TypeScript)
1. Ensure TypeScript compilation passes: `npm run build`
2. Follow existing type annotation patterns
3. Test all endpoints after changes
4. Update documentation for API changes

### Frontend (Flutter)
1. Follow Dart/Flutter best practices
2. Test on multiple devices/screen sizes
3. Ensure proper state management
4. Update UI documentation

## 🔄 Deployment

### Production Deployment
```bash
# Build and deploy with Docker
docker-compose up -d --build

# Or manual deployment
cd backend
npm run build
npm start

cd ../client
flutter build apk
# Deploy APK to app stores
```

### Development Environment
```bash
# Backend development
cd backend && npm run dev

# Frontend development (separate terminal)
cd client && flutter run
```

## 📄 License

This project is part of the GlucoMonitor application suite for glucose monitoring and diabetes management.

---

**Note**: The backend has been successfully migrated to TypeScript with all functionality preserved and enhanced. The migration provides better type safety, improved developer experience, and future-proof architecture while maintaining full compatibility with the existing Flutter frontend.
