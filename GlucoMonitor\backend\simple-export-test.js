const axios = require('axios');

// Test configuration
const BASE_URL = 'http://localhost:5000';

// Simple test without authentication to check basic functionality
const testBasicEndpoints = async () => {
    console.log('🧪 Testing Basic Export Endpoints (No Auth Required)\n');

    // Test health endpoint
    try {
        const health = await axios.get(`${BASE_URL}/health`);
        console.log('✅ Health endpoint: OK');
        console.log(`   Server uptime: ${Math.floor(health.data.uptime)} seconds\n`);
    } catch (error) {
        console.log('❌ Health endpoint: FAILED');
        console.log(`   Error: ${error.message}\n`);
        return;
    }

    // Test export endpoint (should return 401)
    try {
        const exportTest = await axios.get(`${BASE_URL}/api/glucose/export/chart?format=json`);
        console.log('❌ Export endpoint security: FAILED (should require auth)');
    } catch (error) {
        if (error.response?.status === 401) {
            console.log('✅ Export endpoint security: OK (correctly requires authentication)');
            console.log(`   Response: ${error.response.data.message}\n`);
        } else {
            console.log('❌ Export endpoint: Unexpected error');
            console.log(`   Status: ${error.response?.status}`);
            console.log(`   Error: ${error.response?.data?.message || error.message}\n`);
        }
    }
};

// Test with a manual JWT token (if provided)
const testWithToken = async (token) => {
    console.log('🔐 Testing Export Endpoints with Authentication\n');

    const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
    };

    // Test different export formats
    const formats = ['json', 'csv'];
    const chartTypes = ['line', 'bar'];

    for (const format of formats) {
        for (const chartType of chartTypes) {
            try {
                console.log(`📊 Testing ${format.toUpperCase()} export with ${chartType} chart...`);
                
                const response = await axios.get(
                    `${BASE_URL}/api/glucose/export/chart?format=${format}&chartType=${chartType}`,
                    { headers }
                );

                if (response.status === 200) {
                    console.log(`✅ ${format.toUpperCase()} ${chartType}: SUCCESS`);
                    if (format === 'json') {
                        console.log(`   Data points: ${response.data?.data?.chartData?.length || 0}`);
                    }
                } else {
                    console.log(`❌ ${format.toUpperCase()} ${chartType}: Unexpected status ${response.status}`);
                }
            } catch (error) {
                console.log(`❌ ${format.toUpperCase()} ${chartType}: FAILED`);
                console.log(`   Status: ${error.response?.status}`);
                console.log(`   Error: ${error.response?.data?.message || error.message}`);
            }
        }
    }

    // Test bulk export
    try {
        console.log('\n📦 Testing bulk export...');
        const response = await axios.post(
            `${BASE_URL}/api/glucose/export/bulk`,
            {
                chartType: 'line',
                formats: ['json', 'csv'],
                destinations: ['download'],
                includeAllFormats: false
            },
            { headers }
        );

        if (response.status === 200) {
            console.log('✅ Bulk export: SUCCESS');
            console.log(`   Total exports: ${response.data?.data?.totalExports || 0}`);
        }
    } catch (error) {
        console.log('❌ Bulk export: FAILED');
        console.log(`   Status: ${error.response?.status}`);
        console.log(`   Error: ${error.response?.data?.message || error.message}`);
    }
};

// Test environment configuration
const testEnvironmentConfig = () => {
    console.log('🔧 Environment Configuration Check\n');

    const requiredVars = [
        'MONGODB_URI',
        'JWT_SECRET',
        'EMAIL_USER',
        'EMAIL_PASSWORD',
        'GOOGLE_DRIVE_FOLDER_ID',
        'DROPBOX_ACCESS_TOKEN'
    ];

    console.log('Environment Variables Status:');
    requiredVars.forEach(varName => {
        // We can't access process.env from here, so we'll just show what should be checked
        console.log(`   ${varName}: Should be configured in .env file`);
    });

    console.log('\n📋 Manual Verification Steps:');
    console.log('1. Check .env file exists and has all required variables');
    console.log('2. Verify Google Service Account JSON key is in place');
    console.log('3. Confirm MongoDB connection is working');
    console.log('4. Test email credentials are valid');
    console.log('5. Verify Dropbox token has proper permissions\n');
};

// Main test runner
const runTests = async () => {
    console.log('🚀 GlucoMonitor Export Functionality Test Suite');
    console.log('================================================\n');

    // Test basic endpoints
    await testBasicEndpoints();

    // Test environment config
    testEnvironmentConfig();

    // Instructions for manual testing
    console.log('🔑 Manual Testing with JWT Token:');
    console.log('================================\n');
    console.log('To test authenticated endpoints:');
    console.log('1. Get a JWT token by logging in through the app or API');
    console.log('2. Run this script with the token as an argument:');
    console.log('   node simple-export-test.js "your_jwt_token_here"');
    console.log('3. Or modify this script to include your token\n');

    // Check if token was provided as argument
    const token = process.argv[2];
    if (token && token !== 'your_jwt_token_here') {
        console.log('🔐 JWT Token provided, testing authenticated endpoints...\n');
        await testWithToken(token);
    } else {
        console.log('ℹ️  No JWT token provided, skipping authenticated tests\n');
    }

    console.log('🎯 Quick Manual Tests:');
    console.log('======================');
    console.log('1. Test health endpoint:');
    console.log('   curl http://localhost:5000/health');
    console.log('');
    console.log('2. Test export endpoint (should return 401):');
    console.log('   curl http://localhost:5000/api/glucose/export/chart');
    console.log('');
    console.log('3. Test with token (replace YOUR_TOKEN):');
    console.log('   curl -H "Authorization: Bearer YOUR_TOKEN" \\');
    console.log('        "http://localhost:5000/api/glucose/export/chart?format=json"');
    console.log('');

    console.log('✨ Test completed! Check results above for any issues.');
};

// Run the tests
runTests().catch(console.error);
