#!/bin/bash

# Network Connectivity Test Script for GlucoMonitor
# This script helps test network connectivity between client and backend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "GlucoMonitor Network Connectivity Test"
echo "======================================"

# Test 1: Check if backend is running locally
print_status "Test 1: Checking localhost backend..."
if curl -f -s http://localhost:5000/api/health > /dev/null; then
    print_success "Backend is running on localhost:5000"
    curl -s http://localhost:5000/api/health | jq '.' 2>/dev/null || curl -s http://localhost:5000/api/health
else
    print_error "Backend is not running on localhost:5000"
    print_warning "Please start the backend server first:"
    echo "  cd backend && npm run dev"
    exit 1
fi

echo ""

# Test 2: Get network interfaces
print_status "Test 2: Network interface information..."
if command -v ip &> /dev/null; then
    # Linux
    ip addr show | grep "inet " | grep -v "127.0.0.1" | awk '{print $2}' | cut -d'/' -f1
elif command -v ifconfig &> /dev/null; then
    # macOS
    ifconfig | grep "inet " | grep -v "127.0.0.1" | awk '{print $2}'
else
    print_warning "Cannot determine network interfaces automatically"
    print_status "Please run 'ipconfig' (Windows) or 'ifconfig' (macOS/Linux) to find your IP"
fi

echo ""

# Test 3: Test network accessibility
print_status "Test 3: Testing network accessibility..."

# Get the current IP from common network ranges
for ip in $(hostname -I 2>/dev/null || ifconfig | grep "inet " | grep -v "127.0.0.1" | awk '{print $2}' | head -3); do
    print_status "Testing backend accessibility on $ip:5000..."
    
    if curl -f -s http://$ip:5000/api/health > /dev/null; then
        print_success "Backend is accessible on $ip:5000"
        echo "Use this URL in your Flutter app:"
        echo "BACKEND_URL=http://$ip:5000/api"
    else
        print_warning "Backend is not accessible on $ip:5000"
    fi
done

echo ""

# Test 4: Port availability
print_status "Test 4: Checking port 5000 availability..."
if command -v netstat &> /dev/null; then
    netstat -an | grep ":5000 " | head -5
elif command -v ss &> /dev/null; then
    ss -tuln | grep ":5000 "
else
    print_warning "Cannot check port status automatically"
fi

echo ""

# Test 5: Firewall check
print_status "Test 5: Firewall recommendations..."
print_warning "If the backend is not accessible from other devices:"
echo "1. Check Windows Firewall (Windows)"
echo "2. Check iptables/ufw (Linux)"
echo "3. Check System Preferences > Security & Privacy > Firewall (macOS)"
echo "4. Ensure port 5000 is allowed for incoming connections"

echo ""

# Test 6: Android Emulator specific
print_status "Test 6: Android Emulator configuration..."
print_success "For Android Emulator, use: http://********:5000/api"
print_success "For iOS Simulator, use: http://localhost:5000/api"
print_success "For physical devices, use: http://YOUR_IP:5000/api"

echo ""
print_status "Network test completed!"
print_warning "Update client/.env with the correct BACKEND_URL based on the test results above."
