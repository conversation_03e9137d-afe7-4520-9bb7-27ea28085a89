import 'package:flutter/material.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../services/favorites_service.dart';

class FavoritesWidget extends StatefulWidget {
  final List<FoodEntry> favorites;
  final MealType mealType;
  final Function(FoodEntry) onFoodAdded;
  final VoidCallback onRefresh;

  const FavoritesWidget({
    super.key,
    required this.favorites,
    required this.mealType,
    required this.onFoodAdded,
    required this.onRefresh,
  });

  @override
  State<FavoritesWidget> createState() => _FavoritesWidgetState();
}

class _FavoritesWidgetState extends State<FavoritesWidget>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<FoodEntry> _frequentFoods = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _loadFrequentFoods();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadFrequentFoods() async {
    setState(() => _isLoading = true);
    try {
      final frequent = await FavoritesService.getFrequentFoods();
      setState(() => _frequentFoods = frequent);
    } catch (e) {
      debugPrint('Error loading frequent foods: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Tab bar
        TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [Tab(text: 'Favorites'), Tab(text: 'Frequent')],
        ),

        // Tab content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [_buildFavoritesTab(), _buildFrequentTab()],
          ),
        ),
      ],
    );
  }

  Widget _buildFavoritesTab() {
    if (widget.favorites.isEmpty) {
      return _buildEmptyState(
        icon: Icons.favorite_border,
        title: 'No Favorites Yet',
        message: 'Add foods to your favorites for quick access',
        actionText: 'Browse Foods',
        onAction: () {
          // Close modal and let user navigate to search
          Navigator.of(context).pop();
        },
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.favorite, color: Colors.red, size: 20),
              const SizedBox(width: 8),
              Text(
                '${widget.favorites.length} Favorite Foods',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: widget.onRefresh,
                icon: const Icon(Icons.refresh, size: 20),
                tooltip: 'Refresh',
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: widget.favorites.length,
              itemBuilder: (context, index) {
                final food = widget.favorites[index];
                return _buildFoodTile(food, isFavorite: true);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFrequentTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_frequentFoods.isEmpty) {
      return _buildEmptyState(
        icon: Icons.trending_up,
        title: 'No Frequent Foods',
        message: 'Foods you eat regularly will appear here',
        actionText: 'Add Some Foods',
        onAction: () {
          // Close modal and let user navigate to search
          Navigator.of(context).pop();
        },
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.trending_up, color: AppColors.primary, size: 20),
              const SizedBox(width: 8),
              Text(
                '${_frequentFoods.length} Frequently Eaten',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.onSurface,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: _loadFrequentFoods,
                icon: const Icon(Icons.refresh, size: 20),
                tooltip: 'Refresh',
              ),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: ListView.builder(
              itemCount: _frequentFoods.length,
              itemBuilder: (context, index) {
                final food = _frequentFoods[index];
                return _buildFoodTile(food, isFavorite: false);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFoodTile(FoodEntry food, {required bool isFavorite}) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Icon(
            _getFoodIcon(food.category),
            color: AppColors.primary,
            size: 20,
          ),
        ),
        title: Text(
          food.name,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: AppColors.onSurface,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${food.calories.toInt()} cal • ${food.carbohydrates.toInt()}g carbs',
              style: const TextStyle(color: AppColors.textSecondary),
            ),
            if (food.portion.isNotEmpty)
              Text(
                'Portion: ${food.portion}',
                style: TextStyle(
                  color: AppColors.textSecondary.withValues(alpha: 0.8),
                  fontSize: 12,
                ),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (isFavorite)
              IconButton(
                onPressed: () => _removeFromFavorites(food),
                icon: const Icon(Icons.favorite, color: Colors.red, size: 20),
                tooltip: 'Remove from favorites',
              )
            else
              IconButton(
                onPressed: () => _addToFavorites(food),
                icon: const Icon(Icons.favorite_border, size: 20),
                tooltip: 'Add to favorites',
              ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () => widget.onFoodAdded(food),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                minimumSize: const Size(60, 36),
              ),
              child: const Text('Add'),
            ),
          ],
        ),
        isThreeLine: food.portion.isNotEmpty,
      ),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String message,
    required String actionText,
    required VoidCallback onAction,
  }) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              size: 64,
              color: AppColors.textSecondary.withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: AppColors.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              message,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.textSecondary.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            OutlinedButton.icon(
              onPressed: onAction,
              icon: const Icon(Icons.search),
              label: Text(actionText),
              style: OutlinedButton.styleFrom(
                foregroundColor: AppColors.primary,
                side: const BorderSide(color: AppColors.primary),
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 12,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  IconData _getFoodIcon(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruits:
        return Icons.apple;
      case FoodCategory.vegetables:
        return Icons.eco;
      case FoodCategory.grains:
        return Icons.grain;
      case FoodCategory.proteins:
        return Icons.egg;
      case FoodCategory.dairy:
        return Icons.local_drink;
      case FoodCategory.beverages:
        return Icons.local_cafe;
      case FoodCategory.fats:
        return Icons.opacity;
      case FoodCategory.sweets:
        return Icons.cookie;
      case FoodCategory.other:
        return Icons.fastfood;
    }
  }

  Future<void> _addToFavorites(FoodEntry food) async {
    try {
      final success = await FavoritesService.addToFavorites(food);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Added ${food.name} to favorites'),
            backgroundColor: Colors.green,
          ),
        );
        widget.onRefresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to add to favorites: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _removeFromFavorites(FoodEntry food) async {
    try {
      final success = await FavoritesService.removeFromFavorites(food.id);
      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Removed ${food.name} from favorites'),
            backgroundColor: Colors.orange,
          ),
        );
        widget.onRefresh();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to remove from favorites: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
