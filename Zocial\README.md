# Zocial

A Flutter application with Firebase authentication integration.

## Project Overview

Zocial is a mobile application built with Flutter that implements Firebase Authentication for user management. The application follows clean architecture principles to maintain separation of concerns and improve testability.

## Project Structure

The project follows a feature-based structure with clean architecture principles:

```
lib/
├── features/
│   └── auth/
│       ├── data/
│       │   └── firebase_auth_repo.dart (Implementation of auth repository)
│       └── domain/
│           ├── entities/
│           │   └── app_user.dart (User entity model)
│           └── repos/
│               └── auth_repo.dart (Auth repository interface)
├── firebase_options.dart (Firebase configuration)
└── main.dart (Application entry point)
```

## Implementation Steps

### 1. Project Setup

1. Created a new Flutter project using `flutter create zocial`
2. Set up Firebase project in Firebase Console
3. Integrated Firebase using FlutterFire CLI with `flutterfire configure`
4. Added Firebase dependencies to pubspec.yaml:
   - firebase_core
   - firebase_auth

### 2. Authentication Implementation

#### Domain Layer

1. Created the `AppUser` entity class to represent authenticated users:
   - Properties: uid, email, name
   - JSON serialization methods for data persistence

2. Defined the `AuthRepo` abstract class with authentication operations:
   - Login with email and password
   - Register with email and password
   - Logout
   - Get current user

#### Data Layer

1. Implemented `FirebaseAuthRepo` that implements the `AuthRepo` interface:
   - Used Firebase Authentication for user management
   - Mapped Firebase User objects to our domain AppUser objects
   - Implemented error handling for authentication operations

### 3. Firebase Configuration

1. Generated Firebase configuration using FlutterFire CLI
2. Modified `firebase_options.dart` to handle platform-specific configurations
3. Removed macOS and Windows platform support as they're not needed
4. Initialized Firebase in the main.dart file

### 4. Application Entry Point

1. Set up the main.dart file to initialize Firebase before running the app
2. Created a basic MaterialApp with minimal UI

## Next Steps

- Implement UI for authentication screens
- Add state management for authentication flow
- Implement user profile management
- Add additional Firebase services (Firestore, Storage, etc.)

## Getting Started

### Prerequisites

- Flutter SDK
- Firebase account
- Android Studio / VS Code

### Setup

1. Clone the repository
2. Run `flutter pub get` to install dependencies
3. Ensure Firebase configuration is set up correctly
4. Run the app with `flutter run`

## Resources

- [Flutter Documentation](https://docs.flutter.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [FlutterFire Documentation](https://firebase.flutter.dev/docs/overview/)
