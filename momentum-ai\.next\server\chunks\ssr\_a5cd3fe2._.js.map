{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Sentebale/momentum-ai/src/app/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useSession, signIn } from 'next-auth/react'\nimport { Button } from '@/components/ui/Button'\nimport { useRouter } from 'next/navigation'\nimport { useEffect } from 'react'\n\nexport default function Home() {\n  const { data: session, status } = useSession()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (session) {\n      router.push('/dashboard')\n    }\n  }, [session, router])\n\n  if (status === 'loading') {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"></div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20\">\n        <div className=\"text-center\">\n          {/* Hero Section */}\n          <h1 className=\"text-4xl md:text-6xl font-bold text-gray-900 mb-6\">\n            Build Better Habits with{' '}\n            <span className=\"text-blue-600\">AI</span>\n          </h1>\n          <p className=\"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\">\n            Momentum AI creates personalized habits based on your goals and lifestyle.\n            Track your progress, build streaks, and transform your life one habit at a time.\n          </p>\n\n          {/* CTA Button */}\n          <div className=\"mb-16\">\n            <Button\n              onClick={() => signIn('google')}\n              size=\"lg\"\n              className=\"bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 text-lg\"\n            >\n              Get Started with Google\n            </Button>\n          </div>\n\n          {/* Features */}\n          <div className=\"grid md:grid-cols-3 gap-8 mt-20\">\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"text-3xl mb-4\">🤖</div>\n              <h3 className=\"text-xl font-semibold mb-2\">AI-Powered Generation</h3>\n              <p className=\"text-gray-600\">\n                Tell us about yourself and let AI create personalized habits that fit your lifestyle.\n              </p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"text-3xl mb-4\">📊</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Track Progress</h3>\n              <p className=\"text-gray-600\">\n                Monitor your habits with beautiful visualizations and streak tracking.\n              </p>\n            </div>\n            <div className=\"bg-white p-6 rounded-lg shadow-md\">\n              <div className=\"text-3xl mb-4\">🎯</div>\n              <h3 className=\"text-xl font-semibold mb-2\">Achieve Goals</h3>\n              <p className=\"text-gray-600\">\n                Build lasting habits that help you reach your personal and professional goals.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,MAAM,OAAO,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD;IAC3C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAS;KAAO;IAEpB,IAAI,WAAW,WAAW;QACxB,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAG,WAAU;;4BAAoD;4BACvC;0CACzB,8OAAC;gCAAK,WAAU;0CAAgB;;;;;;;;;;;;kCAElC,8OAAC;wBAAE,WAAU;kCAA+C;;;;;;kCAM5D,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;4BACtB,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;kCAMH,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,8OAAC;wCAAG,WAAU;kDAA6B;;;;;;kDAC3C,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Sentebale/momentum-ai/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}