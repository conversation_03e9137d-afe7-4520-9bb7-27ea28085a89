import mongoose, { Document, Schema } from 'mongoose';

// Interface for custom meal time document
export interface ICustomMealTimeDocument extends Document {
    id: string;
    name: string;
    displayName: string;
    icon: string;
    color: string;
    defaultTime: string;
    timeRange: {
        start: string;
        end: string;
    };
    isActive: boolean;
    userId: string;
    createdAt: Date;
    updatedAt: Date;
}

// Custom meal time schema
const customMealTimeSchema = new Schema<ICustomMealTimeDocument>({
    name: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    displayName: {
        type: String,
        required: true,
        trim: true,
        maxlength: 100
    },
    icon: {
        type: String,
        required: true,
        trim: true,
        maxlength: 50
    },
    color: {
        type: String,
        required: true,
        trim: true,
        match: /^#[0-9A-F]{6}$/i // Hex color validation
    },
    defaultTime: {
        type: String,
        required: true,
        match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/ // HH:mm format validation
    },
    timeRange: {
        start: {
            type: String,
            required: true,
            match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
        },
        end: {
            type: String,
            required: true,
            match: /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/
        }
    },
    isActive: {
        type: Boolean,
        default: true
    },
    userId: {
        type: String,
        required: true
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for efficient queries
customMealTimeSchema.index({ userId: 1, isActive: 1 });
customMealTimeSchema.index({ userId: 1, name: 1 }, { unique: true });

// Virtual for formatted time range
customMealTimeSchema.virtual('formattedTimeRange').get(function(this: ICustomMealTimeDocument) {
    return `${this.timeRange.start} - ${this.timeRange.end}`;
});

// Method to check if a time falls within this meal's range
customMealTimeSchema.methods.isTimeInRange = function(this: ICustomMealTimeDocument, time: string): boolean {
    const [hours, minutes] = time.split(':').map(Number);
    const timeMinutes = hours * 60 + minutes;

    const [startHours, startMinutes] = this.timeRange.start.split(':').map(Number);
    const startTimeMinutes = startHours * 60 + startMinutes;

    const [endHours, endMinutes] = this.timeRange.end.split(':').map(Number);
    let endTimeMinutes = endHours * 60 + endMinutes;

    // Handle overnight ranges (e.g., 22:00 - 02:00)
    if (endTimeMinutes < startTimeMinutes) {
        endTimeMinutes += 24 * 60; // Add 24 hours
        if (timeMinutes < startTimeMinutes) {
            return timeMinutes + 24 * 60 >= startTimeMinutes && timeMinutes + 24 * 60 <= endTimeMinutes;
        }
    }

    return timeMinutes >= startTimeMinutes && timeMinutes <= endTimeMinutes;
};

// Static method to get default meal times for a user
customMealTimeSchema.statics.getDefaultMealTimes = function(userId: string) {
    const { DEFAULT_MEAL_TIMES, MealType } = require('./FoodEntry');
    
    return Object.entries(DEFAULT_MEAL_TIMES).map(([mealType, config]: [string, any]) => ({
        name: mealType,
        displayName: config.displayName,
        icon: config.icon,
        color: config.color,
        defaultTime: config.defaultTime,
        timeRange: config.timeRange,
        isActive: true,
        userId
    }));
};

// Pre-save middleware to validate time range
customMealTimeSchema.pre('save', function(next) {
    const [startHours, startMinutes] = this.timeRange.start.split(':').map(Number);
    const [endHours, endMinutes] = this.timeRange.end.split(':').map(Number);
    
    const startTimeMinutes = startHours * 60 + startMinutes;
    let endTimeMinutes = endHours * 60 + endMinutes;
    
    // Allow overnight ranges
    if (endTimeMinutes < startTimeMinutes) {
        // This is an overnight range, which is valid
    }
    
    next();
});

export const CustomMealTime = mongoose.model<ICustomMealTimeDocument>('CustomMealTime', customMealTimeSchema);
export default CustomMealTime;
