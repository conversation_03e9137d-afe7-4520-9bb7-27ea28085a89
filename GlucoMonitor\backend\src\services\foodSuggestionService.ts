import mongoose from 'mongoose';
import FoodDatabase, { IFoodDatabaseEntry } from '../models/FoodDatabase';
import FoodEntry, { FoodCategory, GlycemicIndex, MealType } from '../models/FoodEntry';
import DailyNutrition from '../models/DailyNutrition';
import GlucoseReading from '../models/GlucoseReading';
import User, { IUser } from '../models/User';
import FoodRecognitionHistory, { RecognitionStatus } from '../models/FoodRecognitionHistory';

// Suggestion types
export enum SuggestionType {
    BLOOD_SUGAR_FRIENDLY = 'blood_sugar_friendly',
    NUTRITIONAL_BALANCE = 'nutritional_balance',
    HISTORICAL_PREFERENCE = 'historical_preference',
    CULTURAL_PREFERENCE = 'cultural_preference',
    SEASONAL_AVAILABILITY = 'seasonal_availability',
    MEAL_COMPLETION = 'meal_completion',
    DIABETES_MANAGEMENT = 'diabetes_management',
    WEIGHT_MANAGEMENT = 'weight_management'
}

// Suggestion algorithm weights
interface AlgorithmWeights {
    bloodSugarImpact: number;
    nutritionalBalance: number;
    userPreference: number;
    culturalRelevance: number;
    seasonality: number;
    diabetesManagement: number;
    previousSuccess: number;
}

// Food suggestion interface
export interface IFoodSuggestion {
    food: IFoodDatabaseEntry;
    score: number;
    reasons: string[];
    suggestionType: SuggestionType;
    confidence: number;
    nutritionalBenefits: string[];
    potentialConcerns: string[];
    recommendedPortion: {
        amount: number;
        unit: string;
        description: string;
    };
    mealTiming?: {
        optimal: string[];
        avoid: string[];
    };
    bloodSugarPrediction?: {
        expectedImpact: 'low' | 'moderate' | 'high';
        peakTime: number; // minutes after consumption
        duration: number; // minutes
    };
}

// Suggestion context
export interface ISuggestionContext {
    userId: mongoose.Types.ObjectId;
    mealType: MealType;
    currentTime: Date;
    recentBloodSugar?: number;
    targetBloodSugarRange?: { min: number; max: number };
    remainingCalories?: number;
    remainingCarbs?: number;
    dietaryRestrictions?: string[];
    preferredCategories?: FoodCategory[];
    excludeCategories?: FoodCategory[];
    maxSuggestions?: number;
}

export class FoodSuggestionService {
    
    /**
     * Generate personalized food suggestions based on multiple algorithms
     */
    static async generateSuggestions(context: ISuggestionContext): Promise<IFoodSuggestion[]> {
        try {
            // Get user profile and preferences
            const user = await User.findById(context.userId);
            if (!user) throw new Error('User not found');
            
            // Get user's food history and patterns
            const userHistory = await this.getUserFoodHistory(context.userId);
            const bloodSugarPatterns = await this.getBloodSugarPatterns(context.userId);
            const nutritionalNeeds = await this.getNutritionalNeeds(context.userId, context.mealType);
            
            // Calculate algorithm weights based on user profile
            const weights = this.calculateAlgorithmWeights(user, context);
            
            // Generate suggestions using different algorithms
            const suggestions: IFoodSuggestion[] = [];
            
            // 1. Blood sugar friendly suggestions
            const bloodSugarSuggestions = await this.getBloodSugarFriendlySuggestions(context, bloodSugarPatterns, weights);
            suggestions.push(...bloodSugarSuggestions);
            
            // 2. Nutritional balance suggestions
            const nutritionalSuggestions = await this.getNutritionalBalanceSuggestions(context, nutritionalNeeds, weights);
            suggestions.push(...nutritionalSuggestions);
            
            // 3. Historical preference suggestions
            const preferenceSuggestions = await this.getHistoricalPreferenceSuggestions(context, userHistory, weights);
            suggestions.push(...preferenceSuggestions);
            
            // 4. Cultural preference suggestions
            const culturalSuggestions = await this.getCulturalPreferenceSuggestions(context, user, weights);
            suggestions.push(...culturalSuggestions);
            
            // 5. Seasonal availability suggestions
            const seasonalSuggestions = await this.getSeasonalSuggestions(context, weights);
            suggestions.push(...seasonalSuggestions);
            
            // 6. Meal completion suggestions
            const mealCompletionSuggestions = await this.getMealCompletionSuggestions(context, weights);
            suggestions.push(...mealCompletionSuggestions);
            
            // Remove duplicates and rank suggestions
            const uniqueSuggestions = this.removeDuplicatesAndRank(suggestions);
            
            // Apply final filtering and sorting
            const filteredSuggestions = this.applyFinalFiltering(uniqueSuggestions, context);
            
            // Limit to requested number of suggestions
            const maxSuggestions = context.maxSuggestions || 10;
            return filteredSuggestions.slice(0, maxSuggestions);
            
        } catch (error) {
            console.error('Error generating food suggestions:', error);
            throw new Error('Failed to generate food suggestions');
        }
    }
    
    /**
     * Get blood sugar friendly food suggestions
     */
    private static async getBloodSugarFriendlySuggestions(
        context: ISuggestionContext,
        bloodSugarPatterns: any,
        weights: AlgorithmWeights
    ): Promise<IFoodSuggestion[]> {
        const suggestions: IFoodSuggestion[] = [];
        
        // Find foods with low glycemic index and low carb content
        const lowGIFoods = await FoodDatabase.find({
            isVerified: true,
            glycemicIndex: GlycemicIndex.LOW,
            carbohydratesPer100g: { $lte: 15 }
        }).limit(20);
        
        for (const food of lowGIFoods) {
            const score = this.calculateBloodSugarScore(food, context, bloodSugarPatterns) * weights.bloodSugarImpact;
            
            if (score > 0.6) {
                suggestions.push({
                    food,
                    score,
                    reasons: [
                        'Low glycemic index for stable blood sugar',
                        'Low carbohydrate content',
                        'Suitable for diabetes management'
                    ],
                    suggestionType: SuggestionType.BLOOD_SUGAR_FRIENDLY,
                    confidence: score,
                    nutritionalBenefits: this.getNutritionalBenefits(food),
                    potentialConcerns: this.getPotentialConcerns(food),
                    recommendedPortion: this.calculateRecommendedPortion(food, context),
                    bloodSugarPrediction: this.predictBloodSugarImpact(food)
                });
            }
        }
        
        return suggestions;
    }
    
    /**
     * Get nutritional balance suggestions
     */
    private static async getNutritionalBalanceSuggestions(
        context: ISuggestionContext,
        nutritionalNeeds: any,
        weights: AlgorithmWeights
    ): Promise<IFoodSuggestion[]> {
        const suggestions: IFoodSuggestion[] = [];
        
        // Find foods that complement current nutritional intake
        const balancingFoods = await this.findNutritionallyBalancingFoods(context, nutritionalNeeds);
        
        for (const food of balancingFoods) {
            const score = this.calculateNutritionalScore(food, nutritionalNeeds) * weights.nutritionalBalance;
            
            if (score > 0.5) {
                suggestions.push({
                    food,
                    score,
                    reasons: this.getNutritionalReasons(food, nutritionalNeeds),
                    suggestionType: SuggestionType.NUTRITIONAL_BALANCE,
                    confidence: score,
                    nutritionalBenefits: this.getNutritionalBenefits(food),
                    potentialConcerns: this.getPotentialConcerns(food),
                    recommendedPortion: this.calculateRecommendedPortion(food, context)
                });
            }
        }
        
        return suggestions;
    }
    
    /**
     * Get historical preference suggestions
     */
    private static async getHistoricalPreferenceSuggestions(
        context: ISuggestionContext,
        userHistory: any,
        weights: AlgorithmWeights
    ): Promise<IFoodSuggestion[]> {
        const suggestions: IFoodSuggestion[] = [];
        
        // Analyze user's food preferences from history
        const preferredCategories = userHistory.preferredCategories || [];
        const frequentFoods = userHistory.frequentFoods || [];
        
        // Find similar foods to user's preferences
        const similarFoods = await this.findSimilarFoods(frequentFoods, preferredCategories);
        
        for (const food of similarFoods) {
            const score = this.calculatePreferenceScore(food, userHistory) * weights.userPreference;
            
            if (score > 0.4) {
                suggestions.push({
                    food,
                    score,
                    reasons: [
                        'Based on your food history',
                        'Similar to foods you enjoy',
                        'Matches your preferences'
                    ],
                    suggestionType: SuggestionType.HISTORICAL_PREFERENCE,
                    confidence: score,
                    nutritionalBenefits: this.getNutritionalBenefits(food),
                    potentialConcerns: this.getPotentialConcerns(food),
                    recommendedPortion: this.calculateRecommendedPortion(food, context)
                });
            }
        }
        
        return suggestions;
    }
    
    /**
     * Get cultural preference suggestions
     */
    private static async getCulturalPreferenceSuggestions(
        context: ISuggestionContext,
        user: IUser,
        weights: AlgorithmWeights
    ): Promise<IFoodSuggestion[]> {
        const suggestions: IFoodSuggestion[] = [];
        
        // Find traditional South African foods based on user's language preference
        const culturalFoods = await FoodDatabase.find({
            isVerified: true,
            isTraditionalSA: true,
            $or: [
                { 'nameTranslations.af': { $exists: true } },
                { 'nameTranslations.zu': { $exists: true } },
                { 'nameTranslations.xh': { $exists: true } }
            ]
        }).limit(15);
        
        for (const food of culturalFoods) {
            const score = this.calculateCulturalScore(food, user) * weights.culturalRelevance;
            
            if (score > 0.3) {
                suggestions.push({
                    food,
                    score,
                    reasons: [
                        'Traditional South African food',
                        'Culturally relevant',
                        'Local and familiar'
                    ],
                    suggestionType: SuggestionType.CULTURAL_PREFERENCE,
                    confidence: score,
                    nutritionalBenefits: this.getNutritionalBenefits(food),
                    potentialConcerns: this.getPotentialConcerns(food),
                    recommendedPortion: this.calculateRecommendedPortion(food, context)
                });
            }
        }
        
        return suggestions;
    }
    
    /**
     * Get seasonal availability suggestions
     */
    private static async getSeasonalSuggestions(
        context: ISuggestionContext,
        weights: AlgorithmWeights
    ): Promise<IFoodSuggestion[]> {
        const suggestions: IFoodSuggestion[] = [];
        const currentMonth = context.currentTime.getMonth() + 1;
        const currentSeason = this.getCurrentSeason(currentMonth);
        
        // Find foods that are in season
        const seasonalFoods = await FoodDatabase.find({
            isVerified: true,
            seasonality: { $in: [currentSeason, 'year_round'] }
        }).limit(10);
        
        for (const food of seasonalFoods) {
            const score = this.calculateSeasonalScore(food, currentSeason) * weights.seasonality;
            
            if (score > 0.3) {
                suggestions.push({
                    food,
                    score,
                    reasons: [
                        'Currently in season',
                        'Fresh and available',
                        'Optimal nutritional value'
                    ],
                    suggestionType: SuggestionType.SEASONAL_AVAILABILITY,
                    confidence: score,
                    nutritionalBenefits: this.getNutritionalBenefits(food),
                    potentialConcerns: this.getPotentialConcerns(food),
                    recommendedPortion: this.calculateRecommendedPortion(food, context)
                });
            }
        }
        
        return suggestions;
    }
    
    /**
     * Get meal completion suggestions
     */
    private static async getMealCompletionSuggestions(
        context: ISuggestionContext,
        weights: AlgorithmWeights
    ): Promise<IFoodSuggestion[]> {
        const suggestions: IFoodSuggestion[] = [];
        
        // Get today's food entries for the user
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const todayEntries = await FoodEntry.find({
            userId: context.userId,
            timestamp: { $gte: today },
            mealType: context.mealType
        });
        
        // Analyze what's missing from the current meal
        const mealAnalysis = this.analyzeMealCompleteness(todayEntries);
        
        // Find foods that would complete the meal
        const completingFoods = await this.findMealCompletingFoods(mealAnalysis, context.mealType);
        
        for (const food of completingFoods) {
            const score = this.calculateMealCompletionScore(food, mealAnalysis) * weights.nutritionalBalance;
            
            if (score > 0.4) {
                suggestions.push({
                    food,
                    score,
                    reasons: this.getMealCompletionReasons(food, mealAnalysis),
                    suggestionType: SuggestionType.MEAL_COMPLETION,
                    confidence: score,
                    nutritionalBenefits: this.getNutritionalBenefits(food),
                    potentialConcerns: this.getPotentialConcerns(food),
                    recommendedPortion: this.calculateRecommendedPortion(food, context)
                });
            }
        }
        
        return suggestions;
    }
    
    /**
     * Calculate algorithm weights based on user profile and context
     */
    private static calculateAlgorithmWeights(user: IUser, context: ISuggestionContext): AlgorithmWeights {
        const weights: AlgorithmWeights = {
            bloodSugarImpact: 0.3,
            nutritionalBalance: 0.25,
            userPreference: 0.2,
            culturalRelevance: 0.1,
            seasonality: 0.05,
            diabetesManagement: 0.05,
            previousSuccess: 0.05
        };
        
        // Adjust weights based on diabetes type
        if (user.diabetesType === 'Type 1' || user.diabetesType === 'Type 2') {
            weights.bloodSugarImpact = 0.4;
            weights.diabetesManagement = 0.15;
            weights.nutritionalBalance = 0.2;
        }
        
        // Adjust weights based on recent blood sugar
        if (context.recentBloodSugar && context.recentBloodSugar > 180) {
            weights.bloodSugarImpact = 0.5;
            weights.diabetesManagement = 0.2;
        }
        
        return weights;
    }
    
    // Helper methods (simplified implementations)
    private static async getUserFoodHistory(userId: mongoose.Types.ObjectId): Promise<any> {
        // Implementation would analyze user's food entry history
        const recentEntries = await FoodEntry.find({
            userId,
            timestamp: { $gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) }
        }).limit(100);

        const preferredCategories = recentEntries.reduce((acc: any, entry) => {
            acc[entry.category] = (acc[entry.category] || 0) + 1;
            return acc;
        }, {});

        const frequentFoods = recentEntries.reduce((acc: any, entry) => {
            acc[entry.name] = (acc[entry.name] || 0) + 1;
            return acc;
        }, {});

        return {
            preferredCategories: Object.keys(preferredCategories).sort((a, b) => preferredCategories[b] - preferredCategories[a]),
            frequentFoods: Object.keys(frequentFoods).sort((a, b) => frequentFoods[b] - frequentFoods[a]).slice(0, 10)
        };
    }
    
    private static async getBloodSugarPatterns(userId: mongoose.Types.ObjectId): Promise<any> {
        // Implementation would analyze blood sugar patterns
        return {};
    }
    
    private static async getNutritionalNeeds(userId: mongoose.Types.ObjectId, mealType: MealType): Promise<any> {
        // Implementation would calculate nutritional needs
        return {};
    }
    
    private static calculateBloodSugarScore(food: IFoodDatabaseEntry, context: ISuggestionContext, patterns: any): number {
        // Implementation would calculate blood sugar impact score
        return 0.8;
    }
    
    private static calculateNutritionalScore(food: IFoodDatabaseEntry, needs: any): number {
        // Implementation would calculate nutritional balance score
        return 0.7;
    }
    
    private static calculatePreferenceScore(food: IFoodDatabaseEntry, history: any): number {
        // Implementation would calculate preference score
        return 0.6;
    }
    
    private static calculateCulturalScore(food: IFoodDatabaseEntry, user: IUser): number {
        // Implementation would calculate cultural relevance score
        return 0.5;
    }
    
    private static calculateSeasonalScore(food: IFoodDatabaseEntry, season: string): number {
        // Implementation would calculate seasonal score
        return 0.4;
    }
    
    private static calculateMealCompletionScore(food: IFoodDatabaseEntry, analysis: any): number {
        // Implementation would calculate meal completion score
        return 0.6;
    }
    
    private static getNutritionalBenefits(food: IFoodDatabaseEntry): string[] {
        const benefits = [];
        if (food.fiberPer100g > 5) benefits.push('High in fiber');
        if (food.proteinPer100g > 10) benefits.push('Good protein source');
        if (food.glycemicIndex === GlycemicIndex.LOW) benefits.push('Low glycemic index');
        return benefits;
    }
    
    private static getPotentialConcerns(food: IFoodDatabaseEntry): string[] {
        const concerns = [];
        if (food.sodiumPer100g && food.sodiumPer100g > 400) concerns.push('High in sodium');
        if (food.sugarPer100g && food.sugarPer100g > 10) concerns.push('Contains added sugars');
        return concerns;
    }
    
    private static calculateRecommendedPortion(food: IFoodDatabaseEntry, context: ISuggestionContext): any {
        // Implementation would calculate optimal portion size
        return {
            amount: 100,
            unit: 'g',
            description: '1 serving'
        };
    }
    
    private static predictBloodSugarImpact(food: IFoodDatabaseEntry): any {
        // Implementation would predict blood sugar impact
        return {
            expectedImpact: 'low' as const,
            peakTime: 30,
            duration: 120
        };
    }
    
    private static removeDuplicatesAndRank(suggestions: IFoodSuggestion[]): IFoodSuggestion[] {
        // Remove duplicates and rank by score
        const unique = suggestions.filter((suggestion, index, self) => 
            index === self.findIndex(s => s.food._id.toString() === suggestion.food._id.toString())
        );
        
        return unique.sort((a, b) => b.score - a.score);
    }
    
    private static applyFinalFiltering(suggestions: IFoodSuggestion[], context: ISuggestionContext): IFoodSuggestion[] {
        // Apply dietary restrictions and other filters
        return suggestions.filter(suggestion => {
            // Filter by dietary restrictions
            if (context.dietaryRestrictions) {
                // Implementation would check dietary restrictions
            }
            
            // Filter by excluded categories
            if (context.excludeCategories && context.excludeCategories.includes(suggestion.food.category)) {
                return false;
            }
            
            return true;
        });
    }
    
    private static getCurrentSeason(month: number): string {
        // Southern Hemisphere seasons for South Africa
        if (month >= 12 || month <= 2) return 'summer';
        if (month >= 3 && month <= 5) return 'autumn';
        if (month >= 6 && month <= 8) return 'winter';
        return 'spring';
    }
    
    // Additional helper methods would be implemented here...
    private static async findNutritionallyBalancingFoods(context: ISuggestionContext, needs: any): Promise<IFoodDatabaseEntry[]> {
        return [];
    }
    
    private static async findSimilarFoods(frequentFoods: any[], categories: FoodCategory[]): Promise<IFoodDatabaseEntry[]> {
        return [];
    }
    
    private static analyzeMealCompleteness(entries: any[]): any {
        return {};
    }
    
    private static async findMealCompletingFoods(analysis: any, mealType: MealType): Promise<IFoodDatabaseEntry[]> {
        return [];
    }
    
    private static getNutritionalReasons(food: IFoodDatabaseEntry, needs: any): string[] {
        return ['Provides needed nutrients'];
    }
    
    private static getMealCompletionReasons(food: IFoodDatabaseEntry, analysis: any): string[] {
        return ['Completes your meal'];
    }
}
