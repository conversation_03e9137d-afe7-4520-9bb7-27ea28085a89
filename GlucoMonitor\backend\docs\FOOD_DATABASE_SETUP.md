# Enhanced South African Food Database Setup Guide

## Overview

This guide explains how to set up and use the Enhanced South African Food Database, which provides comprehensive multilingual food data for the GlucoMonitor application.

## Features

### 🌍 Multilingual Support
- **11 South African Languages**: English, Zulu, Afrikaans, Sesotho, Xhosa, Sepedi, Setswana, Siswati, Tshivenda, Xitsonga, Ndebele
- **Localized Food Names**: Traditional and common names in multiple languages
- **Cultural Context**: Regional availability and traditional preparation methods

### 🍽️ Comprehensive Food Database
- **Traditional SA Foods**: Pap, morogo, boerewors, biltong, samp and beans, rooibos, marula, baobab
- **International Foods**: Common foods consumed in South Africa
- **Detailed Nutrition**: Macronutrients, vitamins, minerals, glycemic index
- **Portion Information**: Common serving sizes and weights

### 🔍 Advanced Search Capabilities
- **Text Search**: Search across names, brands, descriptions
- **Multilingual Search**: Query in any supported language
- **Nutrition Filtering**: Filter by carbs, protein, calories, GI
- **Dietary Tags**: Vegan, vegetarian, gluten-free, keto-friendly, etc.
- **Origin Filtering**: Traditional SA, international, local adaptations

### 🏥 Diabetes-Specific Features
- **Glycemic Index**: Low, medium, high classifications
- **Diabetic Notes**: Specific guidance for diabetic users
- **Blood Sugar Impact**: Calculated impact levels
- **Diabetes-Friendly Filtering**: Pre-filtered low-carb, low-GI foods

## Quick Start

### 1. Database Setup

```bash
# Navigate to backend directory
cd backend

# Install dependencies
npm install

# Set up environment variables
cp .env.example .env
# Edit .env with your MongoDB connection string

# Seed the food database
npm run seed:foods
```

### 2. Verify Setup

```bash
# Start the backend server
npm run dev

# Test the API
curl http://localhost:5000/api/food-diary/foods/traditional-sa?limit=5
```

## Database Seeding Commands

### Initial Seeding
```bash
# Seed database (skips if data already exists)
npm run seed:foods
```

### Force Re-seeding
```bash
# Clear existing data and re-seed
npm run seed:foods:force
```

### Clear Database
```bash
# Remove all food data
npm run seed:foods:clear
```

## Food Data Structure

### Traditional South African Foods
```typescript
{
  name: 'Pap (Maize Porridge)',
  nameTranslations: {
    english: 'Maize Porridge',
    zulu: 'Iphutu',
    afrikaans: 'Mieliepap',
    sesotho: 'Papa',
    xhosa: 'Umphokoqo'
  },
  localNames: [
    { language: 'en', name: 'Pap', isCommon: true },
    { language: 'zu', name: 'Iphutu', isCommon: true }
  ],
  isTraditionalSA: true,
  origin: 'traditional_sa',
  region: 'Nationwide',
  // ... nutrition and other data
}
```

### International Foods
```typescript
{
  name: 'Chicken Breast (Skinless)',
  nameTranslations: {
    english: 'Chicken Breast',
    afrikaans: 'Hoenderbors',
    zulu: 'Isifuba senkukhu'
  },
  isTraditionalSA: false,
  origin: 'international',
  availability: 'year_round',
  // ... nutrition and other data
}
```

## API Usage Examples

### Basic Search
```javascript
// Search for foods containing "chicken"
const response = await fetch('/api/food-diary/search?query=chicken&limit=10');
const data = await response.json();
```

### Multilingual Search
```javascript
// Search in Zulu
const response = await fetch('/api/food-diary/search/multilingual?query=iphutu&language=zu');
const data = await response.json();
```

### Nutrition-Based Search
```javascript
// Find low-carb, high-protein foods
const response = await fetch('/api/food-diary/search/nutrition?maxCarbs=10&minProtein=20');
const data = await response.json();
```

### Dietary Filtering
```javascript
// Find vegan, gluten-free foods
const response = await fetch('/api/food-diary/foods/dietary?tags=vegan,gluten-free');
const data = await response.json();
```

## Frontend Integration

### Flutter Service Integration
```dart
class FoodDatabaseService {
  static Future<List<FoodEntry>> searchMultilingual(
    String query, 
    String? language
  ) async {
    final response = await http.get(
      Uri.parse('$baseUrl/search/multilingual')
        .replace(queryParameters: {
          'query': query,
          if (language != null) 'language': language,
        }),
      headers: await _getHeaders(),
    );
    
    if (response.statusCode == 200) {
      final data = jsonDecode(response.body);
      return (data['data'] as List)
        .map((json) => FoodEntry.fromJson(json))
        .toList();
    }
    throw Exception('Failed to search foods');
  }
}
```

### Language Detection
```dart
String detectLanguage(String query) {
  // Simple language detection based on common words
  final zuluWords = ['iphutu', 'umngqusho', 'imifino'];
  final afrikaansWords = ['mieliepap', 'boerewors', 'rooibos'];
  
  if (zuluWords.any((word) => query.toLowerCase().contains(word))) {
    return 'zu';
  }
  if (afrikaansWords.any((word) => query.toLowerCase().contains(word))) {
    return 'af';
  }
  return 'en'; // Default to English
}
```

## Database Statistics

After seeding, you'll have access to:

- **Traditional SA Foods**: ~50+ items with full multilingual support
- **International Foods**: ~100+ commonly consumed items
- **Total Nutrition Data Points**: 15+ nutrients per food
- **Multilingual Names**: 11 South African languages
- **Dietary Classifications**: 10+ dietary tags
- **Search Indexes**: Optimized for fast multilingual search

## Performance Optimization

### Indexes Created
- **Text Search Index**: Multilingual full-text search
- **Category Index**: Fast category filtering
- **Nutrition Index**: Optimized nutrition-based queries
- **Dietary Tags Index**: Quick dietary filtering
- **Origin Index**: Efficient origin-based searches

### Caching Recommendations
```javascript
// Cache frequently accessed data
const cache = new Map();

async function getCachedFoods(cacheKey, fetchFunction) {
  if (cache.has(cacheKey)) {
    return cache.get(cacheKey);
  }
  
  const data = await fetchFunction();
  cache.set(cacheKey, data);
  
  // Cache for 1 hour
  setTimeout(() => cache.delete(cacheKey), 3600000);
  
  return data;
}
```

## Troubleshooting

### Common Issues

1. **Seeding Fails**
   ```bash
   # Check MongoDB connection
   npm run seed:foods:clear
   npm run seed:foods
   ```

2. **Search Returns No Results**
   ```bash
   # Verify text indexes
   mongo
   > use glucomonitor
   > db.fooddatabases.getIndexes()
   ```

3. **Multilingual Search Issues**
   ```bash
   # Check language parameter
   curl "localhost:5000/api/food-diary/search/multilingual?query=test&language=invalid"
   # Should return error with valid language list
   ```

### Monitoring
```bash
# Check database stats
curl http://localhost:5000/api/food-diary/foods/traditional-sa?limit=1
curl http://localhost:5000/api/food-diary/search?query=test&limit=1
```

## Contributing

### Adding New Foods
1. Add food data to appropriate data file
2. Include all required multilingual fields
3. Verify nutrition data accuracy
4. Test search functionality
5. Update documentation

### Language Support
To add support for additional languages:
1. Update `ILocalizedName` interface
2. Add language to validation arrays
3. Update text search indexes
4. Add translation data for existing foods

## Support

For issues or questions:
- Check the API documentation: `backend/docs/FOOD_DATABASE_API.md`
- Review error logs in the console
- Verify environment configuration
- Test with provided examples
