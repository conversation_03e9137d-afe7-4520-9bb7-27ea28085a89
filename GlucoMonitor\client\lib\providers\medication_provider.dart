import 'package:flutter/material.dart';
import '../models/medication.dart';
import '../models/medication_reminder.dart';
import '../services/medication_service.dart';

class MedicationProvider extends ChangeNotifier {
  List<Medication> _medications = [];
  List<MedicationReminder> _todayReminders = [];
  List<MedicationReminder> _upcomingReminders = [];
  MedicationStats? _stats;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<Medication> get medications => _medications;
  List<Medication> get activeMedications =>
      _medications.where((med) => med.isActive).toList();
  List<MedicationReminder> get todayReminders => _todayReminders;
  List<MedicationReminder> get upcomingReminders => _upcomingReminders;
  MedicationStats? get stats => _stats;
  bool get isLoading => _isLoading;
  String? get error => _error;

  // Get pending reminders for today
  List<MedicationReminder> get pendingTodayReminders =>
      _todayReminders.where((r) => r.status == ReminderStatus.pending).toList();

  // Get overdue reminders
  List<MedicationReminder> get overdueReminders =>
      _todayReminders
          .where((r) => r.isOverdue && r.status == ReminderStatus.pending)
          .toList();

  // Get completed reminders for today
  List<MedicationReminder> get completedTodayReminders =>
      _todayReminders.where((r) => r.status == ReminderStatus.taken).toList();

  // Initialize provider
  Future<void> initialize() async {
    await loadMedications();
    await loadTodayReminders();
    await loadUpcomingReminders();
    await loadStats();
  }

  // Load all medications
  Future<void> loadMedications() async {
    try {
      _setLoading(true);
      _medications = await MedicationService.getMedications();
      _clearError();
    } catch (e) {
      _setError('Failed to load medications: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Load today's reminders
  Future<void> loadTodayReminders() async {
    try {
      _todayReminders = await MedicationService.getTodayReminders();
      _todayReminders.sort(
        (a, b) => a.scheduledTime.compareTo(b.scheduledTime),
      );
      notifyListeners();
    } catch (e) {
      _setError('Failed to load today\'s reminders: $e');
    }
  }

  // Load upcoming reminders
  Future<void> loadUpcomingReminders() async {
    try {
      _upcomingReminders = await MedicationService.getUpcomingReminders();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load upcoming reminders: $e');
    }
  }

  // Load medication statistics
  Future<void> loadStats() async {
    try {
      _stats = await MedicationService.getMedicationStats();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load medication stats: $e');
    }
  }

  // Add or update medication
  Future<void> saveMedication(Medication medication) async {
    try {
      _setLoading(true);
      await MedicationService.saveMedication(medication);
      await loadMedications();
      await loadTodayReminders();
      await loadUpcomingReminders();
      await loadStats();
      _clearError();
    } catch (e) {
      _setError('Failed to save medication: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Delete medication
  Future<void> deleteMedication(String medicationId) async {
    try {
      _setLoading(true);
      await MedicationService.deleteMedication(medicationId);
      await loadMedications();
      await loadTodayReminders();
      await loadUpcomingReminders();
      await loadStats();
      _clearError();
    } catch (e) {
      _setError('Failed to delete medication: $e');
      rethrow;
    } finally {
      _setLoading(false);
    }
  }

  // Get medication by ID
  Medication? getMedicationById(String id) {
    try {
      return _medications.firstWhere((med) => med.id == id);
    } catch (e) {
      return null;
    }
  }

  // Mark reminder as taken
  Future<void> markReminderAsTaken(String reminderId, {String? notes}) async {
    try {
      await MedicationService.updateReminderStatus(
        reminderId,
        ReminderStatus.taken,
        notes: notes,
      );
      await loadTodayReminders();
      await loadStats();
      _clearError();
    } catch (e) {
      _setError('Failed to mark reminder as taken: $e');
      rethrow;
    }
  }

  // Mark reminder as missed
  Future<void> markReminderAsMissed(String reminderId, {String? notes}) async {
    try {
      await MedicationService.updateReminderStatus(
        reminderId,
        ReminderStatus.missed,
        notes: notes,
      );
      await loadTodayReminders();
      await loadStats();
      _clearError();
    } catch (e) {
      _setError('Failed to mark reminder as missed: $e');
      rethrow;
    }
  }

  // Skip reminder
  Future<void> skipReminder(String reminderId, {String? notes}) async {
    try {
      await MedicationService.updateReminderStatus(
        reminderId,
        ReminderStatus.skipped,
        notes: notes,
      );
      await loadTodayReminders();
      await loadStats();
      _clearError();
    } catch (e) {
      _setError('Failed to skip reminder: $e');
      rethrow;
    }
  }

  // Toggle medication active status
  Future<void> toggleMedicationActive(String medicationId) async {
    try {
      final medication = getMedicationById(medicationId);
      if (medication != null) {
        final updatedMedication = medication.copyWith(
          isActive: !medication.isActive,
          updatedAt: DateTime.now(),
        );
        await saveMedication(updatedMedication);
      }
    } catch (e) {
      _setError('Failed to toggle medication status: $e');
      rethrow;
    }
  }

  // Toggle medication reminders
  Future<void> toggleMedicationReminders(String medicationId) async {
    try {
      final medication = getMedicationById(medicationId);
      if (medication != null) {
        final updatedMedication = medication.copyWith(
          reminderEnabled: !medication.reminderEnabled,
          updatedAt: DateTime.now(),
        );
        await saveMedication(updatedMedication);
      }
    } catch (e) {
      _setError('Failed to toggle medication reminders: $e');
      rethrow;
    }
  }

  // Get reminders for a specific medication
  List<MedicationReminder> getRemindersForMedication(String medicationId) {
    return _todayReminders
        .where((r) => r.medicationId == medicationId)
        .toList();
  }

  // Get next reminder for a medication
  MedicationReminder? getNextReminderForMedication(String medicationId) {
    final medicationReminders =
        _upcomingReminders
            .where(
              (r) =>
                  r.medicationId == medicationId &&
                  r.status == ReminderStatus.pending,
            )
            .toList();

    if (medicationReminders.isEmpty) return null;

    medicationReminders.sort(
      (a, b) => a.scheduledTime.compareTo(b.scheduledTime),
    );
    return medicationReminders.first;
  }

  // Refresh all data
  Future<void> refresh() async {
    await initialize();
  }

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  // Get adherence percentage for a specific medication
  double getMedicationAdherence(String medicationId, {int days = 7}) {
    // This would typically calculate adherence over the specified number of days
    // For now, return a placeholder calculation
    final medicationReminders =
        _todayReminders.where((r) => r.medicationId == medicationId).toList();

    if (medicationReminders.isEmpty) return 100.0;

    final takenCount =
        medicationReminders
            .where((r) => r.status == ReminderStatus.taken)
            .length;

    return (takenCount / medicationReminders.length) * 100;
  }

  // Get medication color
  Color getMedicationColor(String medicationId) {
    final medication = getMedicationById(medicationId);
    return medication?.medicationColor ?? Colors.grey;
  }
}
