import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mobile_scanner/mobile_scanner.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:http/http.dart' as http;
import '../models/food_entry.dart';
import '../constants/app_colors.dart';

class BarcodeScannerService {
  static const String _openFoodFactsBaseUrl =
      'https://world.openfoodfacts.org/api/v0/product';

  /// Request camera permission for barcode scanning
  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    return status == PermissionStatus.granted;
  }

  /// Check if camera permission is granted
  static Future<bool> hasCameraPermission() async {
    final status = await Permission.camera.status;
    return status == PermissionStatus.granted;
  }

  /// Show barcode scanner modal
  static Future<FoodEntry?> showBarcodeScanner(BuildContext context) async {
    // Check camera permission first
    final hasPermission = await hasCameraPermission();
    if (!hasPermission) {
      final granted = await requestCameraPermission();
      if (!granted) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Camera permission is required to scan barcodes'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return null;
      }
    }

    if (!context.mounted) return null;

    return await showModalBottomSheet<FoodEntry>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const BarcodeScannerModal(),
    );
  }

  /// Fetch product information from Open Food Facts API
  static Future<FoodEntry?> fetchProductInfo(String barcode) async {
    try {
      final response = await http
          .get(
            Uri.parse('$_openFoodFactsBaseUrl/$barcode.json'),
            headers: {'User-Agent': 'GlucoMonitor/1.0'},
          )
          .timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        if (data['status'] == 1 && data['product'] != null) {
          return _parseOpenFoodFactsProduct(data['product'], barcode);
        }
      }
      return null;
    } catch (e) {
      debugPrint('Error fetching product info: $e');
      return null;
    }
  }

  /// Parse Open Food Facts product data into FoodEntry
  static FoodEntry _parseOpenFoodFactsProduct(
    Map<String, dynamic> product,
    String barcode,
  ) {
    final nutriments = product['nutriments'] ?? {};

    // Extract nutrition per 100g
    final caloriesPer100g = _parseNutrient(nutriments['energy-kcal_100g']);
    final carbsPer100g = _parseNutrient(nutriments['carbohydrates_100g']);
    final proteinPer100g = _parseNutrient(nutriments['proteins_100g']);
    final fatPer100g = _parseNutrient(nutriments['fat_100g']);
    final fiberPer100g = _parseNutrient(nutriments['fiber_100g']);

    // Determine food category based on categories
    final categories = product['categories_tags'] as List<dynamic>? ?? [];
    final foodCategory = _determineFoodCategory(categories);

    // Estimate glycemic index based on category and carb content
    final glycemicIndex = _estimateGlycemicIndex(foodCategory, carbsPer100g);

    return FoodEntry(
      id: 'scanned_${DateTime.now().millisecondsSinceEpoch}',
      name: product['product_name'] ?? 'Unknown Product',
      carbohydrates: carbsPer100g,
      calories: caloriesPer100g,
      protein: proteinPer100g,
      fat: fatPer100g,
      fiber: fiberPer100g,
      glycemicIndex: glycemicIndex,
      category: foodCategory,
      portion: '100g',
      portionSize: 100.0,
      unit: 'g',
      mealType: MealType.breakfast, // Default, user can change
      timestamp: DateTime.now(),
      brand: product['brands'] ?? '',
      isCustom: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      barcode: barcode,
    );
  }

  static double _parseNutrient(dynamic value) {
    if (value == null) return 0.0;
    if (value is num) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  static FoodCategory _determineFoodCategory(List<dynamic> categories) {
    final categoryStrings =
        categories.map((c) => c.toString().toLowerCase()).toList();

    if (categoryStrings.any(
      (c) => c.contains('meat') || c.contains('fish') || c.contains('poultry'),
    )) {
      return FoodCategory.proteins;
    }
    if (categoryStrings.any(
      (c) => c.contains('dairy') || c.contains('milk') || c.contains('cheese'),
    )) {
      return FoodCategory.dairy;
    }
    if (categoryStrings.any((c) => c.contains('fruit'))) {
      return FoodCategory.fruits;
    }
    if (categoryStrings.any((c) => c.contains('vegetable'))) {
      return FoodCategory.vegetables;
    }
    if (categoryStrings.any(
      (c) => c.contains('bread') || c.contains('cereal') || c.contains('pasta'),
    )) {
      return FoodCategory.grains;
    }
    if (categoryStrings.any(
      (c) => c.contains('beverage') || c.contains('drink'),
    )) {
      return FoodCategory.beverages;
    }
    if (categoryStrings.any(
      (c) =>
          c.contains('sweet') || c.contains('candy') || c.contains('dessert'),
    )) {
      return FoodCategory.sweets;
    }
    if (categoryStrings.any(
      (c) => c.contains('oil') || c.contains('fat') || c.contains('nut'),
    )) {
      return FoodCategory.fats;
    }

    return FoodCategory.grains; // Default
  }

  static GlycemicIndex _estimateGlycemicIndex(
    FoodCategory category,
    double carbs,
  ) {
    // Simple estimation based on category and carb content
    switch (category) {
      case FoodCategory.vegetables:
      case FoodCategory.proteins:
      case FoodCategory.fats:
        return GlycemicIndex.low;
      case FoodCategory.fruits:
        return carbs > 15 ? GlycemicIndex.medium : GlycemicIndex.low;
      case FoodCategory.dairy:
        return GlycemicIndex.low;
      case FoodCategory.grains:
        return carbs > 20 ? GlycemicIndex.high : GlycemicIndex.medium;
      case FoodCategory.sweets:
      case FoodCategory.beverages:
        return carbs > 10 ? GlycemicIndex.high : GlycemicIndex.medium;
      case FoodCategory.other:
        return carbs > 15 ? GlycemicIndex.medium : GlycemicIndex.low;
    }
  }
}

class BarcodeScannerModal extends StatefulWidget {
  const BarcodeScannerModal({super.key});

  @override
  State<BarcodeScannerModal> createState() => _BarcodeScannerModalState();
}

class _BarcodeScannerModalState extends State<BarcodeScannerModal> {
  MobileScannerController controller = MobileScannerController();
  bool _isProcessing = false;

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  void _onDetect(BarcodeCapture capture) async {
    if (_isProcessing) return;

    final List<Barcode> barcodes = capture.barcodes;
    if (barcodes.isEmpty) return;

    final barcode = barcodes.first;
    if (barcode.rawValue == null) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // Fetch product information
      final foodEntry = await BarcodeScannerService.fetchProductInfo(
        barcode.rawValue!,
      );

      if (mounted) {
        if (foodEntry != null) {
          Navigator.of(context).pop(foodEntry);
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Product not found in database'),
              backgroundColor: Colors.orange,
            ),
          );
          setState(() {
            _isProcessing = false;
          });
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error scanning barcode: $e'),
            backgroundColor: Colors.red,
          ),
        );
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
                const Expanded(
                  child: Text(
                    'Scan Barcode',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.center,
                  ),
                ),
                const SizedBox(width: 48), // Balance the close button
              ],
            ),
          ),

          // Scanner
          Expanded(
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: MobileScanner(
                    controller: controller,
                    onDetect: _onDetect,
                  ),
                ),

                // Overlay with scanning frame
                Center(
                  child: Container(
                    width: 250,
                    height: 250,
                    decoration: BoxDecoration(
                      border: Border.all(color: AppColors.primary, width: 2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),

                // Processing indicator
                if (_isProcessing)
                  Container(
                    color: Colors.black54,
                    child: const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(color: AppColors.primary),
                          SizedBox(height: 16),
                          Text(
                            'Looking up product...',
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // Instructions
          Container(
            padding: const EdgeInsets.all(16),
            child: const Text(
              'Point your camera at a barcode to scan',
              style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}
