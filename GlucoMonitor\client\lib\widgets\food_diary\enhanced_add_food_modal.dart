import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../models/food_entry.dart';
import '../../providers/food_diary_provider.dart';
import '../../services/food_diary_service.dart';
import '../../services/barcode_scanner_service.dart';
import '../../services/ai_food_recognition_service.dart';
import '../../services/favorites_service.dart';
import 'voice_input_widget.dart';
import 'favorites_widget.dart';
import 'recipe_builder_widget.dart';

class EnhancedAddFoodModal extends StatefulWidget {
  final MealType? initialMealType;

  const EnhancedAddFoodModal({super.key, this.initialMealType});

  @override
  State<EnhancedAddFoodModal> createState() => _EnhancedAddFoodModalState();
}

class _EnhancedAddFoodModalState extends State<EnhancedAddFoodModal>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late MealType _selectedMealType;
  final TextEditingController _searchController = TextEditingController();
  List<FoodEntry> _searchResults = [];
  List<FoodEntry> _favorites = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedMealType = widget.initialMealType ?? MealType.breakfast;
    _tabController = TabController(length: 7, vsync: this);
    _loadInitialData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadInitialData() async {
    setState(() => _isLoading = true);
    try {
      final favorites = await FavoritesService.getFavorites();

      setState(() {
        _favorites = favorites;
      });
    } catch (e) {
      debugPrint('Error loading initial data: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.9,
      decoration: const BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            margin: const EdgeInsets.only(top: 12),
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: AppColors.textSecondary.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                const Icon(Icons.restaurant_menu, color: AppColors.primary),
                const SizedBox(width: 12),
                const Text(
                  'Add Food Entry',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                    color: AppColors.onSurface,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close),
                ),
              ],
            ),
          ),

          // Meal type selector
          _buildMealTypeSelector(),

          // Tab bar with all entry methods
          TabBar(
            controller: _tabController,
            isScrollable: true,
            labelColor: AppColors.primary,
            unselectedLabelColor: AppColors.textSecondary,
            indicatorColor: AppColors.primary,
            tabs: const [
              Tab(icon: Icon(Icons.search), text: 'Search'),
              Tab(icon: Icon(Icons.qr_code_scanner), text: 'Barcode'),
              Tab(icon: Icon(Icons.camera_alt), text: 'AI Photo'),
              Tab(icon: Icon(Icons.photo_library), text: 'Photo'),
              Tab(icon: Icon(Icons.mic), text: 'Voice'),
              Tab(icon: Icon(Icons.favorite), text: 'Favorites'),
              Tab(icon: Icon(Icons.restaurant), text: 'Recipes'),
            ],
          ),

          // Tab content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSearchTab(),
                _buildBarcodeTab(),
                _buildAIPhotoTab(),
                _buildPhotoUploadTab(),
                _buildVoiceTab(),
                _buildFavoritesTab(),
                _buildRecipesTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMealTypeSelector() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: AppColors.background,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children:
            MealType.values.map((mealType) {
              final isSelected = _selectedMealType == mealType;
              return Expanded(
                child: GestureDetector(
                  onTap: () => setState(() => _selectedMealType = mealType),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    decoration: BoxDecoration(
                      color:
                          isSelected ? AppColors.primary : Colors.transparent,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      mealType.displayName,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        color:
                            isSelected ? Colors.white : AppColors.textSecondary,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ),
              );
            }).toList(),
      ),
    );
  }

  Widget _buildSearchTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // Search bar
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: 'Search for foods...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchController.text.isNotEmpty
                      ? IconButton(
                        onPressed: () {
                          _searchController.clear();
                          setState(() => _searchResults.clear());
                        },
                        icon: const Icon(Icons.clear),
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: AppColors.background,
            ),
            onChanged: _performSearch,
          ),
          const SizedBox(height: 16),

          // Search results
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _searchResults.isEmpty
                    ? _buildEmptyState('Start typing to search for foods')
                    : ListView.builder(
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final food = _searchResults[index];
                        return _buildFoodTile(food);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildBarcodeTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.qr_code_scanner, size: 80, color: AppColors.primary),
          const SizedBox(height: 24),
          const Text(
            'Scan Barcode',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Scan packaged foods for automatic nutrition data',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _scanBarcode,
              icon: const Icon(Icons.qr_code_scanner),
              label: const Text('Start Scanning'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAIPhotoTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.camera_alt, size: 80, color: AppColors.primary),
          const SizedBox(height: 24),
          const Text(
            'AI Food Recognition',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Take a photo and let AI identify your food automatically',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          const SizedBox(height: 32),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _takeAIPhoto,
              icon: const Icon(Icons.camera_alt),
              label: const Text('Take Photo'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPhotoUploadTab() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.photo_library, size: 80, color: AppColors.primary),
          const SizedBox(height: 24),
          const Text(
            'Photo Upload',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 12),
          const Text(
            'Upload photos of your meals for visual tracking',
            textAlign: TextAlign.center,
            style: TextStyle(fontSize: 16, color: AppColors.textSecondary),
          ),
          const SizedBox(height: 32),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _uploadFromGallery,
                  icon: const Icon(Icons.photo_library),
                  label: const Text('Gallery'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _takePhoto,
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Camera'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppColors.primary,
                    side: const BorderSide(color: AppColors.primary),
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVoiceTab() {
    return VoiceInputWidget(
      mealType: _selectedMealType,
      onFoodAdded: _onFoodAdded,
    );
  }

  Widget _buildFavoritesTab() {
    return FavoritesWidget(
      favorites: _favorites,
      mealType: _selectedMealType,
      onFoodAdded: _onFoodAdded,
      onRefresh: _loadInitialData,
    );
  }

  Widget _buildRecipesTab() {
    return RecipeBuilderWidget(
      mealType: _selectedMealType,
      onFoodAdded: _onFoodAdded,
    );
  }

  Widget _buildFoodTile(FoodEntry food) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppColors.primary.withValues(alpha: 0.1),
          child: Icon(
            _getFoodIcon(food.category),
            color: AppColors.primary,
            size: 20,
          ),
        ),
        title: Text(
          food.name,
          style: const TextStyle(
            fontWeight: FontWeight.w500,
            color: AppColors.onSurface,
          ),
        ),
        subtitle: Text(
          '${food.calories.toInt()} cal • ${food.carbohydrates.toInt()}g carbs',
          style: const TextStyle(color: AppColors.textSecondary),
        ),
        trailing: IconButton(
          onPressed: () => _addFood(food),
          icon: const Icon(Icons.add_circle, color: AppColors.primary),
        ),
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search_off,
            size: 64,
            color: AppColors.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  IconData _getFoodIcon(FoodCategory category) {
    switch (category) {
      case FoodCategory.fruits:
        return Icons.apple;
      case FoodCategory.vegetables:
        return Icons.eco;
      case FoodCategory.grains:
        return Icons.grain;
      case FoodCategory.proteins:
        return Icons.egg;
      case FoodCategory.dairy:
        return Icons.local_drink;
      case FoodCategory.beverages:
        return Icons.local_cafe;
      case FoodCategory.fats:
        return Icons.opacity;
      case FoodCategory.sweets:
        return Icons.cookie;
      case FoodCategory.other:
        return Icons.fastfood;
    }
  }

  Future<void> _performSearch(String query) async {
    if (query.isEmpty) {
      setState(() => _searchResults.clear());
      return;
    }

    setState(() => _isLoading = true);
    try {
      final results = await FoodDiaryService.searchFoodDatabase(query);
      setState(() => _searchResults = results);
    } catch (e) {
      debugPrint('Search error: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _scanBarcode() async {
    try {
      final scannedFood = await BarcodeScannerService.showBarcodeScanner(
        context,
      );
      if (scannedFood != null) {
        _addFood(scannedFood);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to scan barcode: $e');
    }
  }

  Future<void> _takeAIPhoto() async {
    try {
      final recognizedFood =
          await AIFoodRecognitionService.showFoodRecognitionModal(
            context,
            mealType: _selectedMealType,
          );
      if (recognizedFood != null) {
        _addFood(recognizedFood);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to recognize food: $e');
    }
  }

  Future<void> _uploadFromGallery() async {
    try {
      // Use the existing AI recognition modal but with gallery source
      final recognizedFood =
          await AIFoodRecognitionService.showFoodRecognitionModal(
            context,
            mealType: _selectedMealType,
          );
      if (recognizedFood != null) {
        _addFood(recognizedFood);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to process image: $e');
    }
  }

  Future<void> _takePhoto() async {
    try {
      // Use the existing AI recognition modal for photo taking
      final photoFood = await AIFoodRecognitionService.showFoodRecognitionModal(
        context,
        mealType: _selectedMealType,
      );
      if (photoFood != null) {
        _addFood(photoFood);
      }
    } catch (e) {
      _showErrorSnackBar('Failed to take photo: $e');
    }
  }

  void _addFood(FoodEntry food) async {
    try {
      final newEntry = food.copyWith(
        id: FoodDiaryService.generateId(),
        mealType: _selectedMealType,
        timestamp: DateTime.now(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await context.read<FoodDiaryProvider>().addFoodEntry(newEntry);
      _onFoodAdded(newEntry);
    } catch (e) {
      _showErrorSnackBar('Failed to add food: $e');
    }
  }

  void _onFoodAdded(FoodEntry food) {
    if (mounted) {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Added ${food.name} to ${_selectedMealType.displayName}',
          ),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }
}
