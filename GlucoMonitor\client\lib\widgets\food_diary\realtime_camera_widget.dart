import 'dart:async';
import 'package:flutter/material.dart';
import '../../models/food_entry.dart';
import '../../models/food_recognition_result.dart';
import '../../services/realtime_camera_service.dart';
import '../../constants/app_colors.dart';

/// Real-time camera widget with food recognition overlay
class RealtimeCameraWidget extends StatefulWidget {
  final MealType mealType;
  final Function(FoodRecognitionResult) onFoodDetected;
  final Function(List<FoodEntry>) onFoodsSelected;

  const RealtimeCameraWidget({
    super.key,
    required this.mealType,
    required this.onFoodDetected,
    required this.onFoodsSelected,
  });

  @override
  State<RealtimeCameraWidget> createState() => _RealtimeCameraWidgetState();
}

class _RealtimeCameraWidgetState extends State<RealtimeCameraWidget>
    with WidgetsBindingObserver {
  StreamSubscription<FoodRecognitionResult>? _recognitionSubscription;
  FoodRecognitionResult? _currentResult;
  bool _isInitialized = false;
  bool _isAnalyzing = false;
  String _statusMessage = 'Initializing camera...';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _initializeCamera();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _stopRecognition();
    RealtimeCameraService.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (!_isInitialized) return;

    if (state == AppLifecycleState.inactive) {
      _stopRecognition();
    } else if (state == AppLifecycleState.resumed) {
      _startRecognition();
    }
  }

  Future<void> _initializeCamera() async {
    try {
      final success = await RealtimeCameraService.initializeCamera();
      if (success && mounted) {
        setState(() {
          _isInitialized = true;
          _statusMessage = 'Point camera at food to identify';
        });
        _startRecognition();
      } else if (mounted) {
        setState(() {
          _statusMessage = 'Failed to initialize camera';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'Camera error: $e';
        });
      }
    }
  }

  void _startRecognition() {
    if (!_isInitialized) return;

    try {
      _recognitionSubscription = RealtimeCameraService.startRealtimeRecognition(
        mealType: widget.mealType,
      ).listen(
        (result) {
          if (mounted) {
            setState(() {
              _currentResult = result;
              _isAnalyzing = true;
              _statusMessage =
                  result.detectedFoods.isNotEmpty
                      ? 'Found: ${result.detectedFoods.first.name}'
                      : 'Looking for food...';
            });
            widget.onFoodDetected(result);
          }
        },
        onError: (error) {
          if (mounted) {
            setState(() {
              _statusMessage = 'Recognition error: $error';
            });
          }
        },
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'Failed to start recognition: $e';
        });
      }
    }
  }

  void _stopRecognition() {
    _recognitionSubscription?.cancel();
    _recognitionSubscription = null;
    RealtimeCameraService.stopRealtimeRecognition();
    if (mounted) {
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  Future<void> _capturePhoto() async {
    try {
      setState(() {
        _statusMessage = 'Analyzing photo...';
      });

      // Take picture and analyze using simplified service
      final imageBytes = await RealtimeCameraService.takePicture();
      FoodRecognitionResult? result;

      if (imageBytes != null && imageBytes.isNotEmpty) {
        result = await RealtimeCameraService.analyzeImage(
          imageBytes,
          mealType: widget.mealType,
        );
      } else {
        // Simulate analysis for demo
        await Future.delayed(const Duration(milliseconds: 1000));
        result = null; // Will be handled below
      }

      if (result != null && mounted) {
        setState(() {
          _currentResult = result;
          _statusMessage =
              result!.detectedFoods.isNotEmpty
                  ? 'Found ${result.detectedFoods.length} food(s)'
                  : 'No food detected';
        });
        widget.onFoodDetected(result);
      } else if (mounted) {
        setState(() {
          _statusMessage = 'No food detected in photo';
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _statusMessage = 'Photo analysis failed: $e';
        });
      }
    }
  }

  void _onTapToFocus(TapDownDetails details) {
    if (!_isInitialized) return;

    final renderBox = context.findRenderObject() as RenderBox;
    final localPosition = renderBox.globalToLocal(details.globalPosition);
    final cameraSize = renderBox.size;

    final focusPoint = Offset(
      localPosition.dx / cameraSize.width,
      localPosition.dy / cameraSize.height,
    );

    RealtimeCameraService.setFocusPoint(focusPoint);
  }

  @override
  Widget build(BuildContext context) {
    if (!_isInitialized) {
      return Container(
        color: Colors.black,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(color: AppColors.primary),
              const SizedBox(height: 16),
              Text(
                _statusMessage,
                style: const TextStyle(color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    // Use simplified camera preview
    return Stack(
      children: [
        // Simplified camera preview
        GestureDetector(
          onTapDown: _onTapToFocus,
          child: SizedBox.expand(
            child: RealtimeCameraService.getCameraPreview(),
          ),
        ),

        // Detection overlay
        if (_currentResult != null && _currentResult!.detectedItems != null)
          ..._buildDetectionOverlays(),

        // Status overlay
        Positioned(
          top: 40,
          left: 16,
          right: 16,
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              children: [
                if (_isAnalyzing)
                  const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      color: AppColors.primary,
                    ),
                  ),
                if (_isAnalyzing) const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _statusMessage,
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Capture button
        Positioned(
          bottom: 40,
          left: 0,
          right: 0,
          child: Center(
            child: GestureDetector(
              onTap: _capturePhoto,
              child: Container(
                width: 70,
                height: 70,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  shape: BoxShape.circle,
                  border: Border.all(color: Colors.white, width: 3),
                ),
                child: const Icon(
                  Icons.camera_alt,
                  color: Colors.white,
                  size: 30,
                ),
              ),
            ),
          ),
        ),

        // Food count indicator
        if (_currentResult != null && _currentResult!.detectedFoods.isNotEmpty)
          Positioned(
            bottom: 120,
            right: 20,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                '${_currentResult!.detectedFoods.length} food(s)',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
      ],
    );
  }

  List<Widget> _buildDetectionOverlays() {
    if (_currentResult?.detectedItems == null) return [];

    return _currentResult!.detectedItems!.map((detectedFood) {
      return Positioned(
        left: detectedFood.boundingBox.left,
        top: detectedFood.boundingBox.top,
        width: detectedFood.boundingBox.width,
        height: detectedFood.boundingBox.height,
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color:
                  detectedFood.isSelected ? AppColors.primary : Colors.orange,
              width: 2,
            ),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Align(
            alignment: Alignment.topLeft,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color:
                    detectedFood.isSelected ? AppColors.primary : Colors.orange,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(4),
                  bottomRight: Radius.circular(4),
                ),
              ),
              child: Text(
                '${detectedFood.name} ${(detectedFood.confidence * 100).toInt()}%',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      );
    }).toList();
  }
}
