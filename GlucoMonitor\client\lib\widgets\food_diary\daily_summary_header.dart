import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../providers/food_diary_provider.dart';

class DailySummaryHeader extends StatelessWidget {
  const DailySummaryHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<FoodDiaryProvider>(
      builder: (context, provider, child) {
        final goals = provider.nutritionGoals;

        return Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            color: AppColors.surface,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Daily Summary',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: AppColors.onSurface,
                    ),
                  ),
                  if (provider.averageBloodSugarChange != null)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: _getBloodSugarChangeColor(
                          provider.averageBloodSugarChange!,
                        ).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getBloodSugarChangeIcon(
                              provider.averageBloodSugarChange!,
                            ),
                            size: 16,
                            color: _getBloodSugarChangeColor(
                              provider.averageBloodSugarChange!,
                            ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${provider.averageBloodSugarChange! >= 0 ? '+' : ''}${provider.averageBloodSugarChange!.toStringAsFixed(0)} mg/dL',
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                              color: _getBloodSugarChangeColor(
                                provider.averageBloodSugarChange!,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
              const SizedBox(height: 20),

              // Nutrition metrics grid
              Row(
                children: [
                  Expanded(
                    child: _buildNutritionCard(
                      'Carbs',
                      '${provider.totalCarbs.toStringAsFixed(0)}g',
                      '${goals.carbohydrates.toStringAsFixed(0)}g goal',
                      provider.carbProgress,
                      Icons.grain,
                      Colors.orange,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildNutritionCard(
                      'Calories',
                      provider.totalCalories.toStringAsFixed(0),
                      '${goals.calories.toStringAsFixed(0)} goal',
                      provider.calorieProgress,
                      Icons.local_fire_department,
                      Colors.red,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: _buildNutritionCard(
                      'Protein',
                      '${provider.totalProtein.toStringAsFixed(0)}g',
                      '${goals.protein.toStringAsFixed(0)}g goal',
                      provider.proteinProgress,
                      Icons.fitness_center,
                      Colors.purple,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildNutritionCard(
                      'Fiber',
                      '${provider.totalFiber.toStringAsFixed(0)}g',
                      '${goals.fiber.toStringAsFixed(0)}g goal',
                      provider.totalFiber / goals.fiber,
                      Icons.eco,
                      Colors.green,
                    ),
                  ),
                ],
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildNutritionCard(
    String label,
    String value,
    String goal,
    double progress,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: color,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            goal,
            style: const TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: Colors.grey.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(color),
            minHeight: 4,
          ),
          const SizedBox(height: 4),
          Text(
            '${(progress * 100).toStringAsFixed(0)}%',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  Color _getBloodSugarChangeColor(double change) {
    if (change <= 30) return Colors.green;
    if (change <= 50) return Colors.orange;
    return Colors.red;
  }

  IconData _getBloodSugarChangeIcon(double change) {
    if (change <= 30) return Icons.trending_flat;
    if (change <= 50) return Icons.trending_up;
    return Icons.trending_up;
  }
}
