import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../constants/app_colors.dart';
import '../../models/medication_reminder.dart';
import '../../providers/medication_provider.dart';
import '../../services/notification_service.dart';
import '../../widgets/medication/add_medication_modal.dart';
import '../../widgets/medication/medication_list.dart';
import '../../widgets/medication/medication_reminder_card.dart';

class MedicationScreen extends StatefulWidget {
  const MedicationScreen({super.key});

  @override
  State<MedicationScreen> createState() => _MedicationScreenState();
}

class _MedicationScreenState extends State<MedicationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);

    // Initialize medication provider and notification service
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      // Ensure notification service is initialized
      await NotificationService.initialize();
      await NotificationService.requestPermissions();

      // Initialize medication provider (check if widget is still mounted)
      if (mounted) {
        context.read<MedicationProvider>().initialize();
      }
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('Medications'),
        backgroundColor: AppColors.background,
        foregroundColor: AppColors.onBackground,
        actions: [
          // Test notification button (for debugging)
          IconButton(
            icon: const Icon(Icons.notifications_active),
            onPressed: _testNotification,
            tooltip: 'Test Notification',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primary,
          unselectedLabelColor: AppColors.textSecondary,
          indicatorColor: AppColors.primary,
          tabs: const [
            Tab(text: 'Today'),
            Tab(text: 'Medications'),
            Tab(text: 'Overview'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodayTab(),
          _buildMedicationsTab(),
          _buildOverviewTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: "medication_fab",
        onPressed: _addMedication,
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildTodayTab() {
    return Consumer<MedicationProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final todayReminders = provider.todayReminders;
        final pendingReminders = provider.pendingTodayReminders;
        final overdueReminders = provider.overdueReminders;
        final completedReminders = provider.completedTodayReminders;

        return RefreshIndicator(
          onRefresh: provider.refresh,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              // Stats cards
              _buildTodayStats(provider),
              const SizedBox(height: 24),

              // Overdue reminders
              if (overdueReminders.isNotEmpty) ...[
                _buildSectionHeader(
                  'Overdue',
                  overdueReminders.length,
                  Colors.red,
                ),
                const SizedBox(height: 12),
                ...overdueReminders.map(
                  (reminder) => _buildReminderCard(reminder, provider),
                ),
                const SizedBox(height: 24),
              ],

              // Pending reminders
              if (pendingReminders.isNotEmpty) ...[
                _buildSectionHeader(
                  'Upcoming',
                  pendingReminders.length,
                  AppColors.primary,
                ),
                const SizedBox(height: 12),
                ...pendingReminders.map(
                  (reminder) => _buildReminderCard(reminder, provider),
                ),
                const SizedBox(height: 24),
              ],

              // Completed reminders
              if (completedReminders.isNotEmpty) ...[
                _buildSectionHeader(
                  'Completed',
                  completedReminders.length,
                  Colors.green,
                ),
                const SizedBox(height: 12),
                ...completedReminders.map(
                  (reminder) => _buildReminderCard(reminder, provider),
                ),
              ],

              // Empty state
              if (todayReminders.isEmpty) ...[
                const SizedBox(height: 40),
                Center(
                  child: Column(
                    children: [
                      Icon(
                        Icons.medication_outlined,
                        size: 64,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(height: 16),
                      const Text(
                        'No reminders for today',
                        style: TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: AppColors.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Add medications to see reminders here',
                        style: TextStyle(
                          fontSize: 16,
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }

  Widget _buildMedicationsTab() {
    return const MedicationList();
  }

  Widget _buildOverviewTab() {
    return Consumer<MedicationProvider>(
      builder: (context, provider, child) {
        if (provider.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        final stats = provider.stats;
        if (stats == null) {
          return const Center(child: Text('No data available'));
        }

        return RefreshIndicator(
          onRefresh: provider.refresh,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              _buildOverviewStats(stats),
              const SizedBox(height: 24),
              _buildAdherenceChart(stats),
              const SizedBox(height: 24),
              _buildUpcomingReminders(provider),
            ],
          ),
        );
      },
    );
  }

  Widget _buildTodayStats(MedicationProvider provider) {
    final stats = provider.stats;
    if (stats == null) return const SizedBox.shrink();

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Total',
            stats.todayReminders.toString(),
            Icons.medication,
            AppColors.primary,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Completed',
            stats.completedToday.toString(),
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            'Missed',
            stats.missedToday.toString(),
            Icons.cancel,
            Colors.red,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: AppColors.onSurface,
            ),
          ),
          Text(
            label,
            style: const TextStyle(
              fontSize: 14,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, int count, Color color) {
    return Row(
      children: [
        Container(
          width: 4,
          height: 20,
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(2),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          title,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(width: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            count.toString(),
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
              color: color,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildReminderCard(
    MedicationReminder reminder,
    MedicationProvider provider,
  ) {
    final medication = provider.getMedicationById(reminder.medicationId);
    return MedicationReminderCard(reminder: reminder, medication: medication);
  }

  Widget _buildOverviewStats(MedicationStats stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Overview',
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Active Medications',
                stats.activeMedications.toString(),
                Icons.medication,
                AppColors.primary,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Adherence Rate',
                '${stats.overallAdherence.toStringAsFixed(0)}%',
                Icons.trending_up,
                stats.overallAdherence >= 80 ? Colors.green : Colors.orange,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAdherenceChart(MedicationStats stats) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Adherence Progress',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppColors.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          LinearProgressIndicator(
            value: stats.overallAdherence / 100,
            backgroundColor: Colors.grey.withValues(alpha: 0.2),
            valueColor: AlwaysStoppedAnimation<Color>(
              stats.overallAdherence >= 80 ? Colors.green : Colors.orange,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            '${stats.overallAdherence.toStringAsFixed(1)}% adherence over the last 7 days',
            style: const TextStyle(
              fontSize: 16,
              color: AppColors.textSecondary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpcomingReminders(MedicationProvider provider) {
    final upcomingReminders = provider.upcomingReminders.take(5).toList();

    if (upcomingReminders.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Upcoming Reminders',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppColors.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        ...upcomingReminders.map((reminder) {
          final medication = provider.getMedicationById(reminder.medicationId);
          return MedicationReminderCard(
            reminder: reminder,
            medication: medication,
            showDate: true,
          );
        }),
      ],
    );
  }

  void _addMedication() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AddMedicationModal(),
    );
  }

  // Test notification function
  void _testNotification() async {
    try {
      // Check if notifications are enabled
      final isEnabled = NotificationService.isEnabled;
      debugPrint('Notifications enabled: $isEnabled');

      if (!isEnabled) {
        // Try to initialize and request permissions again
        await NotificationService.initialize();
        final permissionGranted =
            await NotificationService.requestPermissions();
        debugPrint('Permission granted after request: $permissionGranted');

        if (!permissionGranted) {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                  'Notification permissions not granted. Please enable notifications in settings.',
                ),
                backgroundColor: Colors.red,
              ),
            );
          }
          return;
        }
      }

      // Show immediate test notification
      await NotificationService.showImmediateNotification(
        title: 'Test Notification',
        body:
            'This is a test notification from GlucoMonitor medication reminders!',
      );

      // Get pending notifications count
      final pendingCount =
          await NotificationService.getPendingNotificationsCount();
      debugPrint('Pending notifications: $pendingCount');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Test notification sent! Pending notifications: $pendingCount',
            ),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      debugPrint('Error testing notification: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error testing notification: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
