import sharp from 'sharp';
import logger from '../utils/logger';

// Interface for image features
export interface ImageFeatures {
    colorFeatures: {
        avgRed: number;
        avgGreen: number;
        avgBlue: number;
        dominantColor: string;
        brightness: number;
        saturation: number;
    };
    textureFeatures: {
        smoothness: number;
        uniformity: number;
        graininess: number;
    };
    shapeFeatures: {
        roundness: number;
        elongation: number;
        compactness: number;
    };
    sizeFeatures: {
        totalPixels: number;
        estimatedArea: number;
    };
}

// Interface for recognition options
export interface RecognitionOptions {
    enableMultiDetection: boolean;
    enablePortionEstimation: boolean;
    userId: string;
    mealType: string;
}

// Interface for recognition result
export interface RecognitionResult {
    foodName: string;
    confidence: number;
    source: 'AI_VISION' | 'CUSTOM_MODEL' | 'PATTERN_MATCH';
    nutritionData?: any;
    portionEstimation?: {
        estimatedWeight: number;
        unit: string;
        confidence: number;
        method: string;
    };
    boundingBox?: {
        x: number;
        y: number;
        width: number;
        height: number;
    };
}

/**
 * Extract comprehensive features from image data
 */
export async function extractImageFeatures(imageData: string): Promise<ImageFeatures> {
    try {
        // Convert base64 to buffer
        const imageBuffer = Buffer.from(imageData, 'base64');
        
        // Get image metadata
        const metadata = await sharp(imageBuffer).metadata();
        const { width = 0, height = 0 } = metadata;
        
        // Resize image for processing (max 512x512 for performance)
        const processedImage = await sharp(imageBuffer)
            .resize(512, 512, { fit: 'inside', withoutEnlargement: true })
            .raw()
            .toBuffer({ resolveWithObject: true });

        const { data, info } = processedImage;
        const pixelCount = info.width * info.height;

        // Extract color features
        const colorFeatures = extractColorFeatures(data, pixelCount);
        
        // Extract texture features
        const textureFeatures = extractTextureFeatures(data, info.width, info.height);
        
        // Extract shape features (simplified)
        const shapeFeatures = extractShapeFeatures(data, info.width, info.height);
        
        // Calculate size features
        const sizeFeatures = {
            totalPixels: width * height,
            estimatedArea: (width * height) * 0.1 // Simplified area estimation
        };

        return {
            colorFeatures,
            textureFeatures,
            shapeFeatures,
            sizeFeatures
        };

    } catch (error) {
        logger.error('Error extracting image features', { error: error.message });
        throw new Error('Failed to extract image features');
    }
}

/**
 * Process image for food recognition
 */
export async function processImageForRecognition(
    imageData: string,
    options: RecognitionOptions
): Promise<RecognitionResult[]> {
    try {
        logger.info('Processing image for food recognition', {
            userId: options.userId,
            enableMultiDetection: options.enableMultiDetection,
            enablePortionEstimation: options.enablePortionEstimation
        });

        // Extract image features
        const features = await extractImageFeatures(imageData);
        
        // Perform recognition based on features
        const results: RecognitionResult[] = [];

        // Pattern-based recognition for South African foods
        const patternResult = await recognizeWithPatterns(features, options.mealType);
        if (patternResult) {
            results.push(patternResult);
        }

        // If multi-detection is enabled, try to find multiple foods
        if (options.enableMultiDetection) {
            const multiResults = await performMultiDetection(imageData, features);
            results.push(...multiResults);
        }

        // Add portion estimation if enabled
        if (options.enablePortionEstimation && results.length > 0) {
            for (const result of results) {
                result.portionEstimation = await estimatePortionSize(features, result.foodName);
            }
        }

        return results;

    } catch (error) {
        logger.error('Error processing image for recognition', {
            userId: options.userId,
            error: error.message
        });
        throw new Error('Failed to process image for recognition');
    }
}

/**
 * Extract color features from image data
 */
function extractColorFeatures(data: Buffer, pixelCount: number) {
    let redSum = 0, greenSum = 0, blueSum = 0;
    const redValues: number[] = [];
    const greenValues: number[] = [];
    const blueValues: number[] = [];

    // Process RGB values (assuming 3 channels)
    for (let i = 0; i < data.length; i += 3) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        redSum += r;
        greenSum += g;
        blueSum += b;

        redValues.push(r);
        greenValues.push(g);
        blueValues.push(b);
    }

    const avgRed = redSum / pixelCount;
    const avgGreen = greenSum / pixelCount;
    const avgBlue = blueSum / pixelCount;

    // Calculate brightness and saturation
    const brightness = (avgRed + avgGreen + avgBlue) / 3;
    const max = Math.max(avgRed, avgGreen, avgBlue);
    const min = Math.min(avgRed, avgGreen, avgBlue);
    const saturation = max > 0 ? (max - min) / max : 0;

    // Determine dominant color
    const dominantColor = getDominantColor(avgRed, avgGreen, avgBlue);

    return {
        avgRed,
        avgGreen,
        avgBlue,
        dominantColor,
        brightness,
        saturation
    };
}

/**
 * Extract texture features from image data
 */
function extractTextureFeatures(data: Buffer, width: number, height: number) {
    let variance = 0;
    let edgeCount = 0;
    const pixelCount = width * height;

    // Calculate pixel variance for smoothness
    for (let y = 0; y < height - 1; y++) {
        for (let x = 0; x < width - 1; x++) {
            const currentIdx = (y * width + x) * 3;
            const nextIdx = (y * width + (x + 1)) * 3;

            const current = (data[currentIdx] + data[currentIdx + 1] + data[currentIdx + 2]) / 3;
            const next = (data[nextIdx] + data[nextIdx + 1] + data[nextIdx + 2]) / 3;

            variance += Math.pow(current - next, 2);

            if (Math.abs(current - next) > 30) {
                edgeCount++;
            }
        }
    }

    const smoothness = 1.0 - Math.min(variance / pixelCount / 255, 1.0);
    const uniformity = 1.0 - Math.min(edgeCount / pixelCount, 1.0);
    const graininess = Math.min(edgeCount / pixelCount, 1.0);

    return {
        smoothness,
        uniformity,
        graininess
    };
}

/**
 * Extract shape features from image data (simplified)
 */
function extractShapeFeatures(data: Buffer, width: number, height: number) {
    // Simplified shape analysis
    // In a production system, this would use proper computer vision algorithms
    
    const aspectRatio = width / height;
    const roundness = Math.min(width, height) / Math.max(width, height);
    const elongation = aspectRatio > 1 ? aspectRatio - 1 : 1 / aspectRatio - 1;
    const compactness = 0.5; // Placeholder

    return {
        roundness,
        elongation: Math.min(elongation, 1.0),
        compactness
    };
}

/**
 * Recognize food using pattern matching
 */
async function recognizeWithPatterns(features: ImageFeatures, mealType: string): Promise<RecognitionResult | null> {
    // South African food patterns
    const patterns = {
        pap: {
            colorMatch: features.colorFeatures.dominantColor === 'white' && 
                       features.colorFeatures.avgRed > 200 && 
                       features.colorFeatures.avgGreen > 200,
            textureMatch: features.textureFeatures.smoothness > 0.7,
            confidence: 0.85
        },
        morogo: {
            colorMatch: features.colorFeatures.dominantColor === 'green' && 
                       features.colorFeatures.avgGreen > 150,
            textureMatch: features.textureFeatures.graininess > 0.6,
            confidence: 0.80
        },
        boerewors: {
            colorMatch: features.colorFeatures.dominantColor === 'brown' && 
                       features.colorFeatures.avgRed > 100,
            textureMatch: features.textureFeatures.uniformity > 0.5,
            confidence: 0.82
        }
    };

    for (const [foodName, pattern] of Object.entries(patterns)) {
        if (pattern.colorMatch && pattern.textureMatch) {
            return {
                foodName: foodName.charAt(0).toUpperCase() + foodName.slice(1),
                confidence: pattern.confidence,
                source: 'PATTERN_MATCH',
                nutritionData: getSAFoodNutrition(foodName)
            };
        }
    }

    return null;
}

/**
 * Perform multi-food detection
 */
async function performMultiDetection(imageData: string, features: ImageFeatures): Promise<RecognitionResult[]> {
    // Simplified multi-detection
    // In production, this would use object detection models
    const results: RecognitionResult[] = [];

    // Simulate finding multiple foods based on image complexity
    if (features.textureFeatures.graininess > 0.7) {
        // High texture variation suggests multiple items
        results.push({
            foodName: 'Mixed Vegetables',
            confidence: 0.7,
            source: 'AI_VISION',
            boundingBox: { x: 0, y: 0, width: 200, height: 200 }
        });
    }

    return results;
}

/**
 * Estimate portion size
 */
async function estimatePortionSize(features: ImageFeatures, foodName: string) {
    // Simplified portion estimation
    const portionMap: { [key: string]: number } = {
        'pap': 200,
        'morogo': 100,
        'boerewors': 80,
        'rice': 150,
        'chicken': 100
    };

    const baseWeight = portionMap[foodName.toLowerCase()] || 100;
    
    // Adjust based on image size
    const sizeMultiplier = Math.min(features.sizeFeatures.totalPixels / 500000, 2.0);
    const estimatedWeight = baseWeight * sizeMultiplier;

    return {
        estimatedWeight: Math.round(estimatedWeight),
        unit: 'g',
        confidence: 0.7,
        method: 'AI_ESTIMATION'
    };
}

/**
 * Get dominant color from RGB values
 */
function getDominantColor(r: number, g: number, b: number): string {
    if (r > g && r > b) return 'red';
    if (g > r && g > b) return 'green';
    if (b > r && b > g) return 'blue';
    if (r > 200 && g > 200 && b > 200) return 'white';
    if (r < 50 && g < 50 && b < 50) return 'black';
    if (r > 150 && g > 100 && b < 100) return 'brown';
    if (r > 200 && g > 200 && b < 150) return 'yellow';
    return 'mixed';
}

/**
 * Get nutrition data for South African foods
 */
function getSAFoodNutrition(foodName: string) {
    const nutritionData: { [key: string]: any } = {
        pap: {
            calories: 112,
            carbohydrates: 23.0,
            protein: 2.4,
            fat: 0.4,
            fiber: 1.2,
            glycemicIndex: 'high',
            category: 'grains'
        },
        morogo: {
            calories: 23,
            carbohydrates: 4.0,
            protein: 3.0,
            fat: 0.3,
            fiber: 2.8,
            glycemicIndex: 'low',
            category: 'vegetables'
        },
        boerewors: {
            calories: 285,
            carbohydrates: 2.0,
            protein: 16.0,
            fat: 24.0,
            fiber: 0.0,
            glycemicIndex: 'low',
            category: 'meat'
        }
    };

    return nutritionData[foodName] || {
        calories: 150,
        carbohydrates: 20,
        protein: 5,
        fat: 3,
        fiber: 2,
        glycemicIndex: 'medium',
        category: 'other'
    };
}
