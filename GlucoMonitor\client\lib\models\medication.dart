import 'package:flutter/material.dart';

enum MedicationType {
  tablet,
  capsule,
  liquid,
  injection,
  inhaler,
  cream,
  drops,
  patch,
  other,
}

enum MedicationFrequency {
  onceDaily,
  twiceDaily,
  threeTimesDaily,
  fourTimesDaily,
  everyOtherDay,
  weekly,
  asNeeded,
  custom,
}

enum ReminderStatus {
  pending,
  taken,
  missed,
  skipped,
}

class MedicationTime {
  final TimeOfDay time;
  final String? label; // e.g., "Morning", "Evening", "With breakfast"

  const MedicationTime({
    required this.time,
    this.label,
  });

  Map<String, dynamic> toJson() {
    return {
      'hour': time.hour,
      'minute': time.minute,
      'label': label,
    };
  }

  factory MedicationTime.fromJson(Map<String, dynamic> json) {
    return MedicationTime(
      time: TimeOfDay(
        hour: json['hour'] as int,
        minute: json['minute'] as int,
      ),
      label: json['label'] as String?,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is MedicationTime &&
          runtimeType == other.runtimeType &&
          time.hour == other.time.hour &&
          time.minute == other.time.minute &&
          label == other.label;

  @override
  int get hashCode => time.hashCode ^ label.hashCode;
}

class Medication {
  final String id;
  final String name;
  final String? brandName;
  final MedicationType type;
  final double dosage;
  final String dosageUnit; // mg, ml, units, etc.
  final MedicationFrequency frequency;
  final List<MedicationTime> times;
  final DateTime startDate;
  final DateTime? endDate;
  final String? notes;
  final String? prescribedBy;
  final bool isActive;
  final bool reminderEnabled;
  final int reminderMinutesBefore;
  final String? color; // Hex color for visual identification
  final DateTime createdAt;
  final DateTime updatedAt;

  const Medication({
    required this.id,
    required this.name,
    this.brandName,
    required this.type,
    required this.dosage,
    required this.dosageUnit,
    required this.frequency,
    required this.times,
    required this.startDate,
    this.endDate,
    this.notes,
    this.prescribedBy,
    this.isActive = true,
    this.reminderEnabled = true,
    this.reminderMinutesBefore = 15,
    this.color,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'brandName': brandName,
      'type': type.name,
      'dosage': dosage,
      'dosageUnit': dosageUnit,
      'frequency': frequency.name,
      'times': times.map((t) => t.toJson()).toList(),
      'startDate': startDate.toIso8601String(),
      'endDate': endDate?.toIso8601String(),
      'notes': notes,
      'prescribedBy': prescribedBy,
      'isActive': isActive,
      'reminderEnabled': reminderEnabled,
      'reminderMinutesBefore': reminderMinutesBefore,
      'color': color,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Medication.fromJson(Map<String, dynamic> json) {
    return Medication(
      id: json['id'] as String,
      name: json['name'] as String,
      brandName: json['brandName'] as String?,
      type: MedicationType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => MedicationType.other,
      ),
      dosage: (json['dosage'] as num).toDouble(),
      dosageUnit: json['dosageUnit'] as String,
      frequency: MedicationFrequency.values.firstWhere(
        (e) => e.name == json['frequency'],
        orElse: () => MedicationFrequency.onceDaily,
      ),
      times: (json['times'] as List<dynamic>)
          .map((t) => MedicationTime.fromJson(t as Map<String, dynamic>))
          .toList(),
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] != null
          ? DateTime.parse(json['endDate'] as String)
          : null,
      notes: json['notes'] as String?,
      prescribedBy: json['prescribedBy'] as String?,
      isActive: json['isActive'] as bool? ?? true,
      reminderEnabled: json['reminderEnabled'] as bool? ?? true,
      reminderMinutesBefore: json['reminderMinutesBefore'] as int? ?? 15,
      color: json['color'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  Medication copyWith({
    String? id,
    String? name,
    String? brandName,
    MedicationType? type,
    double? dosage,
    String? dosageUnit,
    MedicationFrequency? frequency,
    List<MedicationTime>? times,
    DateTime? startDate,
    DateTime? endDate,
    String? notes,
    String? prescribedBy,
    bool? isActive,
    bool? reminderEnabled,
    int? reminderMinutesBefore,
    String? color,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Medication(
      id: id ?? this.id,
      name: name ?? this.name,
      brandName: brandName ?? this.brandName,
      type: type ?? this.type,
      dosage: dosage ?? this.dosage,
      dosageUnit: dosageUnit ?? this.dosageUnit,
      frequency: frequency ?? this.frequency,
      times: times ?? this.times,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      notes: notes ?? this.notes,
      prescribedBy: prescribedBy ?? this.prescribedBy,
      isActive: isActive ?? this.isActive,
      reminderEnabled: reminderEnabled ?? this.reminderEnabled,
      reminderMinutesBefore: reminderMinutesBefore ?? this.reminderMinutesBefore,
      color: color ?? this.color,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => brandName?.isNotEmpty == true ? '$brandName ($name)' : name;

  String get dosageDisplay => '$dosage $dosageUnit';

  String get typeDisplay {
    switch (type) {
      case MedicationType.tablet:
        return 'Tablet';
      case MedicationType.capsule:
        return 'Capsule';
      case MedicationType.liquid:
        return 'Liquid';
      case MedicationType.injection:
        return 'Injection';
      case MedicationType.inhaler:
        return 'Inhaler';
      case MedicationType.cream:
        return 'Cream';
      case MedicationType.drops:
        return 'Drops';
      case MedicationType.patch:
        return 'Patch';
      case MedicationType.other:
        return 'Other';
    }
  }

  String get frequencyDisplay {
    switch (frequency) {
      case MedicationFrequency.onceDaily:
        return 'Once daily';
      case MedicationFrequency.twiceDaily:
        return 'Twice daily';
      case MedicationFrequency.threeTimesDaily:
        return 'Three times daily';
      case MedicationFrequency.fourTimesDaily:
        return 'Four times daily';
      case MedicationFrequency.everyOtherDay:
        return 'Every other day';
      case MedicationFrequency.weekly:
        return 'Weekly';
      case MedicationFrequency.asNeeded:
        return 'As needed';
      case MedicationFrequency.custom:
        return 'Custom schedule';
    }
  }

  Color get medicationColor {
    if (color != null) {
      try {
        return Color(int.parse(color!.replaceFirst('#', '0xFF')));
      } catch (e) {
        // Fallback to default color if parsing fails
      }
    }
    // Default colors based on medication type
    switch (type) {
      case MedicationType.tablet:
        return Colors.blue;
      case MedicationType.capsule:
        return Colors.green;
      case MedicationType.liquid:
        return Colors.orange;
      case MedicationType.injection:
        return Colors.red;
      case MedicationType.inhaler:
        return Colors.purple;
      case MedicationType.cream:
        return Colors.pink;
      case MedicationType.drops:
        return Colors.cyan;
      case MedicationType.patch:
        return Colors.amber;
      case MedicationType.other:
        return Colors.grey;
    }
  }
}
