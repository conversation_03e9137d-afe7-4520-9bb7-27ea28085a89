# Docker Configuration for GlucoMonitor Backend

This document describes the comprehensive Docker setup for the GlucoMonitor backend API with multi-environment support.

## 🚀 Quick Start

### Development Environment
```bash
# From project root
docker-compose --profile dev up --build

# From backend directory
docker-compose --profile dev up --build

# Run in background
docker-compose --profile dev up -d --build
```

### Production Environment
```bash
# From project root
docker-compose --profile prod up --build

# From backend directory (standalone)
docker-compose up --build

# Run in background
docker-compose up -d --build
```

### Testing Environment
```bash
# Run tests in Docker
docker-compose --profile test up --build
```

## 📁 Docker Files

- `Dockerfile` - Multi-stage build with development, builder, and production targets
- `Dockerfile.dev` - Development-optimized image with debugging support
- `Dockerfile.prod` - Legacy production-only build (deprecated)
- `docker-compose.yml` - Backend-specific orchestration
- `../docker-compose.yml` - Main project orchestration with profiles

## 🔧 Environment Configuration

### Production Environment (.env.docker)
```env
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://your-mongodb-connection-string
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE=30d
SMSPORTAL_CLIENT_ID=your-smsportal-client-id
SMSPORTAL_API_SECRET=your-smsportal-api-secret
SMSPORTAL_SENDER=your-sender-name
REDIS_URL=redis://redis:6379
NOTIFICATION_ENABLED=true
DEVELOPMENT_MODE=false
```

### Test Environment (.env.test)
```env
NODE_ENV=test
PORT=5000
MONGODB_URI=mongodb://localhost:27017/glucomonitor_test
JWT_SECRET=test-jwt-secret
JWT_EXPIRE=1h
REDIS_URL=redis://redis:6379
NOTIFICATION_ENABLED=false
DEVELOPMENT_MODE=true
```

Copy the example files and customize:
```bash
cp .env.docker.example .env.docker
cp .env.test.example .env.test
```

## 🏗️ Multi-Stage Build Architecture

### Development Stage
- Based on Node.js 20 Alpine
- Includes development tools and debugging support
- Hot reloading with nodemon
- Debug port (9229) exposed

### Builder Stage
- Compiles TypeScript to JavaScript
- Optimized for build performance
- Includes all dependencies needed for compilation

### Production Stage
- Minimal runtime image
- Only production dependencies
- Non-root user for security
- Proper signal handling with dumb-init

## 🐳 Services Overview

### Backend API
- **Ports**: 
  - 5000 (HTTP)
  - 9229 (Debug - dev only)
- **Health Checks**: 
  - `/api/health` - Basic health status
  - `/api/health/live` - Liveness probe
  - `/api/health/ready` - Readiness probe
  - `/api/health/database` - Database connectivity
  - `/api/health/redis` - Redis connectivity
- **Security**: Runs as non-root user `glucomonitor`

### Redis Cache
- **Port**: 6379
- **Purpose**: Caching, rate limiting, session storage
- **Configuration**: 256MB memory limit with LRU eviction
- **Persistence**: Data persisted in Docker volume
- **Health Check**: Redis PING command

## 🔄 Docker Compose Profiles

### Development Profile (`--profile dev`)
- Hot reloading enabled
- Source code mounted as volumes
- Debug port exposed
- Development environment variables
- Relaxed security settings

### Production Profile (`--profile prod`)
- Optimized production build
- Security hardening
- Health checks enabled
- Proper restart policies
- Volume mounts for logs and uploads

### Test Profile (`--profile test`)
- Test database configuration
- Minimal logging
- Fast startup for CI/CD
- Isolated test environment

## 🛠️ Development Features

### Hot Reloading
Source code changes are automatically detected and the server restarts:
```bash
# Watch for changes in src/ directory
docker-compose --profile dev up
```

### Debugging
Attach your IDE debugger to port 9229:
```bash
# VS Code launch.json example
{
  "type": "node",
  "request": "attach",
  "name": "Docker Debug",
  "port": 9229,
  "address": "localhost",
  "localRoot": "${workspaceFolder}/backend/src",
  "remoteRoot": "/app/src"
}
```

### Volume Mounts
- `./src:/app/src` - Source code hot reloading
- `./logs:/app/logs` - Log file access
- `./uploads:/app/uploads` - File upload storage

## 🔒 Security Features

### Production Security
- Non-root user execution
- Read-only file system where possible
- Security updates in base image
- Minimal attack surface
- Proper signal handling

### Development Security
- Isolated network
- Environment variable isolation
- Debug port only exposed in development

## 📊 Health Monitoring

### Health Check Endpoints
```bash
# Basic health status
curl http://localhost:5000/api/health

# Liveness probe (for orchestrators)
curl http://localhost:5000/api/health/live

# Readiness probe (for orchestrators)
curl http://localhost:5000/api/health/ready

# Database connectivity
curl http://localhost:5000/api/health/database

# Redis connectivity
curl http://localhost:5000/api/health/redis

# Detailed health (requires authentication)
curl -H "Authorization: Bearer <token>" http://localhost:5000/api/health/detailed
```

### Docker Health Checks
All services include Docker health checks:
- **Interval**: 30 seconds
- **Timeout**: 10 seconds
- **Retries**: 3
- **Start Period**: 40 seconds (backend), 30 seconds (Redis)

## 🚀 Useful Commands

### Basic Operations
```bash
# Start development environment
docker-compose --profile dev up

# Start production environment
docker-compose --profile prod up

# Run tests
docker-compose --profile test up

# View logs (all services)
docker-compose logs -f

# View specific service logs
docker-compose logs -f backend-dev
docker-compose logs -f redis

# Execute commands in running container
docker-compose exec backend-dev sh
docker-compose exec redis redis-cli

# Stop all services
docker-compose down

# Stop and remove volumes (⚠️ data loss)
docker-compose down -v
```

### Build Operations
```bash
# Rebuild without cache
docker-compose build --no-cache

# Build specific service
docker-compose build backend-dev

# Pull latest base images
docker-compose pull

# Build and start (force rebuild)
docker-compose up --build
```

### Maintenance
```bash
# Remove unused images
docker image prune

# Remove unused volumes
docker volume prune

# Remove everything (⚠️ destructive)
docker system prune -a

# View resource usage
docker stats
```

## 🐛 Troubleshooting

### Common Issues

#### Port Conflicts
```bash
# Check what's using port 5000
lsof -i :5000

# Change port in docker-compose.yml
ports:
  - "5001:5000"  # Use port 5001 instead
```

#### MongoDB Connection Issues
```bash
# Check MongoDB URI format
MONGODB_URI=********************************:port/database

# Test connection from container
docker-compose exec backend-dev npm run db:check
```

#### Redis Connection Issues
```bash
# Check Redis connectivity
docker-compose exec redis redis-cli ping

# Check Redis logs
docker-compose logs redis

# Test Redis from backend
curl http://localhost:5000/api/health/redis
```

#### Build Issues
```bash
# Clear Docker cache
docker builder prune

# Rebuild from scratch
docker-compose build --no-cache --pull

# Check Dockerfile syntax
docker build --no-cache -t test-build .
```

#### Permission Issues
```bash
# Fix file permissions (Linux/macOS)
sudo chown -R $USER:$USER ./backend/logs
sudo chown -R $USER:$USER ./uploads

# Check container user
docker-compose exec backend-dev id
```

### Performance Optimization

#### Memory Usage
```bash
# Monitor memory usage
docker stats

# Adjust Redis memory limit
command: redis-server --maxmemory 512mb

# Adjust Node.js memory limit
environment:
  - NODE_OPTIONS=--max-old-space-size=1024
```

#### Build Performance
```bash
# Use BuildKit for faster builds
export DOCKER_BUILDKIT=1

# Multi-stage build caching
docker build --target development .
```

### Debugging

#### Container Debugging
```bash
# Access container shell
docker-compose exec backend-dev sh

# Check environment variables
docker-compose exec backend-dev env

# Check process list
docker-compose exec backend-dev ps aux

# Check network connectivity
docker-compose exec backend-dev ping redis
```

#### Log Analysis
```bash
# Follow logs with timestamps
docker-compose logs -f -t

# Filter logs by service
docker-compose logs backend-dev | grep ERROR

# Export logs to file
docker-compose logs > docker-logs.txt
```

## 📈 Production Deployment

### Best Practices
1. Use specific image tags, not `latest`
2. Set resource limits
3. Configure proper restart policies
4. Use secrets management for sensitive data
5. Enable log rotation
6. Monitor resource usage
7. Regular security updates

### Resource Limits
```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
```

### Secrets Management
```yaml
secrets:
  jwt_secret:
    external: true
  mongodb_uri:
    external: true
```

## 🔄 CI/CD Integration

### GitHub Actions Example
```yaml
- name: Build and test
  run: |
    docker-compose --profile test up --build --abort-on-container-exit

- name: Build production image
  run: |
    docker-compose --profile prod build
```

### Health Check Integration
```bash
# Wait for service to be ready
while ! curl -f http://localhost:5000/api/health/ready; do
  sleep 5
done
```

## 🔧 Advanced Configuration

### Custom Networks
```yaml
networks:
  glucomonitor-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
```

### Volume Management
```yaml
volumes:
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: /opt/glucomonitor/redis
```

### Environment Overrides
```bash
# Override specific environment variables
docker-compose up -e MONGODB_URI=mongodb://new-host:27017/db
```
