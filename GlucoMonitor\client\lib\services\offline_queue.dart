import 'dart:async';
import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

class QueuedOperation {
  final String type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  QueuedOperation({required this.type, required this.data, DateTime? timestamp})
    : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'type': type,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
  };

  factory QueuedOperation.fromJson(Map<String, dynamic> json) {
    return QueuedOperation(
      type: json['type'],
      data: json['data'],
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class OfflineQueue {
  static const String _queueKey = 'offline_operation_queue';
  final List<QueuedOperation> _queue = [];
  final _controller = StreamController<int>.broadcast();

  Stream<int> get queueSizeStream => _controller.stream;
  int get size => _queue.length;

  Future<void> init() async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = prefs.getString(_queueKey);
    if (queueJson != null) {
      final List<dynamic> queueData = jsonDecode(queueJson);
      _queue.addAll(queueData.map((item) => QueuedOperation.fromJson(item)));
      _controller.add(_queue.length);
    }
  }

  Future<void> add(QueuedOperation operation) async {
    _queue.add(operation);
    await _persistQueue();
    _controller.add(_queue.length);
  }

  Future<void> _persistQueue() async {
    final prefs = await SharedPreferences.getInstance();
    final queueJson = jsonEncode(_queue.map((op) => op.toJson()).toList());
    await prefs.setString(_queueKey, queueJson);
  }

  Future<List<QueuedOperation>> getOperations() async {
    return List.from(_queue);
  }

  Future<void> remove(QueuedOperation operation) async {
    _queue.removeWhere(
      (op) =>
          op.type == operation.type &&
          op.timestamp == operation.timestamp &&
          op.data.toString() == operation.data.toString(),
    );
    await _persistQueue();
    _controller.add(_queue.length);
  }

  Future<void> clear() async {
    _queue.clear();
    await _persistQueue();
    _controller.add(0);
  }

  void dispose() {
    _controller.close();
  }
}
