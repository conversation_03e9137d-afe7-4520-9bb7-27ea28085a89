import { Response } from 'express';
import FoodEntry, { MealType, FoodCategory } from '../models/FoodEntry';
import DailyNutrition, { IMealNutrition } from '../models/DailyNutrition';
import FoodDatabase from '../models/FoodDatabase';
import { AuthRequest } from '../middleware/auth';

// Helper function to get user ID (with fallback for development)
const getUserId = (req: AuthRequest): string => {
    return req.user?._id?.toString() || '507f1f77bcf86cd799439011'; // Default ObjectId for development
};

// @desc    Create a new food entry
// @route   POST /api/food-diary/entries
// @access  Private
export const createFoodEntry = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const {
            name,
            carbohydrates,
            calories,
            protein,
            fat,
            fiber,
            glycemicIndex,
            category,
            portion,
            portionSize,
            unit,
            mealType,
            customMealTimeId,
            timestamp,
            notes,
            brand,
            isCustom = false
        } = req.body;

        // Validate required fields
        if (!name || !carbohydrates || !calories || !protein || !fat || !fiber ||
            !glycemicIndex || !category || !portion || !portionSize || !unit || !mealType) {
            res.status(400).json({
                success: false,
                message: 'All required nutritional fields must be provided'
            });
            return;
        }

        // Validate custom meal time reference if mealType is 'custom'
        if (mealType === 'custom' && !customMealTimeId) {
            res.status(400).json({
                success: false,
                message: 'Custom meal time ID is required when meal type is custom'
            });
            return;
        }

        const userId = getUserId(req);

        const foodEntry = await FoodEntry.create({
            userId: userId,
            name,
            carbohydrates,
            calories,
            protein,
            fat,
            fiber,
            glycemicIndex,
            category,
            portion,
            portionSize,
            unit,
            mealType,
            customMealTimeId,
            timestamp: timestamp || new Date(),
            notes: notes?.trim(),
            brand: brand?.trim(),
            isCustom
        });

        // Update daily nutrition summary
        await updateDailyNutrition(userId.toString(), foodEntry.timestamp);

        res.status(201).json({
            success: true,
            message: 'Food entry created successfully',
            data: foodEntry
        });
    } catch (error: any) {
        console.error('Error creating food entry:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get food entries for a specific date
// @route   GET /api/food-diary/entries
// @access  Private
export const getFoodEntries = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { date, mealType, startDate, endDate } = req.query;

        const userId = getUserId(req);
        let query: any = { userId: userId };

        if (date) {
            const targetDate = new Date(date as string);
            const startOfDay = new Date(targetDate);
            startOfDay.setHours(0, 0, 0, 0);
            const endOfDay = new Date(targetDate);
            endOfDay.setHours(23, 59, 59, 999);
            
            query.timestamp = { $gte: startOfDay, $lte: endOfDay };
        } else if (startDate && endDate) {
            query.timestamp = { 
                $gte: new Date(startDate as string), 
                $lte: new Date(endDate as string) 
            };
        }

        if (mealType) {
            query.mealType = mealType;
        }

        const foodEntries = await FoodEntry.find(query)
            .sort({ timestamp: -1 })
            .limit(100);

        res.json({
            success: true,
            count: foodEntries.length,
            data: foodEntries
        });
    } catch (error: any) {
        console.error('Error fetching food entries:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Update a food entry
// @route   PUT /api/food-diary/entries/:id
// @access  Private
export const updateFoodEntry = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const foodEntry = await FoodEntry.findOne({
            _id: req.params.id,
            userId: req.user?._id
        });

        if (!foodEntry) {
            res.status(404).json({
                success: false,
                message: 'Food entry not found'
            });
            return;
        }

        const originalTimestamp = foodEntry.timestamp;
        
        // Update the food entry
        Object.assign(foodEntry, req.body);
        await foodEntry.save();

        // Update daily nutrition for both old and new dates if timestamp changed
        await updateDailyNutrition(req.user?._id?.toString() || '', originalTimestamp);
        if (foodEntry.timestamp.toDateString() !== originalTimestamp.toDateString()) {
            await updateDailyNutrition(req.user?._id?.toString() || '', foodEntry.timestamp);
        }

        res.json({
            success: true,
            message: 'Food entry updated successfully',
            data: foodEntry
        });
    } catch (error: any) {
        console.error('Error updating food entry:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Delete a food entry
// @route   DELETE /api/food-diary/entries/:id
// @access  Private
export const deleteFoodEntry = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const foodEntry = await FoodEntry.findOne({
            _id: req.params.id,
            userId: req.user?._id
        });

        if (!foodEntry) {
            res.status(404).json({
                success: false,
                message: 'Food entry not found'
            });
            return;
        }

        const entryTimestamp = foodEntry.timestamp;
        await FoodEntry.deleteOne({ _id: req.params.id });

        // Update daily nutrition summary
        await updateDailyNutrition(req.user?._id?.toString() || '', entryTimestamp);

        res.json({
            success: true,
            message: 'Food entry deleted successfully'
        });
    } catch (error: any) {
        console.error('Error deleting food entry:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get daily nutrition summary
// @route   GET /api/food-diary/daily-nutrition
// @access  Private
export const getDailyNutrition = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { date } = req.query;
        const targetDate = date ? new Date(date as string) : new Date();
        
        // Normalize date to start of day
        const normalizedDate = new Date(targetDate);
        normalizedDate.setHours(0, 0, 0, 0);

        let dailyNutrition = await DailyNutrition.findOne({
            userId: req.user?._id,
            date: normalizedDate
        });

        if (!dailyNutrition) {
            // Create new daily nutrition record with default goals
            const defaultGoals = (DailyNutrition as any).getDefaultGoals();
            dailyNutrition = await DailyNutrition.create({
                userId: req.user?._id,
                date: normalizedDate,
                goals: defaultGoals,
                mealBreakdown: new Map()
            });
        }

        // Update with latest food entries
        await updateDailyNutrition(req.user?._id?.toString() || '', targetDate);

        // Fetch updated record
        dailyNutrition = await DailyNutrition.findOne({
            userId: req.user?._id,
            date: normalizedDate
        });

        res.json({
            success: true,
            data: dailyNutrition
        });
    } catch (error: any) {
        console.error('Error fetching daily nutrition:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Update nutrition goals
// @route   PUT /api/food-diary/nutrition-goals
// @access  Private
export const updateNutritionGoals = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { date } = req.query;
        const targetDate = date ? new Date(date as string) : new Date();
        
        // Normalize date to start of day
        const normalizedDate = new Date(targetDate);
        normalizedDate.setHours(0, 0, 0, 0);

        let dailyNutrition = await DailyNutrition.findOne({
            userId: req.user?._id,
            date: normalizedDate
        });

        if (!dailyNutrition) {
            // Create new record if it doesn't exist
            dailyNutrition = new DailyNutrition({
                userId: req.user?._id,
                date: normalizedDate,
                goals: req.body,
                mealBreakdown: new Map()
            });
        } else {
            dailyNutrition.goals = req.body;
        }

        await dailyNutrition.save();

        res.json({
            success: true,
            message: 'Nutrition goals updated successfully',
            data: dailyNutrition
        });
    } catch (error: any) {
        console.error('Error updating nutrition goals:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// Helper function to update daily nutrition summary
async function updateDailyNutrition(userId: string, date: Date): Promise<void> {
    try {
        // Normalize date to start of day
        const normalizedDate = new Date(date);
        normalizedDate.setHours(0, 0, 0, 0);

        const endOfDay = new Date(normalizedDate);
        endOfDay.setHours(23, 59, 59, 999);

        // Get all food entries for the day
        const foodEntries = await FoodEntry.find({
            userId,
            timestamp: { $gte: normalizedDate, $lte: endOfDay }
        });

        // Calculate totals and meal breakdown
        let totalCalories = 0;
        let totalCarbohydrates = 0;
        let totalProtein = 0;
        let totalFat = 0;
        let totalFiber = 0;

        const mealBreakdown = new Map<MealType, IMealNutrition>();

        // Initialize meal breakdown
        Object.values(MealType).forEach(mealType => {
            mealBreakdown.set(mealType, {
                mealType,
                totalCarbs: 0,
                totalCalories: 0,
                totalProtein: 0,
                totalFat: 0,
                totalFiber: 0,
                entryCount: 0,
                averageGlycemicIndex: 0,
                bloodSugarImpactScore: 0
            });
        });

        // Calculate nutrition values
        foodEntries.forEach(entry => {
            totalCalories += entry.calories;
            totalCarbohydrates += entry.carbohydrates;
            totalProtein += entry.protein;
            totalFat += entry.fat;
            totalFiber += entry.fiber;

            const mealNutrition = mealBreakdown.get(entry.mealType)!;
            mealNutrition.totalCalories += entry.calories;
            mealNutrition.totalCarbs += entry.carbohydrates;
            mealNutrition.totalProtein += entry.protein;
            mealNutrition.totalFat += entry.fat;
            mealNutrition.totalFiber += entry.fiber;
            mealNutrition.entryCount += 1;
        });

        // Calculate adherence score (simplified)
        const defaultGoals = (DailyNutrition as any).getDefaultGoals();
        let adherenceScore = 100;
        
        // Find or create daily nutrition record
        let dailyNutrition = await DailyNutrition.findOne({
            userId,
            date: normalizedDate
        });

        if (!dailyNutrition) {
            dailyNutrition = new DailyNutrition({
                userId,
                date: normalizedDate,
                goals: defaultGoals,
                mealBreakdown: new Map()
            });
        }

        // Update values
        dailyNutrition.totalCalories = totalCalories;
        dailyNutrition.totalCarbohydrates = totalCarbohydrates;
        dailyNutrition.totalProtein = totalProtein;
        dailyNutrition.totalFat = totalFat;
        dailyNutrition.totalFiber = totalFiber;
        dailyNutrition.mealBreakdown = mealBreakdown;
        dailyNutrition.adherenceScore = adherenceScore;

        await dailyNutrition.save();
    } catch (error) {
        console.error('Error updating daily nutrition:', error);
    }
}

// @desc    Search food database
// @route   GET /api/food-diary/search
// @access  Private
export const searchFoodDatabase = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { query, category, limit = 50 } = req.query;

        if (!query || typeof query !== 'string' || query.trim().length < 2) {
            res.status(400).json({
                success: false,
                message: 'Search query must be at least 2 characters long'
            });
            return;
        }

        const searchResults = await (FoodDatabase as any).searchFoods(
            query.trim(),
            category as FoodCategory,
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: searchResults.length,
            data: searchResults
        });
    } catch (error: any) {
        console.error('Error searching food database:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Search food database with multilingual support
// @route   GET /api/food-diary/search/multilingual
// @access  Private
export const searchFoodDatabaseMultilingual = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { query, language, category, limit = 50 } = req.query;

        if (!query || typeof query !== 'string' || query.trim().length < 2) {
            res.status(400).json({
                success: false,
                message: 'Search query must be at least 2 characters long'
            });
            return;
        }

        // Validate language parameter
        const validLanguages = ['en', 'zu', 'af', 'st', 'xh', 'nso', 'tn', 'ss', 've', 'ts', 'nr'];
        if (language && !validLanguages.includes(language as string)) {
            res.status(400).json({
                success: false,
                message: `Invalid language. Supported languages: ${validLanguages.join(', ')}`
            });
            return;
        }

        const searchResults = await (FoodDatabase as any).searchFoodsMultilingual(
            query.trim(),
            language as string,
            category as FoodCategory,
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: searchResults.length,
            data: searchResults,
            searchLanguage: language || 'all'
        });
    } catch (error: any) {
        console.error('Error searching food database (multilingual):', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get foods by origin (traditional_sa, international, local_adaptation)
// @route   GET /api/food-diary/foods/origin/:origin
// @access  Private
export const getFoodsByOrigin = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { origin } = req.params;
        const { limit = 100 } = req.query;

        const validOrigins = ['traditional_sa', 'international', 'local_adaptation'];
        if (!validOrigins.includes(origin)) {
            res.status(400).json({
                success: false,
                message: `Invalid origin. Valid options: ${validOrigins.join(', ')}`
            });
            return;
        }

        const foods = await (FoodDatabase as any).getFoodsByOrigin(
            origin,
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: foods.length,
            data: foods,
            origin
        });
    } catch (error: any) {
        console.error('Error getting foods by origin:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get foods by dietary tags
// @route   GET /api/food-diary/foods/dietary
// @access  Private
export const getFoodsByDietaryTags = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { tags, limit = 100 } = req.query;

        if (!tags || typeof tags !== 'string') {
            res.status(400).json({
                success: false,
                message: 'Dietary tags parameter is required'
            });
            return;
        }

        const tagArray = tags.split(',').map(tag => tag.trim());
        const validTags = ['halal', 'kosher', 'vegan', 'vegetarian', 'gluten-free', 'dairy-free', 'nut-free', 'low-sodium', 'high-protein', 'low-carb', 'keto-friendly'];

        const invalidTags = tagArray.filter(tag => !validTags.includes(tag));
        if (invalidTags.length > 0) {
            res.status(400).json({
                success: false,
                message: `Invalid dietary tags: ${invalidTags.join(', ')}. Valid options: ${validTags.join(', ')}`
            });
            return;
        }

        const foods = await (FoodDatabase as any).getFoodsByDietaryTags(
            tagArray,
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: foods.length,
            data: foods,
            requestedTags: tagArray
        });
    } catch (error: any) {
        console.error('Error getting foods by dietary tags:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Advanced nutrition search
// @route   GET /api/food-diary/search/nutrition
// @access  Private
export const searchByNutrition = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const {
            maxCarbs,
            minProtein,
            maxCalories,
            glycemicIndex,
            category,
            limit = 50
        } = req.query;

        const criteria: any = {};

        if (maxCarbs) {
            const maxCarbsNum = parseFloat(maxCarbs as string);
            if (isNaN(maxCarbsNum) || maxCarbsNum < 0) {
                res.status(400).json({
                    success: false,
                    message: 'maxCarbs must be a valid positive number'
                });
                return;
            }
            criteria.maxCarbs = maxCarbsNum;
        }

        if (minProtein) {
            const minProteinNum = parseFloat(minProtein as string);
            if (isNaN(minProteinNum) || minProteinNum < 0) {
                res.status(400).json({
                    success: false,
                    message: 'minProtein must be a valid positive number'
                });
                return;
            }
            criteria.minProtein = minProteinNum;
        }

        if (maxCalories) {
            const maxCaloriesNum = parseFloat(maxCalories as string);
            if (isNaN(maxCaloriesNum) || maxCaloriesNum < 0) {
                res.status(400).json({
                    success: false,
                    message: 'maxCalories must be a valid positive number'
                });
                return;
            }
            criteria.maxCalories = maxCaloriesNum;
        }

        if (glycemicIndex) {
            const validGI = ['low', 'medium', 'high'];
            if (!validGI.includes(glycemicIndex as string)) {
                res.status(400).json({
                    success: false,
                    message: `Invalid glycemicIndex. Valid options: ${validGI.join(', ')}`
                });
                return;
            }
            criteria.glycemicIndex = glycemicIndex;
        }

        if (category) {
            criteria.category = category;
        }

        const foods = await (FoodDatabase as any).searchByNutrition(
            criteria,
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: foods.length,
            data: foods,
            searchCriteria: criteria
        });
    } catch (error: any) {
        console.error('Error searching by nutrition:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get foods by category
// @route   GET /api/food-diary/foods/category/:category
// @access  Private
export const getFoodsByCategory = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { category } = req.params;
        const { limit = 100 } = req.query;

        if (!Object.values(FoodCategory).includes(category as FoodCategory)) {
            res.status(400).json({
                success: false,
                message: 'Invalid food category'
            });
            return;
        }

        const foods = await (FoodDatabase as any).getFoodsByCategory(
            category as FoodCategory,
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: foods.length,
            data: foods
        });
    } catch (error: any) {
        console.error('Error fetching foods by category:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get diabetes-friendly foods
// @route   GET /api/food-diary/foods/diabetes-friendly
// @access  Private
export const getDiabetesFriendlyFoods = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { limit = 50 } = req.query;

        const foods = await (FoodDatabase as any).getDiabetesFriendlyFoods(
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: foods.length,
            data: foods
        });
    } catch (error: any) {
        console.error('Error fetching diabetes-friendly foods:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get traditional South African foods
// @route   GET /api/food-diary/foods/traditional-sa
// @access  Private
export const getTraditionalSAFoods = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { limit = 100 } = req.query;

        const foods = await (FoodDatabase as any).getTraditionalSAFoods(
            parseInt(limit as string)
        );

        res.json({
            success: true,
            count: foods.length,
            data: foods
        });
    } catch (error: any) {
        console.error('Error fetching traditional SA foods:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};

// @desc    Get nutrition analytics for date range
// @route   GET /api/food-diary/analytics
// @access  Private
export const getNutritionAnalytics = async (req: AuthRequest, res: Response): Promise<void> => {
    try {
        const { startDate, endDate, period = '7' } = req.query;

        let start: Date, end: Date;

        if (startDate && endDate) {
            start = new Date(startDate as string);
            end = new Date(endDate as string);
        } else {
            end = new Date();
            start = new Date();
            start.setDate(end.getDate() - parseInt(period as string));
        }

        // Normalize dates
        start.setHours(0, 0, 0, 0);
        end.setHours(23, 59, 59, 999);

        const dailyNutritionRecords = await (DailyNutrition as any).findByDateRange(
            req.user?._id?.toString() || '',
            start,
            end
        );

        // Calculate analytics
        const analytics = {
            period: {
                startDate: start,
                endDate: end,
                days: Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24))
            },
            averages: {
                calories: 0,
                carbohydrates: 0,
                protein: 0,
                fat: 0,
                fiber: 0,
                adherenceScore: 0
            },
            trends: {
                caloriesTrend: 'stable',
                carbsTrend: 'stable',
                adherenceTrend: 'stable'
            },
            mealPatterns: {
                breakfast: { avgCalories: 0, avgCarbs: 0 },
                lunch: { avgCalories: 0, avgCarbs: 0 },
                dinner: { avgCalories: 0, avgCarbs: 0 },
                snacks: { avgCalories: 0, avgCarbs: 0 }
            },
            goalAdherence: {
                caloriesWithinTarget: 0,
                carbsWithinTarget: 0,
                overallAdherence: 0
            }
        };

        if (dailyNutritionRecords.length > 0) {
            // Calculate averages
            const totals = dailyNutritionRecords.reduce((acc: any, record: any) => {
                acc.calories += record.totalCalories;
                acc.carbohydrates += record.totalCarbohydrates;
                acc.protein += record.totalProtein;
                acc.fat += record.totalFat;
                acc.fiber += record.totalFiber;
                acc.adherenceScore += record.adherenceScore;
                return acc;
            }, { calories: 0, carbohydrates: 0, protein: 0, fat: 0, fiber: 0, adherenceScore: 0 });

            const recordCount = dailyNutritionRecords.length;
            analytics.averages = {
                calories: Math.round(totals.calories / recordCount),
                carbohydrates: Math.round(totals.carbohydrates / recordCount),
                protein: Math.round(totals.protein / recordCount),
                fat: Math.round(totals.fat / recordCount),
                fiber: Math.round(totals.fiber / recordCount),
                adherenceScore: Math.round(totals.adherenceScore / recordCount)
            };
        }

        res.json({
            success: true,
            data: analytics
        });
    } catch (error: any) {
        console.error('Error fetching nutrition analytics:', error);
        res.status(500).json({
            success: false,
            message: 'Server error',
            error: error.message
        });
    }
};
