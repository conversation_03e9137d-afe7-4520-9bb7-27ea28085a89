import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/food_entry.dart';
import '../models/food_recognition_result.dart';

/// Service that implements learning algorithm to improve recognition accuracy
class LearningAlgorithmService {
  static const String _userCorrectionsKey = 'user_corrections';
  static const String _userPreferencesKey = 'user_preferences';
  static const String _accuracyMetricsKey = 'accuracy_metrics';

  /// Record user correction for learning
  static Future<void> recordUserCorrection({
    required String originalPrediction,
    required String userCorrection,
    required Uint8List imageBytes,
    required double originalConfidence,
    required MealType mealType,
  }) async {
    try {
      debugPrint(
        '📚 Recording user correction: $originalPrediction -> $userCorrection',
      );

      final corrections = await _getUserCorrections();

      // Create correction entry
      final correction = UserCorrection(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        originalPrediction: originalPrediction,
        userCorrection: userCorrection,
        timestamp: DateTime.now(),
        originalConfidence: originalConfidence,
        mealType: mealType,
        imageFeatures: await _extractImageFeatures(imageBytes),
      );

      corrections.add(correction);

      // Keep only last 1000 corrections to manage storage
      if (corrections.length > 1000) {
        corrections.removeRange(0, corrections.length - 1000);
      }

      // Save corrections
      await _saveUserCorrections(corrections);

      // Update recognition patterns
      await _updateRecognitionPatterns(correction);

      // Update user preferences
      await _updateUserPreferences(correction);

      // Update accuracy metrics
      await _updateAccuracyMetrics(correction);

      debugPrint('✅ User correction recorded successfully');
    } catch (e) {
      debugPrint('💥 Error recording user correction: $e');
    }
  }

  /// Get improved prediction based on learning
  static Future<FoodRecognitionResult?> getImprovedPrediction(
    String originalPrediction,
    double originalConfidence,
    Uint8List imageBytes,
    MealType mealType,
  ) async {
    try {
      debugPrint('🧠 Getting improved prediction for: $originalPrediction');

      // Extract image features
      final imageFeatures = await _extractImageFeatures(imageBytes);

      // Check for similar corrections
      final similarCorrections = await _findSimilarCorrections(
        originalPrediction,
        imageFeatures,
        mealType,
      );

      if (similarCorrections.isNotEmpty) {
        // Calculate improved prediction
        final improvedPrediction = _calculateImprovedPrediction(
          originalPrediction,
          originalConfidence,
          similarCorrections,
        );

        if (improvedPrediction != null) {
          debugPrint('✅ Improved prediction: ${improvedPrediction.name}');
          return FoodRecognitionResult(
            detectedFoods: [improvedPrediction],
            confidence:
                improvedPrediction.notes?.contains('confidence:') == true
                    ? double.tryParse(
                          improvedPrediction.notes!
                              .split('confidence:')[1]
                              .split(',')[0],
                        ) ??
                        originalConfidence
                    : originalConfidence,
            analysisTime: DateTime.now(),
            isRealtime: false,
          );
        }
      }

      // Check user preferences for this meal type
      final preferences = await _getUserPreferences();
      final mealPreferences = preferences[mealType] ?? [];

      // Boost confidence if food matches user preferences
      if (mealPreferences.any(
        (pref) => pref.foodName.toLowerCase().contains(
          originalPrediction.toLowerCase(),
        ),
      )) {
        debugPrint('📈 Boosting confidence based on user preferences');
        return FoodRecognitionResult(
          detectedFoods: [
            FoodEntry(
              id: 'improved_${DateTime.now().millisecondsSinceEpoch}',
              name: originalPrediction,
              carbohydrates: 20.0,
              calories: 150.0,
              protein: 5.0,
              fat: 3.0,
              fiber: 2.0,
              glycemicIndex: GlycemicIndex.medium,
              category: FoodCategory.other,
              portion: '100g',
              portionSize: 100.0,
              unit: 'g',
              mealType: mealType,
              timestamp: DateTime.now(),
              notes:
                  'confidence:${(originalConfidence + 0.1).clamp(0.0, 1.0)},improved_by_learning',
              isCustom: true,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
            ),
          ],
          confidence: (originalConfidence + 0.1).clamp(0.0, 1.0),
          analysisTime: DateTime.now(),
          isRealtime: false,
        );
      }

      return null;
    } catch (e) {
      debugPrint('💥 Error getting improved prediction: $e');
      return null;
    }
  }

  /// Get user's food preferences for suggestions
  static Future<List<String>> getUserFoodSuggestions(MealType mealType) async {
    try {
      final preferences = await _getUserPreferences();
      final mealPreferences = preferences[mealType] ?? [];

      // Sort by frequency and return top suggestions
      mealPreferences.sort((a, b) => b.frequency.compareTo(a.frequency));

      return mealPreferences.take(10).map((pref) => pref.foodName).toList();
    } catch (e) {
      debugPrint('💥 Error getting user suggestions: $e');
      return [];
    }
  }

  /// Get accuracy metrics for analytics
  static Future<AccuracyMetrics> getAccuracyMetrics() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final metricsJson = prefs.getString(_accuracyMetricsKey);

      if (metricsJson != null) {
        return AccuracyMetrics.fromJson(jsonDecode(metricsJson));
      }

      return AccuracyMetrics.empty();
    } catch (e) {
      debugPrint('💥 Error getting accuracy metrics: $e');
      return AccuracyMetrics.empty();
    }
  }

  /// Extract simple image features for pattern matching
  static Future<ImageFeatures> _extractImageFeatures(
    Uint8List imageBytes,
  ) async {
    // Simplified feature extraction
    // In a real implementation, this would use computer vision

    int redSum = 0, greenSum = 0, blueSum = 0;
    int pixelCount = imageBytes.length ~/ 3;

    for (int i = 0; i < imageBytes.length - 2; i += 3) {
      redSum += imageBytes[i];
      greenSum += imageBytes[i + 1];
      blueSum += imageBytes[i + 2];
    }

    return ImageFeatures(
      avgRed: redSum / pixelCount,
      avgGreen: greenSum / pixelCount,
      avgBlue: blueSum / pixelCount,
      brightness: (redSum + greenSum + blueSum) / (pixelCount * 3),
      size: imageBytes.length,
    );
  }

  /// Find similar corrections based on image features and context
  static Future<List<UserCorrection>> _findSimilarCorrections(
    String prediction,
    ImageFeatures imageFeatures,
    MealType mealType,
  ) async {
    final corrections = await _getUserCorrections();

    return corrections.where((correction) {
      // Check if original prediction is similar
      final predictionSimilarity = _calculateStringSimilarity(
        prediction.toLowerCase(),
        correction.originalPrediction.toLowerCase(),
      );

      // Check if image features are similar
      final featureSimilarity = _calculateFeatureSimilarity(
        imageFeatures,
        correction.imageFeatures,
      );

      // Check if meal type matches
      final mealTypeMatch = correction.mealType == mealType;

      return predictionSimilarity > 0.7 &&
          featureSimilarity > 0.6 &&
          mealTypeMatch;
    }).toList();
  }

  /// Calculate improved prediction from similar corrections
  static FoodEntry? _calculateImprovedPrediction(
    String originalPrediction,
    double originalConfidence,
    List<UserCorrection> similarCorrections,
  ) {
    if (similarCorrections.isEmpty) return null;

    // Find most common correction
    final correctionCounts = <String, int>{};
    for (final correction in similarCorrections) {
      correctionCounts[correction.userCorrection] =
          (correctionCounts[correction.userCorrection] ?? 0) + 1;
    }

    final mostCommonCorrection =
        correctionCounts.entries
            .reduce((a, b) => a.value > b.value ? a : b)
            .key;

    // Calculate improved confidence
    final correctionFrequency =
        correctionCounts[mostCommonCorrection]! / similarCorrections.length;
    final improvedConfidence = (originalConfidence + correctionFrequency * 0.3)
        .clamp(0.0, 1.0);

    return FoodEntry(
      id: 'learned_${DateTime.now().millisecondsSinceEpoch}',
      name: mostCommonCorrection,
      carbohydrates: 20.0,
      calories: 150.0,
      protein: 5.0,
      fat: 3.0,
      fiber: 2.0,
      glycemicIndex: GlycemicIndex.medium,
      category: FoodCategory.other,
      portion: '100g',
      portionSize: 100.0,
      unit: 'g',
      mealType: MealType.breakfast,
      timestamp: DateTime.now(),
      notes: 'confidence:$improvedConfidence,learned_from_corrections',
      isCustom: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  /// Calculate string similarity (simplified Levenshtein distance)
  static double _calculateStringSimilarity(String a, String b) {
    if (a == b) return 1.0;
    if (a.isEmpty || b.isEmpty) return 0.0;

    final longer = a.length > b.length ? a : b;
    final shorter = a.length > b.length ? b : a;

    if (longer.isEmpty) return 1.0;

    final editDistance = _levenshteinDistance(longer, shorter);
    return (longer.length - editDistance) / longer.length;
  }

  /// Calculate Levenshtein distance
  static int _levenshteinDistance(String a, String b) {
    final matrix = List.generate(
      a.length + 1,
      (i) => List.generate(b.length + 1, (j) => 0),
    );

    for (int i = 0; i <= a.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= b.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= a.length; i++) {
      for (int j = 1; j <= b.length; j++) {
        final cost = a[i - 1] == b[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[a.length][b.length];
  }

  /// Calculate feature similarity
  static double _calculateFeatureSimilarity(ImageFeatures a, ImageFeatures b) {
    final redDiff = (a.avgRed - b.avgRed).abs() / 255.0;
    final greenDiff = (a.avgGreen - b.avgGreen).abs() / 255.0;
    final blueDiff = (a.avgBlue - b.avgBlue).abs() / 255.0;
    final brightnessDiff = (a.brightness - b.brightness).abs() / 255.0;

    final colorSimilarity = 1.0 - ((redDiff + greenDiff + blueDiff) / 3.0);
    final brightnessSimilarity = 1.0 - brightnessDiff;

    return (colorSimilarity + brightnessSimilarity) / 2.0;
  }

  // Helper methods for data persistence
  static Future<List<UserCorrection>> _getUserCorrections() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final correctionsJson = prefs.getString(_userCorrectionsKey);

      if (correctionsJson != null) {
        final List<dynamic> correctionsList = jsonDecode(correctionsJson);
        return correctionsList
            .map((json) => UserCorrection.fromJson(json))
            .toList();
      }

      return [];
    } catch (e) {
      debugPrint('💥 Error getting user corrections: $e');
      return [];
    }
  }

  static Future<void> _saveUserCorrections(
    List<UserCorrection> corrections,
  ) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final correctionsJson = jsonEncode(
        corrections.map((c) => c.toJson()).toList(),
      );
      await prefs.setString(_userCorrectionsKey, correctionsJson);
    } catch (e) {
      debugPrint('💥 Error saving user corrections: $e');
    }
  }

  static Future<void> _updateRecognitionPatterns(
    UserCorrection correction,
  ) async {
    // Update recognition patterns for future improvements
    // This would be more sophisticated in a real implementation
  }

  static Future<Map<MealType, List<UserPreference>>>
  _getUserPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = prefs.getString(_userPreferencesKey);

      if (preferencesJson != null) {
        final Map<String, dynamic> preferencesMap = jsonDecode(preferencesJson);
        final result = <MealType, List<UserPreference>>{};

        for (final entry in preferencesMap.entries) {
          final mealType = MealType.values.firstWhere(
            (type) => type.toString() == entry.key,
            orElse: () => MealType.breakfast,
          );
          final preferences =
              (entry.value as List)
                  .map((json) => UserPreference.fromJson(json))
                  .toList();
          result[mealType] = preferences;
        }

        return result;
      }

      return {};
    } catch (e) {
      debugPrint('💥 Error getting user preferences: $e');
      return {};
    }
  }

  static Future<void> _updateUserPreferences(UserCorrection correction) async {
    try {
      final preferences = await _getUserPreferences();
      final mealPreferences = preferences[correction.mealType] ?? [];

      // Find existing preference or create new one
      final existingIndex = mealPreferences.indexWhere(
        (pref) =>
            pref.foodName.toLowerCase() ==
            correction.userCorrection.toLowerCase(),
      );

      if (existingIndex >= 0) {
        // Update existing preference
        mealPreferences[existingIndex] = mealPreferences[existingIndex]
            .copyWith(
              frequency: mealPreferences[existingIndex].frequency + 1,
              lastUsed: DateTime.now(),
            );
      } else {
        // Add new preference
        mealPreferences.add(
          UserPreference(
            foodName: correction.userCorrection,
            frequency: 1,
            lastUsed: DateTime.now(),
          ),
        );
      }

      preferences[correction.mealType] = mealPreferences;

      // Save updated preferences
      final prefs = await SharedPreferences.getInstance();
      final preferencesJson = jsonEncode(
        preferences.map(
          (key, value) => MapEntry(
            key.toString(),
            value.map((pref) => pref.toJson()).toList(),
          ),
        ),
      );
      await prefs.setString(_userPreferencesKey, preferencesJson);
    } catch (e) {
      debugPrint('💥 Error updating user preferences: $e');
    }
  }

  static Future<void> _updateAccuracyMetrics(UserCorrection correction) async {
    try {
      final metrics = await getAccuracyMetrics();
      final updatedMetrics = metrics.copyWith(
        totalPredictions: metrics.totalPredictions + 1,
        correctPredictions:
            correction.originalPrediction.toLowerCase() ==
                    correction.userCorrection.toLowerCase()
                ? metrics.correctPredictions + 1
                : metrics.correctPredictions,
        lastUpdated: DateTime.now(),
      );

      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(
        _accuracyMetricsKey,
        jsonEncode(updatedMetrics.toJson()),
      );
    } catch (e) {
      debugPrint('💥 Error updating accuracy metrics: $e');
    }
  }
}

// Data classes for learning algorithm
class UserCorrection {
  final String id;
  final String originalPrediction;
  final String userCorrection;
  final DateTime timestamp;
  final double originalConfidence;
  final MealType mealType;
  final ImageFeatures imageFeatures;

  const UserCorrection({
    required this.id,
    required this.originalPrediction,
    required this.userCorrection,
    required this.timestamp,
    required this.originalConfidence,
    required this.mealType,
    required this.imageFeatures,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'originalPrediction': originalPrediction,
    'userCorrection': userCorrection,
    'timestamp': timestamp.toIso8601String(),
    'originalConfidence': originalConfidence,
    'mealType': mealType.toString(),
    'imageFeatures': imageFeatures.toJson(),
  };

  factory UserCorrection.fromJson(Map<String, dynamic> json) => UserCorrection(
    id: json['id'],
    originalPrediction: json['originalPrediction'],
    userCorrection: json['userCorrection'],
    timestamp: DateTime.parse(json['timestamp']),
    originalConfidence: json['originalConfidence']?.toDouble() ?? 0.0,
    mealType: MealType.values.firstWhere(
      (type) => type.toString() == json['mealType'],
      orElse: () => MealType.breakfast,
    ),
    imageFeatures: ImageFeatures.fromJson(json['imageFeatures']),
  );
}

class ImageFeatures {
  final double avgRed;
  final double avgGreen;
  final double avgBlue;
  final double brightness;
  final int size;

  const ImageFeatures({
    required this.avgRed,
    required this.avgGreen,
    required this.avgBlue,
    required this.brightness,
    required this.size,
  });

  Map<String, dynamic> toJson() => {
    'avgRed': avgRed,
    'avgGreen': avgGreen,
    'avgBlue': avgBlue,
    'brightness': brightness,
    'size': size,
  };

  factory ImageFeatures.fromJson(Map<String, dynamic> json) => ImageFeatures(
    avgRed: json['avgRed']?.toDouble() ?? 0.0,
    avgGreen: json['avgGreen']?.toDouble() ?? 0.0,
    avgBlue: json['avgBlue']?.toDouble() ?? 0.0,
    brightness: json['brightness']?.toDouble() ?? 0.0,
    size: json['size'] ?? 0,
  );
}

class UserPreference {
  final String foodName;
  final int frequency;
  final DateTime lastUsed;

  const UserPreference({
    required this.foodName,
    required this.frequency,
    required this.lastUsed,
  });

  UserPreference copyWith({
    String? foodName,
    int? frequency,
    DateTime? lastUsed,
  }) => UserPreference(
    foodName: foodName ?? this.foodName,
    frequency: frequency ?? this.frequency,
    lastUsed: lastUsed ?? this.lastUsed,
  );

  Map<String, dynamic> toJson() => {
    'foodName': foodName,
    'frequency': frequency,
    'lastUsed': lastUsed.toIso8601String(),
  };

  factory UserPreference.fromJson(Map<String, dynamic> json) => UserPreference(
    foodName: json['foodName'],
    frequency: json['frequency'],
    lastUsed: DateTime.parse(json['lastUsed']),
  );
}

class AccuracyMetrics {
  final int totalPredictions;
  final int correctPredictions;
  final DateTime lastUpdated;

  const AccuracyMetrics({
    required this.totalPredictions,
    required this.correctPredictions,
    required this.lastUpdated,
  });

  double get accuracy =>
      totalPredictions > 0 ? correctPredictions / totalPredictions : 0.0;

  AccuracyMetrics copyWith({
    int? totalPredictions,
    int? correctPredictions,
    DateTime? lastUpdated,
  }) => AccuracyMetrics(
    totalPredictions: totalPredictions ?? this.totalPredictions,
    correctPredictions: correctPredictions ?? this.correctPredictions,
    lastUpdated: lastUpdated ?? this.lastUpdated,
  );

  Map<String, dynamic> toJson() => {
    'totalPredictions': totalPredictions,
    'correctPredictions': correctPredictions,
    'lastUpdated': lastUpdated.toIso8601String(),
  };

  factory AccuracyMetrics.fromJson(Map<String, dynamic> json) =>
      AccuracyMetrics(
        totalPredictions: json['totalPredictions'],
        correctPredictions: json['correctPredictions'],
        lastUpdated: DateTime.parse(json['lastUpdated']),
      );

  factory AccuracyMetrics.empty() => AccuracyMetrics(
    totalPredictions: 0,
    correctPredictions: 0,
    lastUpdated: DateTime.now(),
  );
}
