# Testing Your Native Splash Screen

## Quick Test Commands:

### For Android:
```bash
flutter run --debug
# or for release mode (better splash screen performance):
flutter run --release
```

### For iOS:
```bash
flutter run --debug
# or for release mode:
flutter run --release
```

## What to expect:
1. **App launch**: You'll see your GlucoMonitor logo centered on a green background
2. **Dark mode**: If your device is in dark mode, you'll see a darker green background
3. **Android 12+**: Modern splash screen with smooth animations
4. **Full screen**: No status bar during splash for immersive experience

## Customization Options:
If you want to modify the splash screen later, edit the `flutter_native_splash` section in `pubspec.yaml` and run:
```bash
dart run flutter_native_splash:create
```

## Troubleshooting:
- If splash doesn't appear, try `flutter clean` then `flutter pub get`
- For Android, ensure you're testing on a physical device or emulator
- For iOS, test on a physical device for best results
